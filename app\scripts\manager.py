import os
from werkzeug.utils import secure_filename
import shutil
from .metadata import MetadataManager
from .validator import ScriptValidator
import traceback
import logging

class ScriptManager:
    """腳本管理器"""
    
    def __init__(self, base_dir):
        self.base_dir = base_dir
        self.scripts_dir = os.path.join(base_dir, 'data', 'scripts')
        self.output_dir = os.path.join(base_dir, 'data', 'outputs')
        self.metadata = MetadataManager(base_dir)
        self.validator = ScriptValidator()
        
        # 確保目錄存在
        for dir_path in [self.scripts_dir, self.output_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
    
    def list_scripts(self):
        """
        列出所有可用的腳本

        Returns:
            list: 腳本列表，每個腳本包含名稱、描述、標籤等信息
        """
        scripts_list = []
        
        try:
            # 獲取腳本目錄中的所有文件
            files = os.listdir(self.scripts_dir)
            logging.info(f'發現 {len(files)} 個文件')
            
            # 獲取元數據中的腳本
            metadata_scripts = self.metadata.list_scripts()
            logging.info(f'元數據中的腳本: {[s["name"] for s in metadata_scripts]}')
            
            # 遍歷文件，排除系統文件和目錄
            for filename in files:
                # 排除隱藏文件、非 .py 文件和目錄
                if filename.startswith('.') or not filename.endswith('.py') or os.path.isdir(os.path.join(self.scripts_dir, filename)):
                    continue
                    
                # 獲取文件信息
                file_path = os.path.join(self.scripts_dir, filename)
                file_stat = os.stat(file_path)
                file_size = file_stat.st_size
                modified_time = file_stat.st_mtime
                
                # 從元數據中獲取描述、標籤等信息
                description = ""
                tag = ""
                
                for script in metadata_scripts:
                    if script["name"] == filename:
                        description = script.get("description", "")
                        tags = script.get("tags", [])
                        tag = tags[0] if tags and len(tags) > 0 else ""
                        logging.info(f'腳本 {filename} 的標籤: {tags}')
                        logging.info(f'腳本 {filename} 的描述: {description}')
                        break
                
                # 創建腳本信息對象
                script_info = {
                    "name": filename,
                    "description": description,
                    "tag": tag,
                    "file_size": file_size,
                    "modified_at": modified_time
                }
                
                scripts_list.append(script_info)
                
        except Exception as e:
            logging.error(f'列出腳本時發生錯誤: {str(e)}')
        
        # 按修改時間排序，最新的排在前面
        scripts_list.sort(key=lambda x: x["modified_at"], reverse=True)
        
        return scripts_list
    
    def upload_script(self, file, description=None, author=None, tags=None):
        """上傳腳本文件"""
        if not file:
            print("錯誤：未收到文件")
            return {'status': 'error', 'message': '未收到文件'}
            
        try:
            if hasattr(file, 'filename'):
                filename = secure_filename(file.filename)
                print(f"處理上傳文件：{filename}（從文件對象獲取）")
            else:
                filename = os.path.basename(file.name)
                print(f"處理上傳文件：{filename}（從文件路徑獲取）")
            
            if not filename.endswith('.py'):
                print(f"錯誤：非Python文件 - {filename}")
                return {'status': 'error', 'message': '只允許上傳Python腳本文件'}
            
            file_path = os.path.join(self.scripts_dir, filename)
            temp_path = file_path + '.tmp'
            
            print(f"正在保存文件到臨時路徑：{temp_path}")
            
            # 複製文件內容
            with open(temp_path, 'wb') as dest:
                if hasattr(file, 'stream'):
                    print("從stream複製文件內容")
                    file.stream.seek(0)
                    shutil.copyfileobj(file.stream, dest)
                else:
                    print("從file對象複製文件內容")
                    file.seek(0)
                    shutil.copyfileobj(file, dest)
            
            # 檢查文件大小
            file_size = os.path.getsize(temp_path)
            print(f"臨時文件已保存，大小：{file_size} 字節")
            
            # 驗證腳本
            print(f"開始驗證腳本：{temp_path}")
            is_valid, validation_result = self.validator.validate_script(temp_path)
            print(f"驗證結果：{is_valid}，詳情：{validation_result}")
            
            # 移動到正確位置
            if os.path.exists(file_path):
                print(f"刪除已存在的文件：{file_path}")
                os.remove(file_path)
            
            print(f"將臨時文件移動到最終位置：{file_path}")
            os.rename(temp_path, file_path)
            print(f"文件移動成功")
            
            # 更新元數據
            print(f"更新元數據：description={description}, author={author}, tags={tags}")
            self.metadata.add_script(
                filename,
                description=description,
                author=author,
                tags=tags or []
            )
            print(f"元數據更新成功")
            
            return {
                'status': 'success',
                'message': '腳本上傳成功',
                'data': {
                    'name': filename,
                    'path': file_path,
                    'description': description or f'腳本 - {filename}',
                    'validation_result': validation_result
                }
            }
        except Exception as e:
            print(f"保存腳本時出錯: {str(e)}")
            print(f"錯誤詳情: {traceback.format_exc()}")
            if os.path.exists(temp_path):
                try:
                    print(f"刪除臨時文件：{temp_path}")
                    os.remove(temp_path)
                except Exception as cleanup_error:
                    print(f"清理臨時文件時出錯：{str(cleanup_error)}")
            
            return {'status': 'error', 'message': f'保存文件失敗: {str(e)}'}
    
    def delete_script(self, script_name):
        """刪除腳本"""
        if not script_name or script_name in {'__init__.py', 'manager.py', 'utils.py'}:
            return False, '不能刪除系統文件'
            
        try:
            script_path = os.path.join(self.scripts_dir, script_name)
            if not os.path.exists(script_path):
                return False, '腳本不存在'
                
            # 刪除腳本文件
            os.remove(script_path)
            
            # 刪除相關的輸出目錄
            output_path = os.path.join(self.output_dir, script_name.rsplit('.', 1)[0])
            if os.path.exists(output_path):
                shutil.rmtree(output_path)
            
            # 刪除元數據
            self.metadata.delete_script(script_name)
            
            return True, '腳本已刪除'
        except Exception as e:
            print(f"刪除腳本時出錯: {str(e)}")
            return False, '刪除腳本失敗'
    
    def get_script_info(self, script_name):
        """獲取腳本信息"""
        try:
            script_path = os.path.join(self.scripts_dir, script_name)
            if not os.path.exists(script_path):
                return None
                
            # 讀取腳本內容
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 獲取元數據
            meta = self.metadata.get_script_meta(script_name) or {}
            
            # 驗證腳本
            is_valid, validation_result = self.validator.validate_script(script_path)
                
            return {
                'name': script_name,
                'path': script_path,
                'size': os.path.getsize(script_path),
                'modified': os.path.getmtime(script_path),
                'content': content,
                'description': meta.get('description', f'腳本 - {script_name}'),
                'author': meta.get('author', 'system'),
                'tags': meta.get('tags', []),
                'validation_result': validation_result,
                'is_valid': is_valid
            }
        except Exception as e:
            print(f"讀取腳本信息時出錯: {str(e)}")
            return None

    def update_script_meta(self, script_name, description=None, tags=None):
        """
        更新腳本元數據

        Args:
            script_name (str): 腳本名稱
            description (str, optional): 腳本描述
            tags (list, optional): 腳本標籤列表

        Returns:
            tuple: (success, message) 表示操作是否成功以及相關消息
        """
        logging.info(f'更新腳本元數據: {script_name}')
        
        try:
            # 更新元數據
            self.metadata.update_script(
                script_name,
                description=description,
                tags=tags
            )
            logging.info(f'腳本 {script_name} 的元數據已更新')
            return True, "元數據更新成功"
        except KeyError:
            logging.error(f'找不到腳本: {script_name}')
            return False, "腳本不存在"
        except Exception as e:
            logging.error(f'更新腳本元數據時發生錯誤: {str(e)}')
            return False, f"更新元數據失敗: {str(e)}"
    
    def search_scripts(self, query):
        """搜索腳本"""
        try:
            results = []
            scripts = self.metadata.search_scripts(query)
            for name, meta in scripts.items():
                script_path = os.path.join(self.scripts_dir, name)
                if os.path.exists(script_path):
                    results.append({
                        'name': name,
                        'path': script_path,
                        'size': os.path.getsize(script_path),
                        'modified': os.path.getmtime(script_path),
                        'description': meta.get('description', f'腳本 - {name}'),
                        'author': meta.get('author', 'system'),
                        'tags': meta.get('tags', [])
                    })
            return results
        except Exception as e:
            print(f"搜索腳本時出錯: {str(e)}")
            return []

    def check_script_compatibility(self, script_name):
        """檢查腳本兼容性"""
        try:
            script_path = os.path.join(self.scripts_dir, script_name)
            if not os.path.exists(script_path):
                return False, {'error': '腳本不存在'}
            
            is_valid, validation_result = self.validator.validate_script(script_path)
            return is_valid, validation_result
        except Exception as e:
            return False, {'error': f'檢查兼容性時出錯: {str(e)}'}

    def get_requirements(self, script_name):
        """獲取腳本的依賴需求"""
        try:
            script_path = os.path.join(self.scripts_dir, script_name)
            if not os.path.exists(script_path):
                return None
            
            _, validation_result = self.validator.validate_script(script_path)
            if validation_result['required_packages']:
                installation_commands = self.validator.get_installation_commands(
                    validation_result['required_packages']
                )
                return {
                    'packages': validation_result['required_packages'],
                    'commands': installation_commands
                }
            return None
        except Exception as e:
            print(f"獲取依賴需求時出錯: {str(e)}")
            return None
