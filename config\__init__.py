# -*- coding: utf-8 -*-
"""
配置模塊
"""
import os

def get_config(config_name=None):
    """獲取當前環境配置"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    if config_name == 'production':
        from config.production import ProductionConfig
        return ProductionConfig
    elif config_name == 'testing':
        from config.testing import TestingConfig
        return TestingConfig
    else:
        from config.development import DevelopmentConfig
        return DevelopmentConfig

def validate_config(config):
    """驗證配置"""
    # 檢查必要的配置項
    required_config = [
        'SECRET_KEY',
        'SQLALCHEMY_DATABASE_URI',
        'UPLOAD_FOLDER',
        'OUTPUT_FOLDER',
    ]
    
    for item in required_config:
        if not hasattr(config, item):
            raise ValueError(f"缺少必要的配置項: {item}")
    
    # 檢查目錄是否可讀寫
    directories = [
        getattr(config, 'UPLOAD_FOLDER'),
        getattr(config, 'OUTPUT_FOLDER'),
        getattr(config, 'LOG_FOLDER', 'logs')
    ]
    
    for directory in directories:
        # 嘗試創建目錄
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
            except Exception as e:
                raise ValueError(f"無法創建目錄 {directory}: {str(e)}")
        
        # 檢查是否可寫
        if not os.access(directory, os.W_OK):
            raise ValueError(f"無法寫入目錄 {directory}") 