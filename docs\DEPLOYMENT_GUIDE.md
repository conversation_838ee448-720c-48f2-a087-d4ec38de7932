# 腳本管理系統部署指南

## 📋 系統要求

### 最低配置
- **操作系統**: Windows 10/11, Ubuntu 18.04+, CentOS 7+
- **Python版本**: 3.8+
- **記憶體**: 2GB RAM
- **磁碟空間**: 5GB
- **網絡**: 支持HTTP/HTTPS

### 推薦配置
- **操作系統**: Ubuntu 20.04 LTS
- **Python版本**: 3.11+
- **記憶體**: 8GB RAM
- **磁碟空間**: 50GB SSD
- **CPU**: 4核心以上

## 🚀 快速部署

### 1. 環境準備

```bash
# 克隆項目
git clone <repository-url>
cd script-management-system

# 創建虛擬環境
python -m venv venv

# 激活虛擬環境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安裝依賴
pip install -r requirements.txt
```

### 2. 配置設置

```bash
# 複製配置文件
cp config/config.example.py config/config.py

# 編輯配置文件
nano config/config.py
```

**關鍵配置項**:
```python
# 數據庫配置
DATABASE_URL = 'sqlite:///app.db'  # 開發環境
# DATABASE_URL = 'postgresql://user:pass@localhost/dbname'  # 生產環境

# 安全配置
SECRET_KEY = 'your-secret-key-here'
UPLOAD_FOLDER = '/path/to/uploads'
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# 日誌配置
LOG_LEVEL = 'INFO'
LOG_FILE = 'logs/app.log'
```

### 3. 數據庫初始化

```bash
# 初始化數據庫
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

### 4. 啟動應用

```bash
# 開發環境
python run.py

# 生產環境
gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

## 🐳 Docker部署

### 1. 使用Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=**************************************/scriptdb
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - db
      - redis

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=scriptdb
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web

volumes:
  postgres_data:
```

### 2. 構建和啟動

```bash
# 構建並啟動所有服務
docker-compose up -d

# 查看日誌
docker-compose logs -f web

# 停止服務
docker-compose down
```

## 🔧 生產環境配置

### 1. Nginx配置

```nginx
# nginx.conf
upstream app {
    server web:5000;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    client_max_body_size 20M;

    location / {
        proxy_pass http://app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 2. Systemd服務配置

```ini
# /etc/systemd/system/script-manager.service
[Unit]
Description=Script Management System
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/script-manager
Environment=PATH=/opt/script-manager/venv/bin
ExecStart=/opt/script-manager/venv/bin/gunicorn -w 4 -b 127.0.0.1:5000 run:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# 啟用和啟動服務
sudo systemctl enable script-manager
sudo systemctl start script-manager
sudo systemctl status script-manager
```

## 📊 監控和日誌

### 1. 日誌配置

```python
# config/logging.py
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/app.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'default',
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'default',
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['file', 'console']
    }
}
```

### 2. 健康檢查

```bash
# 創建健康檢查腳本
cat > health_check.sh << 'EOF'
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/health)
if [ $response -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $response)"
    exit 1
fi
EOF

chmod +x health_check.sh

# 添加到crontab進行定期檢查
echo "*/5 * * * * /path/to/health_check.sh" | crontab -
```

## 🔒 安全配置

### 1. 防火牆設置

```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL證書配置

```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 自動續期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### 3. 安全加固

```bash
# 創建專用用戶
sudo useradd -r -s /bin/false scriptmanager

# 設置文件權限
sudo chown -R scriptmanager:scriptmanager /opt/script-manager
sudo chmod -R 755 /opt/script-manager
sudo chmod 600 /opt/script-manager/config/config.py

# 限制上傳目錄權限
sudo chmod 750 /opt/script-manager/uploads
```

## 📈 性能優化

### 1. 數據庫優化

```sql
-- PostgreSQL索引優化
CREATE INDEX idx_scripts_name ON scripts(name);
CREATE INDEX idx_scripts_tags ON scripts(tags);
CREATE INDEX idx_schedules_status ON schedules(status);
CREATE INDEX idx_schedules_next_run ON schedules(next_run);
```

### 2. Redis緩存配置

```python
# config/cache.py
CACHE_CONFIG = {
    'CACHE_TYPE': 'redis',
    'CACHE_REDIS_URL': 'redis://localhost:6379/0',
    'CACHE_DEFAULT_TIMEOUT': 300
}
```

### 3. 應用優化

```python
# 啟用gzip壓縮
from flask_compress import Compress
Compress(app)

# 啟用緩存
from flask_caching import Cache
cache = Cache(app)
```

## 🔄 備份和恢復

### 1. 數據庫備份

```bash
# PostgreSQL備份腳本
#!/bin/bash
BACKUP_DIR="/backup/database"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U postgres scriptdb > $BACKUP_DIR/backup_$DATE.sql

# 保留最近7天的備份
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete
```

### 2. 文件備份

```bash
# 備份上傳文件和配置
#!/bin/bash
BACKUP_DIR="/backup/files"
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /opt/script-manager/uploads /opt/script-manager/config
```

### 3. 自動備份

```bash
# 添加到crontab
0 2 * * * /path/to/backup_database.sh
0 3 * * * /path/to/backup_files.sh
```

## 🚨 故障排除

### 常見問題

1. **應用無法啟動**
   ```bash
   # 檢查日誌
   tail -f logs/app.log
   
   # 檢查端口占用
   netstat -tlnp | grep :5000
   ```

2. **數據庫連接失敗**
   ```bash
   # 檢查數據庫狀態
   sudo systemctl status postgresql
   
   # 測試連接
   psql -h localhost -U postgres -d scriptdb
   ```

3. **文件上傳失敗**
   ```bash
   # 檢查目錄權限
   ls -la uploads/
   
   # 檢查磁碟空間
   df -h
   ```

### 性能問題診斷

```bash
# 檢查系統資源
top
htop
iotop

# 檢查應用性能
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:5000/

# 數據庫性能分析
EXPLAIN ANALYZE SELECT * FROM scripts WHERE tags LIKE '%test%';
```

---

## 📞 技術支持

### 聯繫方式
- **技術支持**: <EMAIL>
- **文檔更新**: <EMAIL>
- **緊急聯繫**: +1-234-567-8900

### 社區資源
- **GitHub**: https://github.com/your-org/script-manager
- **文檔站點**: https://docs.script-manager.com
- **問題追蹤**: https://github.com/your-org/script-manager/issues

---

**部署支持**: 如需技術支持，請聯繫系統管理員
**更新日期**: 2025-07-08
**版本**: v1.0
