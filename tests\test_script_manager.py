import unittest
import os
import tempfile
import shutil
from werkzeug.datastructures import FileStorage
from app.scripts.manager import ScriptManager
from app.scripts.validator import ScriptValidator

class TestScriptManager(unittest.TestCase):
    def setUp(self):
        """測試前的準備工作"""
        # 創建臨時目錄
        self.temp_dir = tempfile.mkdtemp()
        
        # 創建必要的子目錄
        self.scripts_dir = os.path.join(self.temp_dir, 'data', 'scripts')
        self.outputs_dir = os.path.join(self.temp_dir, 'data', 'outputs')
        self.meta_dir = os.path.join(self.temp_dir, 'data', 'meta')
        os.makedirs(self.scripts_dir)
        os.makedirs(self.outputs_dir)
        os.makedirs(self.meta_dir)
        
        self.script_manager = ScriptManager(self.temp_dir)
        
        # 創建測試用的Python腳本
        self.test_script = os.path.join(self.temp_dir, 'test_script.py')
        with open(self.test_script, 'w', encoding='utf-8') as f:
            f.write('''# 測試腳本
import os
import sys

def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
''')

    def tearDown(self):
        """測試後的清理工作"""
        # 關閉所有打開的文件
        for attr in dir(self):
            if isinstance(getattr(self, attr), FileStorage):
                getattr(self, attr).close()
        
        # 刪除臨時目錄
        try:
            shutil.rmtree(self.temp_dir)
        except PermissionError:
            pass  # 忽略權限錯誤

    def create_file_storage(self, file_path, filename):
        """創建一個模擬的FileStorage對象"""
        file = open(file_path, 'rb')
        storage = FileStorage(
            stream=file,
            filename=filename,
            content_type='text/x-python'
        )
        # 保存引用以便後續關閉
        setattr(self, f'file_storage_{filename}', storage)
        return storage

    def test_script_validation(self):
        """測試腳本驗證功能"""
        # 驗證有效的腳本
        is_valid, result = self.script_manager.validator.validate_script(self.test_script)
        self.assertTrue(is_valid)
        self.assertIsNotNone(result)
        
        # 驗證不存在的腳本
        is_valid, result = self.script_manager.validator.validate_script('nonexistent.py')
        self.assertFalse(is_valid)
        self.assertIn('errors', result)
        self.assertIn('文件不存在', result['errors'])

    def test_script_upload(self):
        """測試腳本上傳功能"""
        # 創建FileStorage對象
        file_storage = self.create_file_storage(self.test_script, 'test_script.py')
        
        # 上傳腳本
        result = self.script_manager.upload_script(
            file_storage,
            description='測試腳本',
            author='測試作者',
            tags=['test', 'demo']
        )
        
        self.assertEqual(result['status'], 'success')
        self.assertIn('message', result)
        
        # 檢查腳本是否被正確保存
        script_path = os.path.join(self.script_manager.scripts_dir, 'test_script.py')
        self.assertTrue(os.path.exists(script_path))
        
        # 檢查元數據是否被正確保存
        meta_path = os.path.join(self.script_manager.metadata.meta_dir, 'test_script.py.meta')
        self.assertTrue(os.path.exists(meta_path))

    def test_script_list(self):
        """測試腳本列表功能"""
        # 上傳一個腳本
        file_storage = self.create_file_storage(self.test_script, 'test_script.py')
        self.script_manager.upload_script(
            file_storage,
            description='測試腳本',
            author='測試作者',
            tags=['test', 'demo']
        )
        
        # 獲取腳本列表
        scripts = self.script_manager.list_scripts()
        self.assertIsInstance(scripts, list)
        self.assertTrue(len(scripts) > 0)
        
        # 檢查腳本信息
        script = scripts[0]
        self.assertEqual(script['name'], 'test_script.py')
        self.assertEqual(script['description'], '測試腳本')
        self.assertEqual(script['author'], '測試作者')
        self.assertEqual(script['tags'], ['test', 'demo'])

    def test_script_delete(self):
        """測試腳本刪除功能"""
        # 上傳一個腳本
        file_storage = self.create_file_storage(self.test_script, 'test_script.py')
        self.script_manager.upload_script(
            file_storage,
            description='測試腳本',
            author='測試作者',
            tags=['test', 'demo']
        )
        
        # 刪除腳本
        success, message = self.script_manager.delete_script('test_script.py')
        self.assertTrue(success)
        
        # 檢查腳本是否被刪除
        script_path = os.path.join(self.script_manager.scripts_dir, 'test_script.py')
        meta_path = os.path.join(self.script_manager.scripts_dir, 'test_script.meta')
        self.assertFalse(os.path.exists(script_path))
        self.assertFalse(os.path.exists(meta_path))

    def test_script_search(self):
        """測試腳本搜索功能"""
        # 上傳多個腳本
        scripts = [
            ('test1.py', '測試腳本1', '作者1', ['test', 'demo']),
            ('test2.py', '測試腳本2', '作者2', ['test', 'example']),
            ('demo.py', '演示腳本', '作者3', ['demo', 'example'])
        ]
        
        for name, desc, author, tags in scripts:
            script_path = os.path.join(self.temp_dir, name)
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write('# ' + desc)
            
            file_storage = self.create_file_storage(script_path, name)
            self.script_manager.upload_script(
                file_storage,
                description=desc,
                author=author,
                tags=tags
            )
        
        # 測試搜索功能
        results = self.script_manager.search_scripts('test')
        self.assertEqual(len(results), 2)
        
        results = self.script_manager.search_scripts('demo')
        self.assertEqual(len(results), 2)
        
        results = self.script_manager.search_scripts('作者')
        self.assertEqual(len(results), 3)

    def test_script_meta_update(self):
        """測試腳本元數據更新功能"""
        # 上傳腳本
        file_storage = self.create_file_storage(self.test_script, 'test_script.py')
        self.script_manager.upload_script(
            file_storage,
            description='原始描述',
            author='原始作者',
            tags=['original']
        )
        
        # 更新元數據
        success, message = self.script_manager.update_script_meta(
            'test_script.py',
            description='更新描述',
            author='更新作者',
            tags=['updated']
        )
        
        self.assertTrue(success)
        
        # 檢查更新結果
        scripts = self.script_manager.list_scripts()
        script = next(s for s in scripts if s['name'] == 'test_script.py')
        self.assertEqual(script['description'], '更新描述')
        self.assertEqual(script['author'], '更新作者')
        self.assertEqual(script['tags'], ['updated'])

if __name__ == '__main__':
    unittest.main() 