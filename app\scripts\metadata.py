"""腳本元數據管理器"""
import os
import json
from datetime import datetime
import pytz
import shutil
import logging

class MetadataManager:
    def __init__(self, base_dir):
        """初始化元數據管理器"""
        self.base_dir = base_dir
        self.meta_dir = os.path.join(base_dir, 'data', 'meta')
        self.meta_file = os.path.join(self.meta_dir, 'scripts.json')
        self.timezone = pytz.timezone('Asia/Taipei')
        
        # 確保目錄存在
        if not os.path.exists(self.meta_dir):
            os.makedirs(self.meta_dir)
        
        # 初始化或加載元數據
        self.metadata = self._load_metadata()
        
        # 遷移舊的元數據到獨立文件
        self._migrate_to_individual_files()
    
    def _load_metadata(self):
        """載入元數據，如果不存在則創建新的"""
        if os.path.exists(self.meta_file):
            try:
                with open(self.meta_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except json.JSONDecodeError:
                print(f"Warning: Metadata file is corrupted, creating new one")
        
        # 創建新的元數據結構
        return {
            "scripts": {},
            "meta": {
                "version": "1.0",
                "last_updated": self._get_current_time()
            }
        }
    
    def _save_metadata(self):
        """保存元數據到文件"""
        self.metadata["meta"]["last_updated"] = self._get_current_time()
        with open(self.meta_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, ensure_ascii=False, indent=4)
    
    def _get_current_time(self):
        """獲取當前時間的ISO格式字符串"""
        return datetime.now(self.timezone).isoformat()
    
    def _get_script_meta_path(self, script_name):
        """獲取腳本專用元數據文件路徑"""
        return os.path.join(self.meta_dir, f"{script_name}.meta.json")
    
    def _migrate_to_individual_files(self):
        """將中央元數據文件中的腳本元數據遷移到獨立文件中"""
        for script_name, script_meta in self.metadata["scripts"].items():
            meta_path = self._get_script_meta_path(script_name)
            if not os.path.exists(meta_path):
                with open(meta_path, 'w', encoding='utf-8') as f:
                    json.dump(script_meta, f, ensure_ascii=False, indent=4)
                print(f"已將腳本 {script_name} 的元數據遷移到獨立文件")
    
    def _load_script_meta(self, script_name):
        """從獨立文件加載腳本元數據"""
        meta_path = self._get_script_meta_path(script_name)
        if os.path.exists(meta_path):
            try:
                with open(meta_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except json.JSONDecodeError:
                print(f"Warning: Script metadata file for {script_name} is corrupted")
        return None
    
    def _save_script_meta(self, script_name, meta_data=None):
        """保存腳本元數據到獨立文件"""
        meta_path = self._get_script_meta_path(script_name)
        if meta_data is None:
            meta_data = self.metadata["scripts"][script_name]
        with open(meta_path, 'w', encoding='utf-8') as f:
            json.dump(meta_data, f, ensure_ascii=False, indent=4)
    
    def add_script(self, script_name, description=None, author=None, tags=None):
        """添加新腳本的元數據"""
        current_time = self._get_current_time()
        
        # 確保標籤是列表
        if tags is None:
            tags = []
        elif not isinstance(tags, list):
            tags = []
            
        # 確保描述和作者有合理默認值
        description = description or f"腳本 - {script_name}"
        author = author or "system"
        
        # 創建元數據
        script_meta = {
            "description": description,
            "created_at": current_time,
            "modified_at": current_time,
            "author": author,
            "tags": tags
        }
        
        # 更新中央元數據
        self.metadata["scripts"][script_name] = script_meta
        self._save_metadata()
        
        # 保存到獨立文件
        self._save_script_meta(script_name, script_meta)
        
        print(f"已添加腳本 {script_name} 的元數據，標籤: {tags}")
        return True
    
    def update_script(self, script_name, description=None, tags=None):
        """
        更新腳本的元數據

        Args:
            script_name (str): 腳本名稱
            description (str, optional): 腳本描述
            tags (list, optional): 腳本標籤列表，現在只使用第一個標籤

        Raises:
            KeyError: 如果腳本不存在則引發此錯誤
        """
        # 檢查腳本是否存在
        if script_name not in self.metadata['scripts']:
            raise KeyError(f"腳本 {script_name} 不存在")

        # 更新元數據
        now = datetime.now().isoformat()
        if description is not None:
            # 限制描述長度為20個字符
            self.metadata['scripts'][script_name]['description'] = description[:20]
        
        if tags is not None:
            # 確保 tags 是列表
            tag_list = tags if isinstance(tags, list) else [tags]
            # 只使用第一個標籤
            self.metadata['scripts'][script_name]['tags'] = tag_list[0:1] if tag_list else []

        # 更新修改時間
        self.metadata['scripts'][script_name]['modified_at'] = now
        
        # 保存元數據
        self._save_metadata()
        
        # 同時更新單獨的元數據文件
        self._save_script_meta(script_name)
    
    def delete_script(self, script_name):
        """刪除腳本的元數據"""
        # 刪除獨立文件
        meta_path = self._get_script_meta_path(script_name)
        if os.path.exists(meta_path):
            os.remove(meta_path)
            print(f"已刪除腳本 {script_name} 的元數據文件")
        
        # 從中央元數據中刪除
        if script_name in self.metadata["scripts"]:
            del self.metadata["scripts"][script_name]
            self._save_metadata()
            print(f"已從中央元數據中刪除腳本 {script_name}")
        
        return True
    
    def get_script_meta(self, script_name):
        """獲取腳本的元數據"""
        # 首先從獨立文件獲取
        script_meta = self._load_script_meta(script_name)
        if script_meta is not None:
            return script_meta
            
        # 如果獨立文件不存在，從中央元數據獲取
        return self.metadata["scripts"].get(script_name)
    
    def list_scripts(self):
        """列出所有腳本的元數據"""
        try:
            if not os.path.exists(self.meta_file):
                return []
            
            with open(self.meta_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
                scripts = []
                
                # 遍歷所有腳本元數據
                for script_name, script_data in metadata["scripts"].items():
                    script_info = {
                        'name': script_name,
                        'description': script_data.get('description', ''),
                        'author': script_data.get('author', ''),
                        'tags': script_data.get('tags', []),
                        'created_at': script_data.get('created_at', ''),
                        'modified_at': script_data.get('modified_at', '')
                    }
                    scripts.append(script_info)
                
                return scripts
        except Exception as e:
            logging.error(f'讀取腳本元數據時出錯: {str(e)}')
            return []
    
    def search_scripts(self, query):
        """搜索腳本
        query可以是描述或標籤的部分匹配
        """
        try:
            results = []
            if not query:  # 如果查詢為空，返回所有腳本
                return self.list_scripts()
                
            query = query.lower()
            scripts = self.list_scripts()
            
            for script in scripts:
                # 檢查腳本名稱
                if query in script.get('name', '').lower():
                    results.append(script)
                    continue
                    
                # 檢查描述
                if query in (script.get('description') or '').lower():
                    results.append(script)
                    continue
                    
                # 檢查作者
                if query in (script.get('author') or '').lower():
                    results.append(script)
                    continue
                    
                # 檢查標籤
                if any(query in (tag or '').lower() for tag in (script.get('tags') or [])):
                    results.append(script)
                    continue
            
            return results
        except Exception as e:
            logging.error(f'搜索腳本時出錯: {str(e)}')
            return []
    
    def backup_metadata(self):
        """創建元數據的備份"""
        backup_dir = os.path.join(self.meta_dir, 'backup')
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
            
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        backup_path = os.path.join(backup_dir, f'metadata_backup_{timestamp}')
        os.makedirs(backup_path)
        
        # 備份中央元數據文件
        if os.path.exists(self.meta_file):
            shutil.copy2(self.meta_file, os.path.join(backup_path, 'scripts.json'))
        
        # 備份所有獨立的元數據文件
        for filename in os.listdir(self.meta_dir):
            if filename.endswith('.meta.json'):
                src_path = os.path.join(self.meta_dir, filename)
                dst_path = os.path.join(backup_path, filename)
                shutil.copy2(src_path, dst_path)
        
        return backup_path
    
    def restore_metadata(self, backup_path):
        """從備份目錄恢復元數據"""
        try:
            # 恢復中央元數據文件
            central_meta_backup = os.path.join(backup_path, 'scripts.json')
            if os.path.exists(central_meta_backup):
                shutil.copy2(central_meta_backup, self.meta_file)
            
            # 恢復所有獨立的元數據文件
            for filename in os.listdir(backup_path):
                if filename.endswith('.meta.json'):
                    src_path = os.path.join(backup_path, filename)
                    dst_path = os.path.join(self.meta_dir, filename)
                    shutil.copy2(src_path, dst_path)
            
            # 重新加載元數據
            self.metadata = self._load_metadata()
            
            return True
        except Exception as e:
            print(f"Error restoring metadata: {str(e)}")
            return False
