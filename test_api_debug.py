#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試API端點
"""
import requests
import json

def test_schedules_api():
    """測試排程API"""
    try:
        print("測試排程列表API...")
        response = requests.get("http://127.0.0.1:5000/api/v1/schedules")
        print(f"狀態碼: {response.status_code}")
        print(f"響應內容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"解析後的數據: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"測試失敗: {str(e)}")

def test_system_resources_api():
    """測試系統資源API"""
    try:
        print("\n測試系統資源API...")
        response = requests.get("http://127.0.0.1:5000/api/v1/system/resources")
        print(f"狀態碼: {response.status_code}")
        print(f"響應內容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"解析後的數據: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"測試失敗: {str(e)}")

if __name__ == "__main__":
    test_schedules_api()
    test_system_resources_api()
