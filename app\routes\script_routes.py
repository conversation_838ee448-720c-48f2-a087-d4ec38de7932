import os
import logging
import tempfile
from flask import Blueprint, jsonify, request, current_app
from werkzeug.utils import secure_filename
from ..models.script import Script
from .. import db
from app.utils.api_logger import APILogger, api_logger

bp = Blueprint('script', __name__, url_prefix='/script')
logger = logging.getLogger(__name__)

def allowed_file(filename):
    """檢查檔案是否為允許的類型"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() == 'py'

@bp.route('/', methods=['GET'])
@APILogger.log_request
@APILogger.monitor_api_health("腳本列表頁面")
def get_scripts():
    """獲取所有腳本"""
    try:
        # 直接從資料庫獲取
        scripts = Script.query.all()
        
        # 同時檢查文件是否存在，如果資料庫記錄和實際文件不一致則同步
        for script in scripts[:]:
            if not os.path.exists(script.file_path):
                api_logger.warning(f"資料庫中的腳本 {script.name} 文件不存在於磁碟: {script.file_path}")
                # 可選：從結果中移除不存在的腳本
                # scripts.remove(script)
        
        # 檢查uploads目錄中是否有未在資料庫中記錄的腳本，並同步
        upload_folder = current_app.config.get('UPLOAD_FOLDER')
        if os.path.exists(upload_folder):
            for filename in os.listdir(upload_folder):
                if filename.endswith('.py'):
                    # 檢查是否已在資料庫中
                    if not Script.query.filter_by(name=filename).first():
                        api_logger.info(f"發現未記錄的腳本文件: {filename}，同步到資料庫")
                        # 自動同步到資料庫
                        script = Script(
                            name=filename,
                            description='(自動同步) ' + filename,
                            file_path=os.path.join(upload_folder, filename)
                        )
                        db.session.add(script)
            db.session.commit()
            
        api_logger.info(f"成功獲取腳本列表，共 {len(scripts)} 個腳本")
        return jsonify({
            'status': 'success',
            'data': [script.to_dict() for script in scripts]
        })
    except Exception as e:
        api_logger.error(f"獲取腳本列表失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'獲取腳本列表失敗: {str(e)}'
        }), 500

@bp.route('/<script_name>', methods=['GET'])
@APILogger.log_request
def get_script(script_name):
    """獲取特定腳本"""
    try:
        script = Script.query.filter_by(name=script_name).first_or_404()
        
        # 檢查文件是否實際存在
        if not os.path.exists(script.file_path):
            api_logger.warning(f"腳本文件不存在: {script.file_path}")
            # 但仍返回資料庫中的資訊
            
        return jsonify({
            'status': 'success',
            'data': script.to_dict()
        })
    except Exception as e:
        api_logger.error(f"獲取腳本 {script_name} 詳細信息失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'獲取腳本詳細信息失敗: {str(e)}'
        }), 500

@bp.route('/', methods=['POST'])
@APILogger.log_request
@APILogger.monitor_api_health("腳本上傳")
def upload_script():
    """上傳新腳本"""
    try:
        api_logger.debug("處理腳本上傳請求")
        
        # 檢查請求中是否有文件
        if 'file' not in request.files:
            api_logger.warning("未找到上傳文件")
            return jsonify({
                'status': 'error',
                'message': '沒有提供檔案'
            }), 400
            
        file = request.files['file']
        
        # 檢查文件名是否為空
        if file.filename == '':
            api_logger.warning("文件名為空")
            return jsonify({
                'status': 'error',
                'message': '沒有選擇檔案'
            }), 400
            
        # 檢查文件類型
        if not file.filename.endswith('.py'):
            api_logger.warning(f"不支持的文件類型: {file.filename}")
            return jsonify({
                'status': 'error',
                'message': '只允許上傳 Python 腳本'
            }), 400
            
        # 安全處理文件名
        filename = secure_filename(file.filename)
        api_logger.debug(f"準備上傳腳本: {filename}")
        
        # 獲取上傳目錄
        upload_folder = current_app.config.get('UPLOAD_FOLDER')
        
        # 確保上傳目錄存在
        try:
            if not os.path.exists(upload_folder):
                api_logger.warning(f"上傳目錄不存在，創建目錄: {upload_folder}")
                os.makedirs(upload_folder, exist_ok=True)
        except Exception as e:
            api_logger.error(f"創建上傳目錄失敗: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'上傳目錄創建失敗: {str(e)}'
            }), 500
        
        # 檢查文件是否已存在
        if Script.query.filter_by(name=filename).first():
            api_logger.warning(f"腳本已存在: {filename}")
            return jsonify({
                'status': 'error',
                'message': '腳本已存在'
            }), 400
            
        # 完整路徑
        file_path = os.path.join(upload_folder, filename)
        
        # 保存文件
        try:
            api_logger.debug(f"保存文件到: {file_path}")
            file.save(file_path)
        except Exception as save_error:
            api_logger.error(f"文件保存失敗: {str(save_error)}")
            return jsonify({
                'status': 'error',
                'message': f'檔案儲存失敗: {str(save_error)}'
            }), 500
        
        # 建立腳本記錄
        try:
            script = Script(
                name=filename,
                description=request.form.get('description', ''),
                file_path=file_path,
                tags=request.form.get('tags', '未分類')
            )
            
            db.session.add(script)
            db.session.commit()
            api_logger.info(f"腳本上傳成功: {filename}")
            
            return jsonify({
                'status': 'success',
                'data': script.to_dict()
            }), 201
        except Exception as db_error:
            api_logger.error(f"資料庫紀錄創建失敗: {str(db_error)}")
            
            # 如果資料庫記錄失敗，嘗試刪除已上傳的文件
            try:
                os.remove(file_path)
            except:
                pass
                
            return jsonify({
                'status': 'error',
                'message': f'腳本記錄創建失敗: {str(db_error)}'
            }), 500
    except Exception as e:
        api_logger.error(f"腳本上傳過程發生未處理的錯誤: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'腳本上傳失敗: {str(e)}'
        }), 500

@bp.route('/validate', methods=['POST'])
@APILogger.log_request
def validate_script():
    """驗證腳本"""
    try:
        api_logger.debug("處理腳本驗證請求")

        # 檢查請求中是否有文件
        if 'file' not in request.files:
            api_logger.warning("未找到驗證文件")
            return jsonify({
                'status': 'error',
                'message': '沒有提供檔案'
            }), 400

        file = request.files['file']

        # 檢查文件名是否為空
        if file.filename == '':
            api_logger.warning("驗證文件名為空")
            return jsonify({
                'status': 'error',
                'message': '沒有選擇檔案'
            }), 400

        # 檢查文件類型
        if not file.filename.endswith('.py'):
            api_logger.warning(f"不支持的驗證文件類型: {file.filename}")
            return jsonify({
                'status': 'error',
                'message': '只允許驗證 Python 腳本'
            }), 400

        # 讀取文件內容進行驗證
        try:
            file_content = file.read().decode('utf-8')
            file.seek(0)  # 重置文件指針
        except UnicodeDecodeError:
            return jsonify({
                'status': 'error',
                'message': '文件編碼錯誤，請確保使用UTF-8編碼'
            }), 400

        # 使用腳本執行器進行驗證
        script_executor = getattr(current_app, 'script_executor', None)
        if not script_executor:
            api_logger.error("腳本執行器未初始化")
            return jsonify({
                'status': 'error',
                'message': '腳本驗證服務不可用'
            }), 500

        # 創建臨時文件進行驗證
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # 執行驗證
            validation_result = script_executor._validate_script(temp_file_path)

            # 構建回應
            response_data = {
                'status': 'success',
                'valid': validation_result.get('valid', False),
                'errors': validation_result.get('errors', []),
                'warnings': validation_result.get('warnings', []),
                'missing_packages': validation_result.get('missing_packages', []),
                'has_severe_errors': len(validation_result.get('errors', [])) > 0,
                'has_missing_packages': len(validation_result.get('missing_packages', [])) > 0
            }

            api_logger.info(f"腳本驗證完成: {file.filename}, 有效: {response_data['valid']}")
            return jsonify(response_data)

        finally:
            # 清理臨時文件
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        api_logger.error(f"腳本驗證過程發生錯誤: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'腳本驗證失敗: {str(e)}'
        }), 500

@bp.route('/install-dependencies', methods=['POST'])
@APILogger.log_request
def install_dependencies():
    """安裝腳本依賴包"""
    try:
        api_logger.debug("處理依賴包安裝請求")

        data = request.get_json()
        if not data or 'packages' not in data:
            return jsonify({
                'status': 'error',
                'message': '沒有提供依賴包列表'
            }), 400

        packages = data['packages']
        if not isinstance(packages, list) or not packages:
            return jsonify({
                'status': 'error',
                'message': '依賴包列表格式錯誤'
            }), 400

        # 這裡可以實現實際的依賴包安裝邏輯
        # 目前返回模擬成功響應
        installed_packages = []
        failed_packages = []

        for package in packages:
            try:
                # 模擬安裝過程
                api_logger.info(f"模擬安裝依賴包: {package}")
                installed_packages.append(package)
            except Exception as e:
                api_logger.error(f"安裝依賴包 {package} 失敗: {str(e)}")
                failed_packages.append({'package': package, 'error': str(e)})

        return jsonify({
            'status': 'success',
            'installed': installed_packages,
            'failed': failed_packages,
            'message': f'成功安裝 {len(installed_packages)} 個依賴包'
        })

    except Exception as e:
        api_logger.error(f"安裝依賴包過程發生錯誤: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'安裝依賴包失敗: {str(e)}'
        }), 500

@bp.route('/<script_name>', methods=['PUT'])
def update_script(script_name):
    """更新腳本資訊"""
    script = Script.query.filter_by(name=script_name).first_or_404()
    data = request.get_json()
    
    if not data:
        return jsonify({
            'status': 'error',
            'message': '沒有提供資料'
        }), 400
        
    if 'description' in data:
        script.description = data['description']
        
    if 'is_active' in data:
        script.is_active = data['is_active']
        
    db.session.commit()
    return jsonify({
        'status': 'success',
        'data': script.to_dict()
    })

@bp.route('/<script_name>', methods=['DELETE'])
def delete_script(script_name):
    """刪除腳本"""
    script = Script.query.filter_by(name=script_name).first_or_404()
    
    # 刪除檔案
    try:
        os.remove(script.file_path)
    except OSError:
        pass
        
    db.session.delete(script)
    db.session.commit()
    
    return jsonify({
        'status': 'success',
        'message': '腳本已刪除'
    }) 