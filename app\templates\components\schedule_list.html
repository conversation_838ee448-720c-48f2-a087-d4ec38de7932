{% macro schedule_list(schedules) %}
<div class="schedule-list">
    <div class="schedule-header">
        <h3>排程列表</h3>
        <button class="btn btn-primary" onclick="showAddScheduleModal()">
            <i class="fas fa-plus"></i> 新增排程
        </button>
    </div>
    
    <div class="schedule-filters">
        <div class="form-group">
            <label for="statusFilter">狀態篩選：</label>
            <select id="statusFilter" class="form-control" onchange="filterSchedules()">
                <option value="all">全部</option>
                <option value="pending">等待中</option>
                <option value="running">執行中</option>
                <option value="completed">已完成</option>
                <option value="failed">失敗</option>
            </select>
        </div>
        <div class="form-group">
            <label for="typeFilter">類型篩選：</label>
            <select id="typeFilter" class="form-control" onchange="filterSchedules()">
                <option value="all">全部</option>
                <option value="immediate">立即執行</option>
                <option value="once">一次性</option>
                <option value="daily">每日</option>
                <option value="weekly">每週</option>
                <option value="monthly">每月</option>
            </select>
        </div>
    </div>

    <div class="schedule-table">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>描述</th>
                    <th>腳本名稱</th>
                    <th>執行時間</th>
                    <th>狀態</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="scheduleList">
                {% if schedules %}
                    {% for schedule in schedules %}
                    <tr data-schedule-id="{{ schedule.id }}">
                        <td>{{ schedule.description or '無描述' }}</td>
                        <td>{{ schedule.script_name }}</td>
                        <td>{{ schedule.next_run or '未設置' }}</td>
                        <td>
                            <span class="status-badge status-{{ schedule.status.lower() }}">
                                {{ schedule.status }}
                            </span>
                            {% if schedule.is_active %}
                            <span class="badge bg-success ms-2">啟用</span>
                            {% else %}
                            <span class="badge bg-secondary ms-2">停用</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                {% if schedule.is_active %}
                                <button class="btn btn-sm btn-outline-secondary me-1" onclick="toggleScheduleStatus('{{ schedule.id }}', false)">停用</button>
                                {% else %}
                                <button class="btn btn-sm btn-outline-success me-1" onclick="toggleScheduleStatus('{{ schedule.id }}', true)">啟用</button>
                                {% endif %}
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editSchedule('{{ schedule.id }}')">修改</button>
                                <button class="btn btn-sm btn-outline-danger me-1" onclick="deleteSchedule('{{ schedule.id }}')">刪除</button>
                                {% if schedule.status == 'running' %}
                                <button class="btn btn-sm btn-warning me-1" onclick="pauseSchedule('{{ schedule.id }}')">暫停</button>
                                <button class="btn btn-sm btn-danger me-1" onclick="stopSchedule('{{ schedule.id }}')">停止</button>
                                {% elif schedule.status == 'failed' %}
                                <button class="btn btn-sm btn-primary me-1" onclick="retrySchedule('{{ schedule.id }}')">重試</button>
                                {% endif %}
                                <button class="btn btn-sm btn-info" onclick="viewScheduleLog('{{ schedule.id }}')">日誌</button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="5" class="text-center">沒有排程記錄</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<style>
.schedule-list {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.schedule-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.schedule-table {
    overflow-x: auto;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.status-pending {
    background-color: #ffd700;
    color: #000;
}

.status-running {
    background-color: #4CAF50;
    color: white;
}

.status-completed {
    background-color: #2196F3;
    color: white;
}

.status-failed {
    background-color: #f44336;
    color: white;
}

.btn-group {
    display: flex;
    gap: 5px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}
</style>

<script>
function filterSchedules() {
    const statusFilter = document.getElementById('statusFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const rows = document.querySelectorAll('#scheduleList tr');
    
    rows.forEach(row => {
        const status = row.querySelector('.status-badge')?.textContent.toLowerCase();
        const type = row.querySelector('td:nth-child(2)')?.textContent.toLowerCase();
        
        if (!status || !type) return;
        
        const statusMatch = statusFilter === 'all' || status.includes(statusFilter);
        const typeMatch = typeFilter === 'all' || type.includes(typeFilter);
        
        row.style.display = statusMatch && typeMatch ? '' : 'none';
    });
}

function showAddScheduleModal() {
    window.location.href = '/schedule/add';
}

// 定期更新排程狀態
setInterval(() => {
    fetch('/schedule/api/schedule')
        .then(response => response.json())
        .then(result => {
            if (result.status === 'success') {
                const schedules = result.data;
                schedules.forEach(schedule => {
                    const row = document.querySelector(`tr[data-schedule-id="${schedule.id}"]`);
                    if (row) {
                        // 更新狀態
                        const statusCell = row.querySelector('td:nth-child(4)');
                        if (statusCell) {
                            statusCell.innerHTML = getStatusHtml(schedule);
                        }
                        
                        // 更新操作按鈕
                        const actionCell = row.querySelector('td:nth-child(5)');
                        if (actionCell) {
                            actionCell.innerHTML = getActionButtons(schedule);
                        }
                    }
                });
            }
        })
        .catch(error => console.error('更新排程狀態失敗:', error));
}, 5000);
</script>
{% endmacro %} 