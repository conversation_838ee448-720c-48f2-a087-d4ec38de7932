#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
測試依賴包的腳本
這個腳本導入多個常用但可能未安裝的依賴包
"""

# 網頁請求和解析
import requests
from bs4 import BeautifulSoup

# 數據處理和分析
import pandas as pd
import numpy as np

# 可視化
import matplotlib.pyplot as plt
import seaborn as sns

# 圖像處理
from PIL import Image

# 更高級的數據科學工具
import scipy
import statsmodels.api as sm

# 機器學習
import sklearn
from sklearn.ensemble import RandomForestClassifier

def main():
    """主函數，展示各個依賴包的簡單用法"""
    print("測試依賴包腳本運行中...")
    
    # 獲取網頁數據
    try:
        response = requests.get("https://www.example.com")
        soup = BeautifulSoup(response.content, "html.parser")
        page_title = soup.title.string
        print(f"網頁標題: {page_title}")
    except Exception as e:
        print(f"網頁請求錯誤: {e}")
    
    # 創建數據框
    try:
        df = pd.DataFrame({
            'A': np.random.randn(10),
            'B': np.random.randn(10)
        })
        print("數據框摘要:")
        print(df.describe())
    except Exception as e:
        print(f"數據處理錯誤: {e}")
    
    # 繪製圖表
    try:
        plt.figure(figsize=(10, 6))
        sns.scatterplot(x='A', y='B', data=df)
        plt.title("隨機數據散點圖")
        plt.savefig("test_plot.png")
        print("已保存測試圖表為 test_plot.png")
    except Exception as e:
        print(f"繪圖錯誤: {e}")
    
    # 簡單機器學習模型
    try:
        X = np.random.rand(100, 4)
        y = np.random.randint(0, 2, 100)
        clf = RandomForestClassifier(n_estimators=10)
        clf.fit(X, y)
        print("機器學習模型訓練完成")
    except Exception as e:
        print(f"機器學習錯誤: {e}")

if __name__ == "__main__":
    main() 