// 排程管理功能 JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 排程類型下拉選單
    const scheduleTypeSelect = document.getElementById('schedule-type');
    if (scheduleTypeSelect) {
        // 監聽變更事件
        scheduleTypeSelect.addEventListener('change', function() {
            updateFormFields(this.value);
        });
        
        // 初始狀態
        updateFormFields(scheduleTypeSelect.value);
    }
    
    // 保存按鈕點擊事件
    const saveButton = document.getElementById('save-schedule');
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            if (validateScheduleForm()) {
                saveSchedule();
            }
        });
    }
    
    // 初始化系統資源使用狀況
    initSystemResources();
    
    // 每分鐘間隔滑桿與數值連動
    const intervalRange = document.getElementById('interval-minutes-range');
    const intervalNumber = document.getElementById('interval-minutes');
    if (intervalRange && intervalNumber) {
        intervalRange.addEventListener('input', function() {
            intervalNumber.value = this.value;
            updateMinutePreview(parseInt(this.value));
        });
        
        intervalNumber.addEventListener('input', function() {
            // 確保數值在範圍內
            let value = parseInt(this.value);
            if (value < 5) value = 5;
            if (value > 60) value = 60;
            this.value = value;
            intervalRange.value = value;
            updateMinutePreview(value);
        });
    }
    
    // 日期時間相關防呆檢查 - 一次性排程
    const executionDate = document.getElementById('execution-date');
    const executionTime = document.getElementById('execution-time');
    if (executionDate && executionTime) {
        executionDate.addEventListener('change', function() {
            validateDateTime();
        });
        
        executionTime.addEventListener('change', function() {
            validateDateTime();
        });
        
        // 設置當前日期為最小日期
        const today = new Date().toISOString().split('T')[0];
        executionDate.min = today;
    }
    
    // 每日排程時間檢查
    const dailyTime = document.getElementById('daily-time');
    if (dailyTime) {
        dailyTime.addEventListener('change', function() {
            validateDailyTime();
        });
        // 初始檢查
        if (dailyTime.value) {
            validateDailyTime();
        }
    }
    
    // 每週排程時間與日期檢查
    const weeklyTime = document.getElementById('weekly-time');
    if (weeklyTime) {
        weeklyTime.addEventListener('change', function() {
            validateWeeklyTime();
        });
    }
    
    // 監聽週間選擇變化
    document.querySelectorAll('input[name="weekdays"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (weeklyTime && weeklyTime.value) {
                validateWeeklyTime();
            }
        });
    });
    
    // 每月排程時間與日期檢查
    const monthlyTime = document.getElementById('monthly-time');
    if (monthlyTime) {
        monthlyTime.addEventListener('change', function() {
            validateMonthlyTime();
        });
    }
    
    // 監聽月份日期選擇變化
    document.querySelectorAll('input[name="days_of_month"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (monthlyTime && monthlyTime.value) {
                validateMonthlyTime();
            }
        });
    });
    
    // 工作日與週末快速選擇按鈕
    const selectWeekdays = document.getElementById('select-weekdays');
    const selectWeekend = document.getElementById('select-weekend');
    if (selectWeekdays) {
        selectWeekdays.addEventListener('click', function() {
            // 選擇週一到週五 (1-5)
            for (let i = 1; i <= 5; i++) {
                document.getElementById(`week-${getDayName(i)}`).checked = true;
            }
            document.getElementById('week-sun').checked = false;
            document.getElementById('week-sat').checked = false;
            
            // 更新提示
            if (weeklyTime && weeklyTime.value) {
                validateWeeklyTime();
            }
        });
    }
    
    if (selectWeekend) {
        selectWeekend.addEventListener('click', function() {
            // 選擇週六和週日 (0,6)
            document.getElementById('week-sun').checked = true;
            document.getElementById('week-sat').checked = true;
            // 取消選擇週一到週五
            for (let i = 1; i <= 5; i++) {
                document.getElementById(`week-${getDayName(i)}`).checked = false;
            }
            
            // 更新提示
            if (weeklyTime && weeklyTime.value) {
                validateWeeklyTime();
            }
        });
    }
    
    // 月份日期快速選擇按鈕
    const selectFirstDay = document.getElementById('select-first-day');
    const selectMidDay = document.getElementById('select-mid-day');
    const selectLastDay = document.getElementById('select-last-day');
    
    if (selectFirstDay) {
        selectFirstDay.addEventListener('click', function() {
            // 清除所有選擇
            clearMonthDays();
            // 選擇1號
            document.getElementById('day-1').checked = true;
            
            // 更新提示
            if (monthlyTime && monthlyTime.value) {
                validateMonthlyTime();
            }
        });
    }
    
    if (selectMidDay) {
        selectMidDay.addEventListener('click', function() {
            // 清除所有選擇
            clearMonthDays();
            // 選擇15號
            document.getElementById('day-15').checked = true;
            
            // 更新提示
            if (monthlyTime && monthlyTime.value) {
                validateMonthlyTime();
            }
        });
    }
    
    if (selectLastDay) {
        selectLastDay.addEventListener('click', function() {
            // 清除所有選擇
            clearMonthDays();
            // 選擇28-31號
            for (let i = 28; i <= 31; i++) {
                document.getElementById(`day-${i}`).checked = true;
            }
            
            // 更新提示
            if (monthlyTime && monthlyTime.value) {
                validateMonthlyTime();
            }
        });
    }
    
    // 監聽模態框關閉事件，清除表單
    const scheduleModal = document.getElementById('scheduleModal');
    if (scheduleModal) {
        scheduleModal.addEventListener('hidden.bs.modal', function() {
            resetScheduleForm();
        });
    }
});

// 清除月份日期選擇
function clearMonthDays() {
    for (let i = 1; i <= 31; i++) {
        document.getElementById(`day-${i}`).checked = false;
    }
}

// 取得星期幾名稱
function getDayName(dayNum) {
    const days = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
    return days[dayNum];
}

// 初始化系統資源使用狀況
function initSystemResources() {
    // 這個函數會在實際應用中從API獲取系統資源使用狀況
    // 這裡使用模擬數據
    fetch('/api/api/v1/system/resources')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                updateResourceMeters(data.data);
            } else {
                // 使用模擬數據
                updateResourceMeters({
                    cpu: 25,
                    memory: 40,
                    schedules: {
                        current: 3,
                        max: 10
                    }
                });
            }
        })
        .catch(error => {
            console.error('載入系統資源資訊時發生錯誤:', error);
            // 使用模擬數據
            updateResourceMeters({
                cpu: 25,
                memory: 40,
                schedules: {
                    current: 3,
                    max: 10
                }
            });
        });
}

// 更新資源使用指標
function updateResourceMeters(data) {
    const cpuUsage = document.getElementById('cpu-usage');
    const memoryUsage = document.getElementById('memory-usage');
    const scheduleCount = document.getElementById('schedule-count');
    
    if (cpuUsage) {
        cpuUsage.style.width = `${data.cpu}%`;
        cpuUsage.setAttribute('data-content', `${data.cpu}%`);
        
        // 基於使用率設置視覺警告
        if (data.cpu > 80) {
            cpuUsage.classList.add('danger');
        } else if (data.cpu > 60) {
            cpuUsage.classList.add('warning');
        }
    }
    
    if (memoryUsage) {
        memoryUsage.style.width = `${data.memory}%`;
        memoryUsage.setAttribute('data-content', `${data.memory}%`);
        
        if (data.memory > 80) {
            memoryUsage.classList.add('danger');
        } else if (data.memory > 60) {
            memoryUsage.classList.add('warning');
        }
    }
    
    if (scheduleCount) {
        const percentUsed = (data.schedules.current / data.schedules.max) * 100;
        scheduleCount.style.width = `${percentUsed}%`;
        scheduleCount.setAttribute('data-content', `${data.schedules.current}/${data.schedules.max}`);
        
        if (percentUsed > 80) {
            scheduleCount.classList.add('danger');
        } else if (percentUsed > 60) {
            scheduleCount.classList.add('warning');
        }
    }
}

// 根據排程類型更新表單欄位
function updateFormFields(scheduleType) {
    // 隱藏所有類型特定欄位
    document.querySelectorAll('.schedule-option').forEach(field => {
        field.style.display = 'none';
    });
    
    // 隱藏所有時間調整提示
    document.querySelectorAll('.alert').forEach(alert => {
        alert.classList.add('d-none');
    });
    
    // 顯示對應排程類型的欄位
    switch(scheduleType) {
        case 'immediate':
            // 立即執行不需額外欄位
            break;
        case 'once':
            document.querySelector('.once-options').style.display = 'block';
            // 設置當前日期為預設值
            const today = new Date().toISOString().split('T')[0];
            const executionDate = document.getElementById('execution-date');
            if (executionDate && !executionDate.value) {
                executionDate.value = today;
            }
            
            // 設置預設時間為當前時間 + 1小時
            const executionTime = document.getElementById('execution-time');
            if (executionTime && !executionTime.value) {
                const now = new Date();
                now.setHours(now.getHours() + 1);
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                executionTime.value = `${hours}:${minutes}`;
            }
            
            // 初始驗證
            validateDateTime();
            break;
        case 'minute':
            document.querySelector('.minute-options').style.display = 'block';
            // 更新分鐘預覽
            updateMinutePreview(parseInt(document.getElementById('interval-minutes').value || 5));
            break;
        case 'daily':
            document.querySelector('.daily-options').style.display = 'block';
            
            // 設置預設時間為當前時間 + 1小時
            const dailyTime = document.getElementById('daily-time');
            if (dailyTime && !dailyTime.value) {
                const now = new Date();
                now.setHours(now.getHours() + 1);
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                dailyTime.value = `${hours}:${minutes}`;
                validateDailyTime();
            }
            break;
        case 'weekly':
            document.querySelector('.weekly-options').style.display = 'block';
            
            // 設置預設時間為當前時間 + 1小時
            const weeklyTime = document.getElementById('weekly-time');
            if (weeklyTime && !weeklyTime.value) {
                const now = new Date();
                now.setHours(now.getHours() + 1);
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                weeklyTime.value = `${hours}:${minutes}`;
            }
            break;
        case 'monthly':
            document.querySelector('.monthly-options').style.display = 'block';
            
            // 設置預設時間為當前時間 + 1小時
            const monthlyTime = document.getElementById('monthly-time');
            if (monthlyTime && !monthlyTime.value) {
                const now = new Date();
                now.setHours(now.getHours() + 1);
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                monthlyTime.value = `${hours}:${minutes}`;
            }
            break;
    }
}

// 更新分鐘執行預覽
function updateMinutePreview(interval) {
    const previewList = document.getElementById('minute-preview');
    if (!previewList) return;
    
    // 清空現有項目
    previewList.innerHTML = '';
    
    // 生成未來5次執行時間
    const now = new Date();
    for (let i = 1; i <= 5; i++) {
        const nextTime = new Date(now.getTime() + i * interval * 60000);
        const formattedTime = nextTime.toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        const listItem = document.createElement('li');
        listItem.className = 'list-group-item';
        listItem.innerHTML = `<i class="fas fa-clock me-2 text-muted"></i> ${formattedTime}`;
        previewList.appendChild(listItem);
    }
}

// 驗證日期時間是否為將來時間，若為過去時間則自動調整
function validateDateTime() {
    const dateInput = document.getElementById('execution-date');
    const timeInput = document.getElementById('execution-time');
    
    if (!dateInput || !timeInput || !dateInput.value || !timeInput.value) {
        return false;
    }
    
    const now = new Date();
    const selectedDate = new Date(`${dateInput.value}T${timeInput.value}`);
    
    if (selectedDate <= now) {
        // 設置為過去時間，自動調整為未來時間
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(selectedDate.getHours(), selectedDate.getMinutes(), 0, 0);
        
        // 更新表單值
        dateInput.value = tomorrow.toISOString().split('T')[0];
        
        // 顯示已自動調整的提示
        document.getElementById('time-auto-adjusted').classList.remove('d-none');
        
        // 移除錯誤提示
        dateInput.classList.remove('is-invalid');
        timeInput.classList.remove('is-invalid');
        document.getElementById('date-feedback').style.display = 'none';
        document.getElementById('time-feedback').style.display = 'none';
        
        // 檢查是否有排程衝突
        checkScheduleConflicts(tomorrow);
        return true;
    } else {
        // 時間有效
        dateInput.classList.remove('is-invalid');
        timeInput.classList.remove('is-invalid');
        document.getElementById('date-feedback').style.display = 'none';
        document.getElementById('time-feedback').style.display = 'none';
        document.getElementById('time-auto-adjusted').classList.add('d-none');
        
        // 檢查是否有排程衝突
        checkScheduleConflicts(selectedDate);
        return true;
    }
}

// 獲取下一個有效的日期（考慮時區）
function getNextValidDate(date, hour, minute) {
    // 創建基於本地時區的日期
    const localDate = new Date();
    
    // 設置指定的時間
    localDate.setHours(hour, minute, 0, 0);
    
    // 如果指定時間已過，設為明天
    if (localDate <= new Date()) {
        localDate.setDate(localDate.getDate() + 1);
    }
    
    return localDate;
}

// 獲取指定星期幾的下一個有效日期
function getNextDayOfWeek(dayOfWeek) {
    const now = new Date();
    const today = now.getDay(); // 0-6 (週日-週六)
    const hour = now.getHours();
    const minute = now.getMinutes();
    
    let daysToAdd = (dayOfWeek - today) % 7;
    
    // 如果是今天，且當前時間已過選擇的時間，則加7天
    if (daysToAdd === 0 && (hour > parseInt(document.getElementById('weekly-time').value.split(':')[0]) || 
                           (hour === parseInt(document.getElementById('weekly-time').value.split(':')[0]) && 
                            minute >= parseInt(document.getElementById('weekly-time').value.split(':')[1])))) {
        daysToAdd = 7;
    } else if (daysToAdd < 0) {
        // 如果日期已過，跳到下週
        daysToAdd += 7;
    }
    
    const nextDate = new Date();
    nextDate.setDate(now.getDate() + daysToAdd);
    return nextDate;
}

// 輔助函數：找出當前日期之後的下一個最近的選中星期幾
function findNextDate(currentDay, selectedDays, hour, minute) {
    // 獲取今天的基本日期資訊
    const today = new Date();
    
    console.log(`findNextDate - 今天日期: ${today.toLocaleString()}, 星期幾: ${currentDay}`);
    console.log(`findNextDate - 選擇的星期幾: ${selectedDays.join(', ')}`);
    
    // 存儲所有可能的下一個日期
    const possibleDates = [];
    
    // 遍歷所有選中的星期幾
    for (const day of selectedDays) {
        // 計算到下一個該星期幾的天數差
        let dayDiff = (day - currentDay + 7) % 7;
        
        // 如果是今天但時間已過，則設為下週同一天
        if (dayDiff === 0 && !isTimeInFuture(hour, minute)) {
            dayDiff = 7;
        }
        
        // 創建新日期（避免使用加法計算日期，防止月底溢出問題）
        const targetDate = new Date(today);
        targetDate.setDate(today.getDate() + dayDiff);
        targetDate.setHours(hour, minute, 0, 0);
        
        console.log(`findNextDate - 目標星期${day}的下一個日期: ${targetDate.toLocaleString()}, 相差${dayDiff}天`);
        
        // 記錄這個日期
        possibleDates.push({
            date: targetDate,
            diff: dayDiff
        });
    }
    
    // 按天數差排序，選出最早的日期
    possibleDates.sort((a, b) => a.diff - b.diff);
    const resultDate = possibleDates[0].date;
    
    console.log(`findNextDate - 最終選擇的日期: ${resultDate.toLocaleString()}, 星期: ${resultDate.getDay()}`);
    
    return resultDate;
}

// 檢查指定時間是否在當前時間之後
function isTimeInFuture(hour, minute) {
    const now = new Date();
    return (now.getHours() < hour || (now.getHours() === hour && now.getMinutes() < minute));
}

// 獲取一組星期幾中下一個最近的有效日期
function getNextValidDayFromWeekdays(selectedDays) {
    if (!selectedDays || selectedDays.length === 0) return null;
    
    console.log(`getNextValidDayFromWeekdays - 開始計算下一個有效日期`);
    
    // 獲取當前日期和時間，並確保不會存在引用問題
    const now = new Date();
    const today = now.getDay(); // 0-6 (週日-週六)
    console.log(`getNextValidDayFromWeekdays - 今天: ${now.toLocaleString()}, 星期: ${today}`);
    
    // 獲取選擇的時間
    const selectedTime = document.getElementById('weekly-time').value.split(':');
    const selectedHour = parseInt(selectedTime[0]);
    const selectedMinute = parseInt(selectedTime[1]);
    console.log(`getNextValidDayFromWeekdays - 選擇的時間: ${selectedHour}:${selectedMinute}`);
    
    // 檢查今天是否為選中的星期幾，且時間未過
    const todayIsSelected = selectedDays.includes(today);
    const timeIsFuture = isTimeInFuture(selectedHour, selectedMinute);
    
    console.log(`getNextValidDayFromWeekdays - 今天是否被選中: ${todayIsSelected}, 時間是否未來: ${timeIsFuture}`);
    
    // 如果今天被選中且時間未過，使用今天
    if (todayIsSelected && timeIsFuture) {
        const resultDate = new Date();
        resultDate.setHours(selectedHour, selectedMinute, 0, 0);
        
        console.log(`getNextValidDayFromWeekdays - 使用今天作為執行日期: ${resultDate.toLocaleString()}`);
        return resultDate;
    } else {
        // 否則找到下一個最近的日期
        return findNextDate(today, selectedDays, selectedHour, selectedMinute);
    }
}

// 獲取一組月份日期中下一個最近的有效日期
function getNextValidDayFromMonthDays(selectedDays) {
    if (!selectedDays || selectedDays.length === 0) return null;
    
    const now = new Date();
    const today = now.getDate();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    const selectedTime = document.getElementById('monthly-time').value.split(':');
    const selectedHour = parseInt(selectedTime[0]);
    const selectedMinute = parseInt(selectedTime[1]);
    
    // 檢查今天是否在選中的日期內，且時間未過
    const todayIsSelected = selectedDays.includes(today);
    const timeIsFuture = now.getHours() < selectedHour || 
                         (now.getHours() === selectedHour && now.getMinutes() < selectedMinute);
    
    if (todayIsSelected && timeIsFuture) {
        // 今天是選中的日期，且時間未過，則使用今天
        const result = new Date();
        result.setHours(selectedHour, selectedMinute, 0, 0);
        return result;
    }
    
    // 找出本月未來的有效日期
    const futureDays = selectedDays.filter(day => day > today);
    
    if (futureDays.length > 0) {
        // 本月有未來的日期
        const nextDay = Math.min(...futureDays);
        const result = new Date(currentYear, currentMonth, nextDay);
        result.setHours(selectedHour, selectedMinute, 0, 0);
        return result;
    } else {
        // 本月沒有未來的日期，跳到下個月的最早日期
        const nextDay = Math.min(...selectedDays);
        // 檢查日期是否有效（避免2月30日等無效日期）
        let nextMonth = (currentMonth + 1) % 12;
        let nextYear = currentYear + (nextMonth === 0 ? 1 : 0);
        
        // 確保日期有效
        let maxDaysInMonth = new Date(nextYear, nextMonth + 1, 0).getDate();
        let validNextDay = Math.min(nextDay, maxDaysInMonth);
        
        const result = new Date(nextYear, nextMonth, validNextDay);
        result.setHours(selectedHour, selectedMinute, 0, 0);
        return result;
    }
}

// 驗證並調整每日排程時間
function validateDailyTime() {
    const timeInput = document.getElementById('daily-time');
    const weekdayOnly = document.getElementById('daily-weekday-only').checked;
    
    if (!timeInput || !timeInput.value) return false;
    
    const now = new Date();
    const selectedTime = timeInput.value.split(':');
    const selectedHour = parseInt(selectedTime[0]);
    const selectedMinute = parseInt(selectedTime[1]);
    
    // 檢查今天的該時間是否已過
    if (now.getHours() > selectedHour || (now.getHours() === selectedHour && now.getMinutes() >= selectedMinute)) {
        // 今天此時間已過，設置為明天
        let tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(selectedHour, selectedMinute, 0, 0);
        
        // 如果僅工作日執行，且明天是週末，則調整為下週一
        if (weekdayOnly) {
            const tomorrowDay = tomorrow.getDay();
            if (tomorrowDay === 0) { // 週日
                tomorrow.setDate(tomorrow.getDate() + 1); // 調整為週一
            } else if (tomorrowDay === 6) { // 週六
                tomorrow.setDate(tomorrow.getDate() + 2); // 調整為週一
            }
        }
        
        // 顯示已自動調整的提示
        document.getElementById('daily-auto-adjusted').classList.remove('d-none');
        document.getElementById('daily-auto-adjusted').textContent = `今日此時間已過，將從 ${formatDate(tomorrow)} 開始執行`;
    } else {
        document.getElementById('daily-auto-adjusted').classList.add('d-none');
    }
    
    return true;
}

// 驗證並調整每週排程時間
function validateWeeklyTime() {
    const timeInput = document.getElementById('weekly-time');
    const weekdaysCheckboxes = document.querySelectorAll('input[name="weekdays"]:checked');
    
    if (!timeInput || !timeInput.value || weekdaysCheckboxes.length === 0) return false;
    
    // 獲取選中的星期幾
    const selectedDays = [];
    weekdaysCheckboxes.forEach(checkbox => {
        selectedDays.push(parseInt(checkbox.value));
    });
    
    console.log(`驗證每週排程 - 選擇的星期: ${selectedDays.join(', ')}`);
    
    // 取得下一個有效執行日期
    const nextDate = getNextValidDayFromWeekdays(selectedDays);
    
    console.log(`驗證每週排程 - 計算的下一個日期: ${nextDate ? nextDate.toLocaleString() : 'null'}`);
    
    // 保存為全域變數 (使用深複製避免引用問題)
    if (nextDate) {
        // 保存一個新的日期對象，避免引用原始對象
        window.nextValidWeeklyDate = new Date(nextDate.getTime());
        
        console.log(`驗證每週排程 - 儲存到全域變數: ${window.nextValidWeeklyDate.toLocaleString()}`);
        console.log(`日期詳情: 星期=${window.nextValidWeeklyDate.getDay()}, 日期=${window.nextValidWeeklyDate.getDate()}, 月份=${window.nextValidWeeklyDate.getMonth()+1}`);
    } else {
        window.nextValidWeeklyDate = null;
    }
    
    if (nextDate) {
        // 檢查是否為今天之後的日期
        const now = new Date();
        const isToday = nextDate.getDate() === now.getDate() && 
                        nextDate.getMonth() === now.getMonth() && 
                        nextDate.getFullYear() === now.getFullYear();
        
        if (!isToday) {
            // 如果不是今天，顯示調整提示
            const nextDateStr = formatDate(nextDate);
            document.getElementById('weekly-auto-adjusted').classList.remove('d-none');
            document.getElementById('weekly-auto-adjusted').textContent = `已自動調整為下一個執行時間: ${nextDateStr}`;
            console.log(`已設定下一個執行時間為: ${nextDateStr}`);
        } else {
            document.getElementById('weekly-auto-adjusted').classList.add('d-none');
        }
    }
    
    return true;
}

// 驗證並調整每月排程時間
function validateMonthlyTime() {
    const timeInput = document.getElementById('monthly-time');
    const daysCheckboxes = document.querySelectorAll('input[name="days_of_month"]:checked');
    
    if (!timeInput || !timeInput.value || daysCheckboxes.length === 0) return false;
    
    // 獲取選中的日期
    const selectedDays = [];
    daysCheckboxes.forEach(checkbox => {
        selectedDays.push(parseInt(checkbox.value));
    });
    
    // 取得下一個有效執行日期
    const nextDate = getNextValidDayFromMonthDays(selectedDays);
    
    // 將計算結果儲存為全域變數，確保 saveSchedule 使用相同的日期
    window.nextValidMonthlyDate = nextDate;
    
    if (nextDate) {
        // 檢查是否為今天之後的日期
        const now = new Date();
        const isToday = nextDate.getDate() === now.getDate() && 
                        nextDate.getMonth() === now.getMonth() && 
                        nextDate.getFullYear() === now.getFullYear();
        
        if (!isToday) {
            // 如果不是今天，顯示調整提示
            document.getElementById('monthly-auto-adjusted').classList.remove('d-none');
            document.getElementById('monthly-auto-adjusted').textContent = `已自動調整為下一個執行時間: ${formatDate(nextDate)}`;
        } else {
            document.getElementById('monthly-auto-adjusted').classList.add('d-none');
        }
    }
    
    return true;
}

// 格式化日期為友好的字符串
function formatDate(date) {
    if (!date) return '';
    
    return date.toLocaleString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

// 檢查排程時間衝突
function checkScheduleConflicts(date) {
    // 實際應用中，這裡會向API請求已有的排程，並檢查是否有衝突
    // 這裡使用模擬數據
    const warningElement = document.getElementById('datetime-warning');
    const conflictCountElement = document.getElementById('conflict-count');
    
    // 模擬：有50%機率出現衝突
    const hasConflict = Math.random() > 0.5;
    const conflictCount = hasConflict ? Math.floor(Math.random() * 5) + 1 : 0;
    
    if (conflictCount > 0) {
        conflictCountElement.textContent = conflictCount;
        warningElement.classList.remove('d-none');
    } else {
        warningElement.classList.add('d-none');
    }
}

// 驗證整個排程表單
function validateScheduleForm() {
    const scheduleType = document.getElementById('schedule-type').value;
    const scriptSelect = document.getElementById('script-select');
    
    // 檢查腳本選擇
    if (!scriptSelect.value) {
        scriptSelect.classList.add('is-invalid');
        alert('請選擇要執行的腳本');
        return false;
    }
    
    // 根據排程類型進行特定驗證
    switch(scheduleType) {
        case 'immediate':
            // 立即執行不需驗證
            return true;
        case 'once':
            return validateDateTime();
        case 'minute':
            const intervalField = document.getElementById('interval-minutes');
            if (!intervalField || !intervalField.value || intervalField.value < 5) {
                intervalField.classList.add('is-invalid');
                alert('間隔分鐘數不可小於5分鐘');
                return false;
            }
            return true;
        case 'daily':
            const dailyTimeField = document.getElementById('daily-time');
            if (!dailyTimeField || !dailyTimeField.value) {
                dailyTimeField.classList.add('is-invalid');
                alert('請選擇每日執行時間');
                return false;
            }
            validateDailyTime(); // 自動調整時間顯示
            return true;
        case 'weekly':
            const weekdaysField = document.querySelectorAll('input[name="weekdays"]:checked');
            const weeklyTimeField = document.getElementById('weekly-time');
            if (!weeklyTimeField || !weeklyTimeField.value) {
                weeklyTimeField.classList.add('is-invalid');
                alert('請選擇每週執行時間');
                return false;
            }
            if (weekdaysField.length === 0) {
                alert('請至少選擇一個星期幾');
                return false;
            }
            validateWeeklyTime(); // 自動調整時間顯示
            return true;
        case 'monthly':
            const daysField = document.querySelectorAll('input[name="days_of_month"]:checked');
            const monthlyTimeField = document.getElementById('monthly-time');
            if (!monthlyTimeField || !monthlyTimeField.value) {
                monthlyTimeField.classList.add('is-invalid');
                alert('請選擇每月執行時間');
                return false;
            }
            if (daysField.length === 0) {
                alert('請至少選擇一個月份日期');
                return false;
            }
            validateMonthlyTime(); // 自動調整時間顯示
            return true;
        default:
            return true;
    }
}

// 重置排程表單
function resetScheduleForm() {
    document.getElementById('scheduleForm').reset();
    document.querySelectorAll('.is-invalid').forEach(el => {
        el.classList.remove('is-invalid');
    });
    document.querySelectorAll('.invalid-feedback').forEach(el => {
        el.style.display = 'none';
    });
    document.getElementById('datetime-warning').classList.add('d-none');
    updateFormFields(document.getElementById('schedule-type').value);
}

// 保存排程
function saveSchedule() {
    // 取得表單資料
    const scriptName = document.getElementById('script-select').value;
    const scheduleType = document.getElementById('schedule-type').value;
    const formData = {
        script_name: scriptName,
        schedule_type: scheduleType,
        description: document.getElementById('schedule-description') ? 
                    document.getElementById('schedule-description').value : '立即執行測試'
    };
    
    // 根據排程類型收集額外資料
    switch(scheduleType) {
        case 'once':
            const dateField = document.getElementById('execution-date');
            const timeField = document.getElementById('execution-time');
            // 創建完整的日期時間物件以確保正確的時區處理
            const onceDateTime = new Date(`${dateField.value}T${timeField.value}`);
            // 使用 ISO 格式但保留本地時間
            formData.execution_time = convertToServerTime(onceDateTime);
            break;
            
        case 'minute':
            const intervalField = document.getElementById('interval-minutes');
            formData.interval_minutes = parseInt(intervalField.value);
            break;
            
        case 'daily':
            const dailyTimeField = document.getElementById('daily-time');
            const weekdayOnly = document.getElementById('daily-weekday-only').checked;
            // 使用當前日期建立完整的日期時間物件
            const dailyDateTime = new Date();
            const [dailyHours, dailyMinutes] = dailyTimeField.value.split(':');
            dailyDateTime.setHours(parseInt(dailyHours), parseInt(dailyMinutes), 0, 0);
            
            // 如果時間已過，自動調整到明天
            if (dailyDateTime <= new Date()) {
                dailyDateTime.setDate(dailyDateTime.getDate() + 1);
            }
            
            // 處理僅工作日執行的情況
            if (weekdayOnly) {
                while (dailyDateTime.getDay() === 0 || dailyDateTime.getDay() === 6) {
                    dailyDateTime.setDate(dailyDateTime.getDate() + 1);
                }
            }
            
            formData.execution_time = convertToServerTime(dailyDateTime);
            formData.weekday_only = weekdayOnly;
            break;
            
        case 'weekly':
            const weekdaysField = document.querySelectorAll('input[name="weekdays"]:checked');
            const weeklyTimeField = document.getElementById('weekly-time');
            const weekdays = [];
            weekdaysField.forEach(el => weekdays.push(parseInt(el.value)));
            formData.weekdays = weekdays;
            
            console.log("==================== 儲存每週排程 ====================");
            
            // 使用之前驗證時計算好的日期
            if (window.nextValidWeeklyDate) {
                // 深複製日期，避免引用問題
                const savedDate = new Date(window.nextValidWeeklyDate.getTime());
                
                console.log(`使用已計算的執行時間: ${savedDate.toLocaleString()}`);
                console.log(`日期詳細資訊: 星期=${savedDate.getDay()}, 日期=${savedDate.getDate()}, 月份=${savedDate.getMonth()+1}, 年=${savedDate.getFullYear()}`);
                
                // 驗證日期是否在選擇的星期幾內
                const savedDayOfWeek = savedDate.getDay(); // 0-6, 週日-週六
                if (!weekdays.includes(savedDayOfWeek)) {
                    console.warn(`警告: 計算的日期星期${savedDayOfWeek}不在選擇的星期列表${weekdays}中`);
                    // 重新計算，以防出錯
                    const recalculatedDate = getNextValidDayFromWeekdays(weekdays);
                    if (recalculatedDate) {
                        savedDate.setTime(recalculatedDate.getTime());
                        console.log(`已重新計算: ${savedDate.toLocaleString()}, 星期=${savedDate.getDay()}`);
                    }
                }
                
                // 轉換為伺服器格式 (不使用 toISOString，避免時區問題)
                formData.execution_time = convertToServerTime(savedDate);
                
                console.log(`發送到伺服器的時間: ${formData.execution_time}`);
            } else {
                console.error('未找到預先計算的執行時間');
                
                // 嘗試重新計算
                const revalidated = validateWeeklyTime();
                if (revalidated && window.nextValidWeeklyDate) {
                    const newDate = new Date(window.nextValidWeeklyDate.getTime());
                    formData.execution_time = convertToServerTime(newDate);
                    console.log(`重新計算後的執行時間: ${formData.execution_time}`);
                } else {
                    alert('無法計算有效的執行時間，請重新檢查設定');
                    return;
                }
            }
            break;
            
        case 'monthly':
            const daysField = document.querySelectorAll('input[name="days_of_month"]:checked');
            const monthlyTimeField = document.getElementById('monthly-time');
            const days = [];
            daysField.forEach(el => days.push(parseInt(el.value)));
            formData.days_of_month = days;
            
            // 使用全域變數中的預先計算好的日期，確保與顯示一致
            if (window.nextValidMonthlyDate) {
                formData.execution_time = convertToServerTime(window.nextValidMonthlyDate);
            } else {
                // 如果沒有預先計算的值，才重新計算
                const nextValidMonthDate = getNextValidDayFromMonthDays(days);
                
                if (nextValidMonthDate) {
                    formData.execution_time = convertToServerTime(nextValidMonthDate);
                } else {
                    throw new Error('無法計算有效的執行時間');
                }
            }
            break;
    }
    
    // 顯示載入狀態
    const saveButton = document.getElementById('save-schedule');
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 處理中...';
    saveButton.disabled = true;
    
    // 發送到後端API
    fetch('/api/schedules', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 成功建立排程
            console.log('排程建立成功:', data);
            // 關閉彈窗
            const modal = document.getElementById('scheduleModal');
            if (modal && bootstrap) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) bsModal.hide();
            }
            // 重新載入頁面
            window.location.reload();
        } else {
            // 顯示錯誤訊息
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;
            alert('建立排程失敗: ' + (data.message || '未知錯誤'));
        }
    })
    .catch(error => {
        console.error('請求錯誤:', error);
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
        alert('發生錯誤，請稍後再試');
    });
}

// 將日期轉換為服務器所需的格式，保留本地時間
function convertToServerTime(date) {
    // 直接使用 Date 對象的本地時間組件，避免任何隱含的時區轉換
    const y = date.getFullYear();
    const m = date.getMonth() + 1; // 注意 JavaScript 月份是 0-11
    const d = date.getDate();
    const h = date.getHours();
    const min = date.getMinutes();
    
    // 格式化為兩位數
    const padZero = (num) => num.toString().padStart(2, '0');
    
    // 構建日期字符串，確保每個組件都是兩位數格式
    return `${y}-${padZero(m)}-${padZero(d)}T${padZero(h)}:${padZero(min)}:00`;
} 