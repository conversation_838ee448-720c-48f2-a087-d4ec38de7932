from datetime import datetime
from .. import db

class ExecutionLog(db.Model):
    """排程執行日誌模型"""
    
    __tablename__ = 'execution_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    schedule_id = db.Column(db.Integer, db.<PERSON>ey('schedules.id'), nullable=False)
    status = db.Column(db.String(20), nullable=False)  # success, error, running
    start_time = db.Column(db.DateTime, nullable=False, default=datetime.now)
    end_time = db.Column(db.DateTime)
    output = db.Column(db.Text)
    error_message = db.Column(db.Text)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    
    def to_dict(self):
        """轉換為字典格式"""
        return {
            'id': self.id,
            'schedule_id': self.schedule_id,
            'status': self.status,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'output': self.output,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def create_log(cls, schedule_id, status, output=None, error_message=None):
        """創建執行日誌"""
        log = cls(
            schedule_id=schedule_id,
            status=status,
            output=output,
            error_message=error_message
        )
        db.session.add(log)
        db.session.commit()
        return log
    
    def complete(self, status, output=None, error_message=None):
        """完成執行日誌"""
        self.status = status
        self.end_time = datetime.now()
        if output:
            self.output = output
        if error_message:
            self.error_message = error_message
        db.session.commit() 