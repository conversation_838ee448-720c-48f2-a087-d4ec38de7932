/* 排程總覽樣式 */
.schedule-overview {
    padding: 1rem;
}

/* 系統資源監控 */
.system-resources {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.resource-meter {
    margin-bottom: 0.5rem;
}

.resource-meter label {
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    color: #64748b;
}

.progress {
    height: 8px;
    background-color: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* 排程表格 */
.schedule-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
}

.schedule-table th {
    background-color: #cbd5e1;
    padding: 12px;
    font-weight: 600;
    text-align: left;
    color: #1e293b;
}

.schedule-table td {
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
}

.schedule-table tr:nth-child(even) {
    background-color: #f8fafc;
}

.schedule-table tr:hover {
    background-color: #e0e7ef;
}

/* 狀態徽章 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.875rem;
    gap: 4px;
}

.badge-success {
    background-color: #d9f99d;
    color: #166534;
}

.badge-warning {
    background-color: #fde68a;
    color: #92400e;
}

.badge-danger {
    background-color: #fecaca;
    color: #991b1b;
}

/* 操作按鈕 */
.action-btn {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    margin-right: 4px;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.btn-success {
    background-color: #e0f2f1;
    color: #166534;
}

.btn-warning {
    background-color: #fff8e1;
    color: #92400e;
}

.btn-danger {
    background-color: #ffebee;
    color: #991b1b;
}

/* 輸出顯示區域 */
.output-container {
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    margin: 8px 0;
}

.output-tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
    padding: 8px 16px;
    border: none;
    background: none;
    cursor: pointer;
    color: #64748b;
}

.tab-btn.active {
    border-bottom: 2px solid #3b82f6;
    color: #3b82f6;
}

.tab-content {
    padding: 16px;
}

.log-output {
    margin: 0;
    padding: 8px;
    background-color: #f8fafc;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 0.875rem;
}

/* 檔案列表 */
.file-list {
    display: grid;
    gap: 8px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 8px;
    background-color: #f8fafc;
    border-radius: 4px;
    gap: 8px;
}

.file-icon {
    color: #64748b;
}

.file-actions {
    margin-left: auto;
}

/* 進度條 */
.progress-container {
    margin-top: 4px;
    height: 4px;
    background-color: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: currentColor;
    transition: width 0.3s ease;
} 