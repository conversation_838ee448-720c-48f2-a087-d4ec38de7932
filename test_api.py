import requests
import json
import time

# 測試建立排程
def test_create_schedule():
    url = "http://localhost:5000/api/schedules"
    data = {
        "script_name": "test_script.py",
        "schedule_type": "immediate",
        "description": "測試新的前端功能 - " + str(time.time()).split('.')[0]  # 添加時間戳以區分
    }
    
    response = requests.post(url, json=data)
    print("建立排程結果:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    
    # 返回創建的排程 ID
    return response.json().get("data", {}).get("id")

# 測試獲取腳本列表
def test_get_scripts():
    url = "http://localhost:5000/api/scripts"
    response = requests.get(url)
    print("獲取腳本列表結果:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))

# 測試獲取所有排程
def test_get_schedules():
    url = "http://localhost:5000/api/schedules"
    response = requests.get(url)
    data = response.json()
    print("獲取排程列表結果:")
    print("排程總數:", len(data.get("data", [])))
    for i, schedule in enumerate(data.get("data", []), 1):
        print(f"\n排程 {i}:")
        print(json.dumps(schedule, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    # 創建新排程
    schedule_id = test_create_schedule()
    print(f"\n成功創建排程，ID: {schedule_id}\n")
    
    # 獲取所有排程
    test_get_schedules() 