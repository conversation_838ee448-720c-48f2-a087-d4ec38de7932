# Python 腳本管理與排程系統 - 文檔索引

*文檔版本：1.0.0*  
*最後更新日期：2025-03-27*

## 文檔概述

此目錄包含 Python 腳本管理與排程系統的所有技術文檔。文檔分為多個類別，包括設計規範、使用指南、API 文檔和架構說明等。

## 架構文檔

- [架構結構說明](Architecture_Structure.md) - 詳細說明系統架構、問題分析和改進建議
- [架構實現指南](Architecture_Implementation_Guide.md) - 如何實現架構改進的具體指南和代碼示例

## API 文檔

- [API 路由規範](API_Routes_Specification.md) - API 路徑、請求格式和回應格式的詳細規範
- [API 遷移指南](API_Migration_Guide.md) - 將舊 API 遷移到新版本的指南和注意事項

## 設計文檔

- [腳本管理器設計](Python_Script_Manager_Design.md) - 腳本管理器的設計和功能說明
- [排程系統設計](scheduling_system_design.md) - 排程系統的設計和功能說明
- [排程系統最終設計](Python_Script_Scheduler_Final_Design.md) - 排程系統的最終設計文檔
- [腳本上傳設計](script_upload.md) - 腳本上傳功能的設計和實現說明

## UI 文檔

- [UI 風格指南](Python_Script_Scheduler_UI_Style.md) - 使用者界面的風格和設計規範

## 開發指南

- [腳本管理設計](script_manager_design.md) - 腳本管理部分的開發指南和最佳實踐

## 文檔更新歷史

| 日期 | 文檔名稱 | 變更摘要 |
|------|----------|----------|
| 2025-03-27 | Architecture_Structure.md | 初始版本 |
| 2025-03-27 | Architecture_Implementation_Guide.md | 初始版本 |
| 2025-03-27 | README.md | 初始版本 |
| 2025-03-26 | scheduling_system_design.md | 排程系統設計更新 |
| 2025-03-25 | Python_Script_Scheduler_Final_Design.md | 最終設計稿完成 |
| 2025-03-24 | script_manager_design.md | 腳本管理設計更新 |
| 2025-03-21 | script_upload.md | 腳本上傳功能說明 |
| 2025-03-19 | system_design.md | 系統設計初稿 |
| 2025-03-18 | Python_Script_Manager_Design.md | 腳本管理器設計更新 |
| 2025-03-17 | Python_Script_Scheduler_UI_Style.md | UI 風格指南初稿 |

## 如何貢獻文檔

1. 所有文檔應使用 Markdown 格式編寫
2. 文檔命名應使用英文，並遵循 `Pascal_Case` 格式
3. 所有文檔應包含版本號和最後更新日期
4. 重大更新應在文檔更新歷史中記錄
5. 新增的架構文檔需同時提供中文和英文版本 