# -*- coding: utf-8 -*-
from flask import jsonify, current_app
import psutil
from app.utils.api_logger import api_logger, APILogger
from app.api.v1 import api_v1

@api_v1.route('/system/resources', methods=['GET'])
@APILogger.log_request
@APILogger.monitor_api_health('系統資源API')
def get_system_resources():
    """獲取系統資源使用狀況"""
    try:
        api_logger.info("獲取系統資源使用情況 - API v1")
        
        # 方式1：嘗試從服務工廠獲取系統監控服務
        try:
            from app.services import get_service, has_service
            
            if has_service('system_monitor'):
                system_monitor = get_service('system_monitor')
                resources = system_monitor.get_system_resources()
                api_logger.debug("使用系統監控服務獲取資源信息")
            else:
                api_logger.warning("系統監控服務未註冊，嘗試其他方法")
                raise KeyError("系統監控服務未註冊")
        except Exception as service_error:
            api_logger.warning(f"無法使用系統監控服務: {str(service_error)}，嘗試使用app.system_monitor")
            
            # 方式2：嘗試使用 current_app.system_monitor
            if hasattr(current_app, 'system_monitor') and current_app.system_monitor:
                try:
                    resources = current_app.system_monitor.get_system_resources()
                    api_logger.debug("使用app.system_monitor獲取資源信息")
                except Exception as app_error:
                    api_logger.warning(f"使用app.system_monitor失敗: {str(app_error)}，使用psutil降級方案")
                    resources = get_resources_fallback()
            else:
                # 方式3：降級處理，直接使用psutil
                api_logger.warning("未找到system_monitor服務，使用psutil降級方案")
                resources = get_resources_fallback()
        
        api_logger.info('成功獲取系統資源：' + str(resources))
        return jsonify({
            'status': 'success',
            'data': resources
        })
    except Exception as e:
        api_logger.error(f'獲取系統資源使用狀況失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': f'Failed to get system resources: {str(e)}'
        }), 500

def get_resources_fallback():
    """使用psutil直接獲取系統資源的降級方案"""
    api_logger.debug("使用psutil降級方案獲取系統資源")
    try:
        # 獲取CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # 獲取記憶體使用率
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 嘗試獲取排程數量
        try:
            from app.models.schedule import Schedule
            # 嘗試直接從數據庫查詢
            schedule_count = Schedule.query.filter_by(is_active=True).count()
        except Exception as db_error:
            api_logger.warning(f"從數據庫獲取排程數量失敗: {str(db_error)}")
            # 嘗試從schedule_manager獲取
            try:
                if hasattr(current_app, 'schedule_manager') and current_app.schedule_manager:
                    schedules = current_app.schedule_manager.get_all_schedules()
                    schedule_count = len([s for s in schedules if s.is_active])
                else:
                    schedule_count = 0
            except Exception as service_error:
                api_logger.warning(f"從服務獲取排程數量失敗: {str(service_error)}")
                schedule_count = 0
            
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory_percent,
            'schedule_count': schedule_count,
            'max_schedules': current_app.config.get('MAX_SCHEDULES', 10)
        }
    except Exception as e:
        api_logger.error(f"降級獲取系統資源失敗: {str(e)}")
        # 返回默認值
        return {
            'cpu_percent': 0,
            'memory_percent': 0,
            'schedule_count': 0,
            'max_schedules': 10
        }

@api_v1.route('/system/processes', methods=['GET'])
@APILogger.log_request
@APILogger.monitor_api_health('系統進程API')
def get_processes():
    """獲取所有進程列表"""
    try:
        api_logger.info("獲取系統進程列表")
        
        # 嘗試使用系統監控服務
        try:
            from app.services import get_service, has_service
            
            if has_service('system_monitor'):
                system_monitor = get_service('system_monitor')
                processes = system_monitor.get_all_processes()
                api_logger.debug("使用系統監控服務獲取進程列表")
            else:
                raise KeyError("系統監控服務未註冊")
        except Exception as service_error:
            api_logger.warning(f"無法使用系統監控服務: {str(service_error)}，使用psutil降級方案")
            
            # 降級處理，直接使用psutil
            try:
                processes = []
                for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'create_time', 'status']):
                    try:
                        pinfo = proc.as_dict(attrs=['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'create_time', 'status'])
                        processes.append(pinfo)
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass
                
                # 按CPU使用率排序
                processes = sorted(processes, key=lambda p: p.get('cpu_percent', 0), reverse=True)
                # 僅返回前50個進程
                processes = processes[:50]
            except Exception as psutil_error:
                api_logger.error(f"使用psutil獲取進程列表失敗: {str(psutil_error)}")
                return jsonify({
                    'status': 'error',
                    'message': f'Failed to get process list: {str(psutil_error)}'
                }), 500
        
        return jsonify({
            'status': 'success',
            'data': processes
        })
    except Exception as e:
        api_logger.error(f'獲取進程列表失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': f'Failed to get processes: {str(e)}'
        }), 500

@api_v1.route('/system/process/<int:pid>/resources', methods=['GET'])
@APILogger.log_request
@APILogger.monitor_api_health('系統進程資源API')
def get_process_resources(pid):
    """獲取特定進程的資源使用狀況"""
    try:
        api_logger.info(f"獲取進程資源使用狀況: PID={pid}")
        
        # 嘗試使用系統監控服務
        try:
            from app.services import get_service, has_service
            
            if has_service('system_monitor'):
                system_monitor = get_service('system_monitor')
                resources = system_monitor.get_process_resources(pid)
                api_logger.debug("使用系統監控服務獲取進程資源")
            else:
                raise KeyError("系統監控服務未註冊")
        except Exception as service_error:
            api_logger.warning(f"無法使用系統監控服務: {str(service_error)}，使用psutil降級方案")
            
            # 降級處理，直接使用psutil
            try:
                process = psutil.Process(pid)
                resources = {
                    "pid": pid,
                    "name": process.name(),
                    "status": process.status(),
                    "cpu_percent": process.cpu_percent(interval=0.1),
                    "memory_percent": process.memory_percent(),
                    "create_time": process.create_time(),
                    "username": process.username()
                }
            except psutil.NoSuchProcess:
                api_logger.warning(f"進程不存在: PID={pid}")
                return jsonify({
                    "status": "error",
                    "message": f"Process with PID {pid} does not exist"
                }), 404
            except (psutil.AccessDenied, Exception) as e:
                api_logger.error(f"無法訪問進程信息: PID={pid}, 錯誤: {str(e)}")
                return jsonify({
                    "status": "error",
                    "message": f"Cannot access process info for PID {pid}: {str(e)}"
                }), 403
        
        return jsonify({
            'status': 'success',
            'data': resources
        })
        
    except Exception as e:
        api_logger.error(f"獲取進程資源時發生錯誤: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Failed to get process resources: {str(e)}"
        }), 500 