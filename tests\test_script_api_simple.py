#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腳本管理API簡單測試
"""
import unittest
import json
from app import create_app, db
from app.models import Script

class TestScriptAPISimple(unittest.TestCase):
    """腳本管理API簡單測試類"""
    
    def setUp(self):
        """測試前準備"""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 建立測試資料庫
        db.create_all()
    
    def tearDown(self):
        """測試後清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_get_scripts_empty(self):
        """測試獲取空腳本列表"""
        response = self.client.get('/script/')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertIsInstance(data['data'], list)
    
    def test_get_scripts_with_data(self):
        """測試獲取包含數據的腳本列表"""
        # 創建腳本記錄
        script = Script(
            name='test_script.py',
            description='測試腳本',
            file_path='/fake/path/test_script.py'
        )
        db.session.add(script)
        db.session.commit()
        
        response = self.client.get('/script/')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertTrue(len(data['data']) > 0)
        
        # 檢查腳本信息
        script_names = [s['name'] for s in data['data']]
        self.assertIn('test_script.py', script_names)
    
    def test_get_script_info(self):
        """測試獲取腳本詳細信息"""
        # 創建腳本記錄
        script = Script(
            name='info_test.py',
            description='信息測試腳本',
            file_path='/fake/path/info_test.py'
        )
        db.session.add(script)
        db.session.commit()
        
        response = self.client.get(f'/script/{script.name}')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['data']['name'], 'info_test.py')
        self.assertEqual(data['data']['description'], '信息測試腳本')
    
    def test_get_script_info_not_found(self):
        """測試獲取不存在腳本的信息"""
        response = self.client.get('/script/nonexistent.py')
        self.assertEqual(response.status_code, 404)
    
    def test_update_script_metadata(self):
        """測試更新腳本元數據"""
        # 創建腳本記錄
        script = Script(
            name='update_test.py',
            description='原始描述',
            file_path='/fake/path/update_test.py'
        )
        db.session.add(script)
        db.session.commit()
        
        # 更新元數據
        update_data = {
            'description': '更新後的描述',
            'is_active': False
        }
        
        response = self.client.put(f'/script/{script.name}',
                                 data=json.dumps(update_data),
                                 content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'success')
        
        # 驗證更新
        updated_script = Script.query.filter_by(name='update_test.py').first()
        self.assertEqual(updated_script.description, '更新後的描述')
        self.assertFalse(updated_script.is_active)
    
    def test_delete_script(self):
        """測試刪除腳本"""
        # 創建腳本記錄
        script = Script(
            name='delete_test.py',
            description='刪除測試腳本',
            file_path='/fake/path/delete_test.py'
        )
        db.session.add(script)
        db.session.commit()
        
        response = self.client.delete(f'/script/{script.name}')
        self.assertEqual(response.status_code, 200)
        
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'success')
        
        # 驗證腳本已刪除
        deleted_script = Script.query.filter_by(name='delete_test.py').first()
        self.assertIsNone(deleted_script)
    
    def test_delete_script_not_found(self):
        """測試刪除不存在的腳本"""
        response = self.client.delete('/script/nonexistent.py')
        self.assertEqual(response.status_code, 404)

if __name__ == '__main__':
    unittest.main()
