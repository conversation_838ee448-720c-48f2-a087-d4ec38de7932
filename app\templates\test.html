<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 測試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>API 測試頁面</h1>

    <div class="test-section">
        <h2>基本 JavaScript 測試</h2>
        <button onclick="testBasicJS()">測試基本 JavaScript</button>
        <div id="basic-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>排程 API 測試</h2>
        <button onclick="testSchedulesAPI()">測試排程 API</button>
        <div id="schedules-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>腳本 API 測試</h2>
        <button onclick="testScriptsAPI()">測試腳本 API</button>
        <div id="scripts-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>系統資源 API 測試</h2>
        <button onclick="testSystemAPI()">測試系統資源 API</button>
        <div id="system-result" class="result"></div>
    </div>

    <script>
        function testBasicJS() {
            const resultDiv = document.getElementById('basic-result');
            try {
                const now = new Date();
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ JavaScript 正常工作！\n當前時間: ${now.toISOString()}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ JavaScript 錯誤: ${error.message}`;
            }
        }

        async function testSchedulesAPI() {
            const resultDiv = document.getElementById('schedules-result');
            try {
                resultDiv.textContent = '🔄 正在測試排程 API...';

                const response = await fetch('/api/v1/schedules');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 排程 API 成功！\n狀態: ${response.status}\n數據: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 排程 API 錯誤！\n狀態: ${response.status}\n錯誤: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 排程 API 請求失敗: ${error.message}`;
            }
        }

        async function testScriptsAPI() {
            const resultDiv = document.getElementById('scripts-result');
            try {
                resultDiv.textContent = '🔄 正在測試腳本 API...';

                const response = await fetch('/script/');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 腳本 API 成功！\n狀態: ${response.status}\n數據: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 腳本 API 錯誤！\n狀態: ${response.status}\n錯誤: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 腳本 API 請求失敗: ${error.message}`;
            }
        }

        async function testSystemAPI() {
            const resultDiv = document.getElementById('system-result');
            try {
                resultDiv.textContent = '🔄 正在測試系統資源 API...';

                const response = await fetch('/api/v1/system/resources');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 系統資源 API 成功！\n狀態: ${response.status}\n數據: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 系統資源 API 錯誤！\n狀態: ${response.status}\n錯誤: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 系統資源 API 請求失敗: ${error.message}`;
            }
        }

        // 頁面載入時自動測試基本 JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 測試頁面已載入');
            testBasicJS();
        });
    </script>
</body>
</html>
