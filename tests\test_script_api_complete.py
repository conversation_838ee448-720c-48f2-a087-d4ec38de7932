#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腳本管理API完整測試
"""
import os
import unittest
import tempfile
import shutil
import json
from io import BytesIO
from app import create_app, db
from app.models import Script

class TestScriptAPIComplete(unittest.TestCase):
    """腳本管理API完整測試類"""
    
    def setUp(self):
        """測試前準備"""
        # 建立測試用應用程式
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 建立測試資料庫
        db.create_all()
        
        # 創建臨時目錄
        self.temp_dir = tempfile.mkdtemp()
        self.scripts_dir = os.path.join(self.temp_dir, 'scripts')
        self.outputs_dir = os.path.join(self.temp_dir, 'outputs')
        os.makedirs(self.scripts_dir, exist_ok=True)
        os.makedirs(self.outputs_dir, exist_ok=True)
        
        # 更新應用配置
        self.app.config['UPLOAD_FOLDER'] = self.scripts_dir
        self.app.config['OUTPUT_FOLDER'] = self.outputs_dir
        
        # 創建測試腳本文件
        self.test_script_content = '''#!/usr/bin/env python3
import time
from datetime import datetime

def main():
    print(f"測試腳本開始執行 - {datetime.now()}")
    time.sleep(0.1)
    print("測試腳本執行完成")
    return True

if __name__ == "__main__":
    result = main()
    print(f"執行結果: {result}")
'''
        
        self.test_script_path = os.path.join(self.scripts_dir, 'test_script.py')
        with open(self.test_script_path, 'w', encoding='utf-8') as f:
            f.write(self.test_script_content)
    
    def tearDown(self):
        """測試後清理"""
        # 清理臨時目錄
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        # 刪除測試資料庫
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_get_scripts_empty(self):
        """測試獲取空腳本列表"""
        response = self.client.get('/script/')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertIsInstance(data['data'], list)
    
    def test_get_scripts_with_data(self):
        """測試獲取包含數據的腳本列表"""
        # 創建腳本記錄
        script = Script(
            name='test_script.py',
            description='測試腳本',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()
        
        response = self.client.get('/script/')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertTrue(len(data['data']) > 0)
        
        # 檢查腳本信息
        script_names = [s['name'] for s in data['data']]
        self.assertIn('test_script.py', script_names)
    
    def test_upload_script_success(self):
        """測試成功上傳腳本"""
        # 準備上傳數據
        data = {
            'file': (BytesIO(self.test_script_content.encode('utf-8')), 'upload_test.py'),
            'description': '上傳測試腳本'
        }
        
        response = self.client.post('/script/', 
                                  data=data,
                                  content_type='multipart/form-data')
        
        self.assertEqual(response.status_code, 201)
        
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'success')
        self.assertIn('data', response_data)
        
        # 驗證腳本已保存到數據庫
        script = Script.query.filter_by(name='upload_test.py').first()
        self.assertIsNotNone(script)
        self.assertEqual(script.description, '上傳測試腳本')
    
    def test_upload_script_invalid_file_type(self):
        """測試上傳無效文件類型"""
        # 準備無效文件
        data = {
            'file': (BytesIO(b'invalid content'), 'test.txt'),
            'description': '無效文件測試'
        }
        
        response = self.client.post('/script/', 
                                  data=data,
                                  content_type='multipart/form-data')
        
        self.assertEqual(response.status_code, 400)
        
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'error')
        self.assertIn('只允許上傳', response_data['message'])
    
    def test_upload_script_no_file(self):
        """測試沒有文件的上傳請求"""
        response = self.client.post('/script/', 
                                  data={},
                                  content_type='multipart/form-data')
        
        self.assertEqual(response.status_code, 400)
        
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'error')
        self.assertIn('沒有選擇檔案', response_data['message'])
    
    def test_get_script_info(self):
        """測試獲取腳本詳細信息"""
        # 創建腳本記錄
        script = Script(
            name='info_test.py',
            description='信息測試腳本',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()

        response = self.client.get(f'/script/{script.name}')
        self.assertEqual(response.status_code, 200)

        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['data']['name'], 'info_test.py')
        self.assertEqual(data['data']['description'], '信息測試腳本')

    def test_get_script_info_not_found(self):
        """測試獲取不存在腳本的信息"""
        response = self.client.get('/script/nonexistent.py')
        self.assertEqual(response.status_code, 404)
    
    def test_update_script_metadata(self):
        """測試更新腳本元數據"""
        # 創建腳本記錄
        script = Script(
            name='update_test.py',
            description='原始描述',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()
        
        # 更新元數據
        update_data = {
            'description': '更新後的描述',
            'is_active': False
        }
        
        response = self.client.put(f'/script/{script.name}',
                                 data=json.dumps(update_data),
                                 content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'success')
        
        # 驗證更新
        updated_script = Script.query.filter_by(name='update_test.py').first()
        self.assertEqual(updated_script.description, '更新後的描述')
        self.assertFalse(updated_script.is_active)
    
    def test_delete_script(self):
        """測試刪除腳本"""
        # 創建腳本記錄
        script = Script(
            name='delete_test.py',
            description='刪除測試腳本',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()
        
        response = self.client.delete(f'/script/{script.name}')
        self.assertEqual(response.status_code, 200)
        
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'success')
        
        # 驗證腳本已刪除
        deleted_script = Script.query.filter_by(name='delete_test.py').first()
        self.assertIsNone(deleted_script)
    
    def test_delete_script_not_found(self):
        """測試刪除不存在的腳本"""
        response = self.client.delete('/script/nonexistent.py')
        self.assertEqual(response.status_code, 404)
    
    def test_api_error_handling(self):
        """測試API錯誤處理"""
        # 測試無效的JSON數據
        response = self.client.put('/script/test.py',
                                 data='invalid json',
                                 content_type='application/json')
        
        self.assertEqual(response.status_code, 400)

if __name__ == '__main__':
    unittest.main()
