# Python 腳本管理與排程系統 - 架構結構說明

*文檔版本：1.0.0*  
*最後更新日期：2025-03-27*

## 目錄

1. [系統概述](#系統概述)
2. [當前架構結構](#當前架構結構)
3. [問題分析](#問題分析)
4. [架構改進建議](#架構改進建議)
5. [遷移計劃](#遷移計劃)

## 系統概述

Python 腳本管理與排程系統是一個基於 Flask 的 Web 應用程序，提供 Python 腳本的上傳、管理、調度和執行功能。系統主要分為前端 UI 和後端 API 兩部分，支持用戶通過 Web 界面進行腳本管理和排程任務的創建與監控。

系統的主要功能包括：
- 腳本上傳與管理
- 排程任務創建與管理
- 腳本執行與日誌記錄
- 系統資源監控

## 當前架構結構

```
Project04/
├── app/                         # 主應用程式目錄
│   ├── __init__.py              # 應用程式初始化
│   ├── core/                    # 核心功能模組
│   ├── models/                  # 數據模型定義
│   │   ├── schedule.py          # 排程模型
│   │   ├── script.py            # 腳本模型
│   │   └── execution_log.py     # 執行日誌模型
│   ├── routes/                  # API 路由定義
│   │   ├── main_routes.py       # 主頁路由
│   │   ├── schedule_routes.py   # 排程相關路由
│   │   ├── script_routes.py     # 腳本相關路由
│   │   └── system_routes.py     # 系統相關路由
│   ├── services/                # 服務層
│   │   ├── schedule_executor.py # 排程執行器
│   │   ├── schedule_manager.py  # 排程管理器
│   │   └── script_executor.py   # 腳本執行器
│   ├── static/                  # 靜態資源
│   │   ├── css/                 # 樣式文件
│   │   └── js/                  # JavaScript 文件
│   │       ├── main.js          # 主要 JS 邏輯
│   │       ├── schedule.js      # 排程相關 JS
│   │       └── schedule_overview.js # 排程概覽 JS
│   ├── templates/               # 前端模板
│   │   ├── index.html           # 主頁模板
│   │   ├── schedules.html       # 排程列表頁
│   │   ├── schedule_detail.html # 排程詳情頁
│   │   └── components/          # 可重用組件
│   │       ├── schedule_detail.html # 排程詳情組件
│   │       ├── schedule_form.html   # 排程表單組件
│   │       └── schedule_list.html   # 排程列表組件
│   └── utils/                   # 工具函數
│       └── logger.py            # 日誌工具
├── config.py                    # 配置文件
├── docs/                        # 文檔
│   ├── API_Migration_Guide.md   # API 遷移指南
│   ├── API_Routes_Specification.md # API 路由規範
│   ├── Python_Script_Manager_Design.md # 腳本管理器設計
│   ├── Python_Script_Scheduler_Final_Design.md # 排程器設計
│   └── script_upload.md         # 腳本上傳文檔
├── logs/                        # 日誌文件
├── outputs/                     # 腳本執行輸出
├── tests/                       # 測試目錄
│   ├── test_integration.py      # 集成測試
│   ├── test_schedule_api.py     # 排程 API 測試
│   └── test_script_api.py       # 腳本 API 測試
├── uploads/                     # 上傳腳本存放
└── run.py                       # 應用入口
```

### 主要組件說明

1. **路由層 (Routes)**
   - `main_routes.py`: 處理主頁和通用 API 請求
   - `schedule_routes.py`: 處理排程相關 API 請求
   - `script_routes.py`: 處理腳本相關 API 請求
   - `system_routes.py`: 處理系統資源監控 API 請求

2. **服務層 (Services)**
   - `schedule_executor.py`: 負責執行排程任務
   - `schedule_manager.py`: 負責管理排程任務的生命週期
   - `script_executor.py`: 負責執行 Python 腳本和監控其輸出

3. **模型層 (Models)**
   - `schedule.py`: 定義排程任務數據模型
   - `script.py`: 定義腳本數據模型
   - `execution_log.py`: 定義執行日誌數據模型

4. **前端 (Templates & Static)**
   - 使用 HTML 模板和組件化設計
   - JavaScript 文件處理前端交互

## 問題分析

通過對當前架構的分析，我們發現以下幾個主要問題：

### 1. API 路徑不一致

**問題描述**：
- 腳本相關 API 使用 `/script` 作為前綴
- 排程相關 API 使用 `/schedule/api/schedule` 作為前綴
- 系統資源 API 使用 `/schedule/api/system-resources` 和 `/api/system-resources`

這種不一致性導致了 API 調用困難，前端代碼維護複雜，且不符合 RESTful API 設計原則。

### 2. 文件存儲位置不合理

**問題描述**：
- `uploads/` 和 `outputs/` 目錄直接位於項目根目錄
- 缺乏統一的數據目錄管理
- 在部署時可能導致路徑問題

### 3. 服務初始化與依賴管理混亂

**問題描述**：
- 服務之間存在複雜的依賴關係，但缺乏清晰的依賴注入機制
- 初始化順序不明確，容易出現循環依賴
- 不同服務之間的耦合度高

### 4. 測試結構不完善

**問題描述**：
- 測試文件命名和組織不符合標準實踐
- 缺少明確的單元測試、集成測試和端到端測試區分
- 測試覆蓋率可能不足

### 5. 配置管理不夠靈活

**問題描述**：
- 所有配置集中在一個文件中
- 缺乏環境特定配置分離
- 敏感配置可能直接硬編碼

## 架構改進建議

基於上述問題分析，我們建議以下改進措施：

### 1. API 路徑統一化

將所有 API 路徑重構為統一的格式：`/api/v1/{resource}`，例如：
- 腳本 API: `/api/v1/scripts`
- 排程 API: `/api/v1/schedules`
- 系統資源 API: `/api/v1/system`

同時，實現一個版本控制中間件，處理 API 版本遷移和舊路徑重定向。

### 2. 目錄結構優化

```
Project04/
├── app/
│   ├── api/                     # API 相關定義 (替代 routes)
│   │   ├── v1/                  # API 版本 1
│   │   │   ├── scripts.py       # 腳本 API
│   │   │   ├── schedules.py     # 排程 API
│   │   │   └── system.py        # 系統 API
│   │   └── middleware/          # API 中間件
│   │       └── version.py       # 版本控制中間件
│   ├── core/                    # 核心邏輯
│   ├── data/                    # 數據目錄
│   │   ├── outputs/             # 輸出文件
│   │   └── uploads/             # 上傳文件
│   ├── models/                  # 數據模型
│   ├── services/                # 服務層
│   │   ├── __init__.py          # 服務註冊
│   │   ├── executors/           # 執行器
│   │   │   ├── script.py        # 腳本執行器
│   │   │   └── schedule.py      # 排程執行器
│   │   └── managers/            # 管理器
│   │       ├── script.py        # 腳本管理器
│   │       └── schedule.py      # 排程管理器
│   ├── static/                  # 靜態資源
│   ├── templates/               # 模板
│   └── utils/                   # 工具函數
├── config/                      # 配置目錄
│   ├── __init__.py              # 配置加載
│   ├── default.py               # 默認配置
│   ├── development.py           # 開發環境配置
│   ├── production.py            # 生產環境配置
│   └── testing.py               # 測試環境配置
├── docs/                        # 文檔
│   ├── architecture/            # 架構文檔
│   ├── api/                     # API 文檔
│   └── user/                    # 用戶文檔
├── logs/                        # 日誌
├── tests/                       # 測試
│   ├── unit/                    # 單元測試
│   │   ├── models/              # 模型測試
│   │   └── services/            # 服務測試
│   ├── integration/             # 集成測試
│   │   ├── api/                 # API 測試
│   │   └── services/            # 服務集成測試
│   └── e2e/                     # 端到端測試
└── run.py                       # 應用入口
```

### 3. 服務初始化改進

**建議實施服務工廠模式**：
- 在 `app/services/__init__.py` 中實現服務註冊與工廠
- 清晰定義服務依賴關係
- 實現懶加載機制以避免循環依賴

示例代碼：
```python
# app/services/__init__.py
_services = {}

def get_service(service_name):
    """獲取服務實例"""
    if service_name not in _services:
        # 按需初始化服務
        if service_name == 'script_executor':
            from .executors.script import ScriptExecutor
            _services[service_name] = ScriptExecutor()
        elif service_name == 'schedule_manager':
            from .managers.schedule import ScheduleManager
            script_executor = get_service('script_executor')
            _services[service_name] = ScheduleManager(script_executor)
        # 其他服務類似處理...
    
    return _services[service_name]
```

### 4. 配置管理優化

- 分離不同環境的配置
- 使用環境變量覆蓋敏感配置
- 實現配置驗證機制

示例：
```python
# config/__init__.py
import os
from .default import DefaultConfig

# 根據環境加載不同配置
env = os.environ.get('FLASK_ENV', 'development')

if env == 'production':
    from .production import ProductionConfig as Config
elif env == 'testing':
    from .testing import TestingConfig as Config
else:
    from .development import DevelopmentConfig as Config

# 配置驗證
def validate_config(config):
    """驗證配置是否完整有效"""
    required_keys = [
        'SQLALCHEMY_DATABASE_URI',
        'UPLOAD_FOLDER',
        'OUTPUT_FOLDER',
        'SECRET_KEY',
    ]
    for key in required_keys:
        if not hasattr(config, key):
            raise ValueError(f"配置缺少必要項 '{key}'")
```

### 5. 測試架構優化

- 按測試類型和模塊組織測試目錄
- 增加測試固件和共享測試資源
- 實現更全面的測試覆蓋

## 遷移計劃

為了平穩遷移到新的架構，我們建議按以下階段進行：

### 第一階段：API 路徑統一和中間件實現 (2 週)

1. 實現 API 版本控制中間件
2. 為所有 API 添加統一路徑前綴
3. 保留舊路徑支持，但在日誌中添加棄用警告

### 第二階段：目錄結構重組 (2 週)

1. 創建新的目錄結構
2. 將文件遷移到新位置
3. 更新配置以適應新的文件路徑

### 第三階段：服務初始化重構 (1 週)

1. 實現服務工廠模式
2. 修改服務依賴注入方式
3. 優化初始化順序

### 第四階段：配置管理優化 (1 週)

1. 拆分配置文件
2. 實現環境特定配置
3. 添加配置驗證

### 第五階段：測試架構重組 (2 週)

1. 重組測試目錄
2. 重構測試代碼
3. 增加更多測試覆蓋

### 第六階段：文檔更新與最終驗證 (1 週)

1. 更新技術文檔
2. 進行全面測試
3. 部署到測試環境驗證 