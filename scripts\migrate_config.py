# -*- coding: utf-8 -*-
import os
import sys
import configparser
import json

def load_old_config():
    """從舊配置文件加載配置"""
    config = {}
    
    # 檢查 config.py 是否存在
    if os.path.exists('config.py'):
        print("找到 config.py 文件")
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 嘗試提取配置項
            config_items = {
                'SECRET_KEY': None,
                'SQLALCHEMY_DATABASE_URI': None,
                'UPLOAD_FOLDER': None,
                'OUTPUT_FOLDER': None,
                'MAX_SCHEDULES': None,
                'LOG_FILE': None,
                'DEBUG': None
            }
            
            for key in config_items:
                import re
                pattern = r'{}\s*=\s*[\'"](.+?)[\'"]'.format(key)
                match = re.search(pattern, content)
                if match:
                    config_items[key] = match.group(1)
                    print(f"從 config.py 提取配置: {key} = {config_items[key]}")
            
            config.update({k: v for k, v in config_items.items() if v is not None})
    
    # 檢查其他可能的配置文件
    for config_file in ['config.ini', 'settings.ini']:
        if os.path.exists(config_file):
            print(f"找到 {config_file} 文件")
            parser = configparser.ConfigParser()
            try:
                parser.read(config_file)
                for section in parser.sections():
                    for key, value in parser.items(section):
                        key = key.upper()
                        if key not in config:
                            config[key] = value
                            print(f"從 {config_file} 提取配置: {key} = {value}")
            except Exception as e:
                print(f"讀取 {config_file} 出錯: {str(e)}")
    
    return config

def update_config_files(config):
    """更新配置文件"""
    # 開發環境配置
    update_env_config('config/development.py', config, True)
    
    # 生產環境配置
    update_env_config('config/production.py', config, False)
    
    # 測試環境配置
    update_env_config('config/testing.py', config, True)
    
    print("配置文件更新完成")

def update_env_config(file_path, config, is_dev_or_test):
    """更新特定環境的配置文件"""
    # 確保目錄存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 檢查文件是否已存在
    if os.path.exists(file_path):
        print(f"配置文件 {file_path} 已存在，跳過")
        return
    
    # 配置模板
    template = """# -*- coding: utf-8 -*-
from config.default import DefaultConfig

class {class_name}(DefaultConfig):
    \"\"\"
    {doc_string}
    \"\"\"
    
    # 調試模式
    DEBUG = {debug}
    
    # SQL回顯
    SQLALCHEMY_ECHO = {sql_echo}
    
    # 數據庫URI
    SQLALCHEMY_DATABASE_URI = '{db_uri}'
    
    # 最大排程數
    MAX_SCHEDULES = {max_schedules}
    
    @classmethod
    def init_app(cls, app):
        \"\"\"初始化應用配置\"\"\"
        super().init_app(app)
        
        # {env_name}環境特定初始化
"""
    
    # 配置類名稱
    class_names = {
        'config/development.py': 'DevelopmentConfig',
        'config/production.py': 'ProductionConfig',
        'config/testing.py': 'TestingConfig'
    }
    
    # 環境文檔
    env_docs = {
        'config/development.py': '開發環境配置',
        'config/production.py': '生產環境配置',
        'config/testing.py': '測試環境配置'
    }
    
    # 環境名稱
    env_names = {
        'config/development.py': '開發',
        'config/production.py': '生產',
        'config/testing.py': '測試'
    }
    
    # 獲取數據庫URI
    db_uri = config.get('SQLALCHEMY_DATABASE_URI', 'sqlite:///app.db')
    
    # 獲取最大排程數
    max_schedules = config.get('MAX_SCHEDULES', '10')
    
    # 生成配置內容
    content = template.format(
        class_name=class_names.get(file_path, 'Config'),
        doc_string=env_docs.get(file_path, '配置文件'),
        debug='True' if is_dev_or_test else 'False',
        sql_echo='True' if is_dev_or_test else 'False',
        db_uri=db_uri,
        max_schedules=max_schedules,
        env_name=env_names.get(file_path, '')
    )
    
    # 寫入配置文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已創建配置文件: {file_path}")

def main():
    """主函數"""
    print("=== 開始遷移配置 ===")
    
    # 確保當前目錄是專案根目錄
    if not os.path.exists('app'):
        print("錯誤: 請在專案根目錄下運行此腳本")
        sys.exit(1)
    
    # 加載舊配置
    config = load_old_config()
    
    # 更新配置文件
    if config:
        update_config_files(config)
    else:
        print("未找到舊配置，將使用默認值創建新配置")
        update_config_files({})
    
    print("\n=== 配置遷移完成 ===")

if __name__ == "__main__":
    main() 