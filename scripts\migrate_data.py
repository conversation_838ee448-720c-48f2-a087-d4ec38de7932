# -*- coding: utf-8 -*-
import os
import shutil
import sys

def create_directory(path):
    """創建目錄"""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"✓ 創建目錄: {path}")

def migrate_files(src, dest):
    """遷移文件"""
    if not os.path.exists(src):
        print(f"源目錄不存在: {src}")
        return
    
    if not os.path.exists(dest):
        os.makedirs(dest)
        print(f"✓ 創建目錄: {dest}")
    
    file_count = 0
    for item in os.listdir(src):
        s = os.path.join(src, item)
        d = os.path.join(dest, item)
        
        try:
            if os.path.isdir(s):
                if not os.path.exists(d):
                    shutil.copytree(s, d)
                    print(f"✓ 複製目錄: {s} -> {d}")
                else:
                    for subitem in os.listdir(s):
                        ss = os.path.join(s, subitem)
                        dd = os.path.join(d, subitem)
                        if os.path.isdir(ss):
                            if not os.path.exists(dd):
                                shutil.copytree(ss, dd)
                        else:
                            shutil.copy2(ss, dd)
                        file_count += 1
            else:
                shutil.copy2(s, d)
                file_count += 1
                
            if file_count % 10 == 0:
                print(f"✓ 已複製 {file_count} 個文件...")
        except Exception as e:
            print(f"! 複製文件時出錯: {s} -> {d}: {str(e)}")
    
    print(f"✓ 總共複製了 {file_count} 個文件從 {src} 到 {dest}")

def main():
    """主函數"""
    print("=== 開始數據遷移 ===")
    
    # 確保當前目錄是專案根目錄
    if not os.path.exists('app'):
        print("錯誤: 請在專案根目錄下運行此腳本")
        sys.exit(1)
    
    # 確保目標目錄存在
    create_directory('app/data/uploads')
    create_directory('app/data/outputs')
    
    # 遷移上傳文件
    print("\n遷移上傳文件...")
    migrate_files('uploads', 'app/data/uploads')
    
    # 遷移輸出文件
    print("\n遷移輸出文件...")
    migrate_files('outputs', 'app/data/outputs')
    
    print("\n=== 數據遷移完成 ===")

if __name__ == "__main__":
    main() 