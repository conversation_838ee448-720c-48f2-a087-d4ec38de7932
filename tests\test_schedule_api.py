from datetime import datetime, timedelta
import pytest
from app import create_app, db
from app.models.schedule import Schedule, ScheduleType, ScheduleStatus
from app.models.script import Script

class TestScheduleAPI:
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """每個測試方法執行前的設置"""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 確保資料庫表格存在
        with self.app.app_context():
            db.drop_all()
            db.create_all()
        
        # 建立測試腳本
        self.test_script = Script(
            name='test_script.py',
            description='測試腳本',
            tags=['測試', '自動化'],
            created_at=datetime.now()
        )
        db.session.add(self.test_script)
        db.session.commit()
        
        # 建立測試排程
        self.test_schedule = Schedule(
            script_name='test_script.py',
            schedule_type=ScheduleType.DAILY.value,
            execution_time=datetime.now() + timedelta(hours=1),
            status=ScheduleStatus.WAITING.value,
            description='測試排程'
        )
        db.session.add(self.test_schedule)
        db.session.commit()
        
        yield
        
        # 清理
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_create_schedule(self):
        """測試建立排程"""
        data = {
            'script_name': 'test_script.py',
            'schedule_type': 'daily',
            'execution_time': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%S'),
            'description': '新測試排程'
        }
        response = self.client.post('/schedule/api/schedule', json=data)
        assert response.status_code == 201
        assert response.json['status'] == 'success'
        assert response.json['data']['script_name'] == 'test_script.py'

    def test_get_schedule_detail(self):
        """測試獲取排程詳情"""
        response = self.client.get(f'/schedule/api/schedule/{self.test_schedule.id}')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert response.json['data']['script_name'] == 'test_script.py'

    def test_get_schedules(self):
        """測試獲取排程列表"""
        response = self.client.get('/schedule/api/schedule')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert len(response.json['data']) >= 1

    def test_update_schedule(self):
        """測試更新排程"""
        data = {
            'description': '更新後的排程描述',
            'is_active': False
        }
        response = self.client.put(f'/schedule/api/schedule/{self.test_schedule.id}', json=data)
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert response.json['data']['description'] == '更新後的排程描述'
        assert not response.json['data']['is_active']

    def test_delete_schedule(self):
        """測試刪除排程"""
        response = self.client.delete(f'/schedule/api/schedule/{self.test_schedule.id}')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        
        # 確認排程已被刪除
        response = self.client.get(f'/schedule/api/schedule/{self.test_schedule.id}')
        assert response.status_code == 404

    def test_toggle_schedule_status(self):
        """測試切換排程狀態"""
        response = self.client.post(f'/schedule/api/schedule/{self.test_schedule.id}/toggle')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert not response.json['data']['is_active']
        
        # 再次切換狀態
        response = self.client.post(f'/schedule/api/schedule/{self.test_schedule.id}/toggle')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert response.json['data']['is_active']