#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腳本管理性能與壓力測試
"""
import time
import threading
import concurrent.futures
import tempfile
import os
import shutil
from app import create_app, db
from app.models import Script

class PerformanceTest:
    """性能測試類"""
    
    def __init__(self):
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.temp_dir = None
        
    def setup(self):
        """測試前準備"""
        self.app_context.push()
        db.create_all()
        
        # 創建臨時目錄
        self.temp_dir = tempfile.mkdtemp()
        self.scripts_dir = os.path.join(self.temp_dir, 'scripts')
        os.makedirs(self.scripts_dir, exist_ok=True)
        
        # 更新應用配置
        self.app.config['UPLOAD_FOLDER'] = self.scripts_dir
        
    def teardown(self):
        """測試後清理"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_api_response_time(self):
        """測試API響應時間"""
        print("🔄 測試API響應時間...")
        
        # 創建測試腳本
        for i in range(10):
            script = Script(
                name=f'perf_test_{i}.py',
                description=f'性能測試腳本 {i}',
                file_path=f'/fake/path/perf_test_{i}.py'
            )
            db.session.add(script)
        db.session.commit()
        
        # 測試GET請求響應時間
        times = []
        for _ in range(20):
            start_time = time.time()
            response = self.client.get('/script/')
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 轉換為毫秒
            times.append(response_time)
            
            assert response.status_code == 200
        
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
        
        print(f"✅ API響應時間統計:")
        print(f"   平均響應時間: {avg_time:.2f}ms")
        print(f"   最大響應時間: {max_time:.2f}ms")
        print(f"   最小響應時間: {min_time:.2f}ms")
        
        # 性能要求：平均響應時間應小於100ms
        assert avg_time < 100, f"API響應時間過慢: {avg_time:.2f}ms"
        
        return {
            'avg_time': avg_time,
            'max_time': max_time,
            'min_time': min_time
        }
    
    def test_concurrent_requests(self):
        """測試並發請求處理"""
        print("🔄 測試並發請求處理...")
        
        # 創建測試數據
        for i in range(5):
            script = Script(
                name=f'concurrent_test_{i}.py',
                description=f'並發測試腳本 {i}',
                file_path=f'/fake/path/concurrent_test_{i}.py'
            )
            db.session.add(script)
        db.session.commit()
        
        def make_request():
            """發送單個請求"""
            start_time = time.time()
            response = self.client.get('/script/')
            end_time = time.time()
            return {
                'status_code': response.status_code,
                'response_time': (end_time - start_time) * 1000
            }
        
        # 並發測試
        concurrent_levels = [1, 5, 10, 20]
        results = {}
        
        for level in concurrent_levels:
            print(f"   測試並發級別: {level}")
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=level) as executor:
                start_time = time.time()
                futures = [executor.submit(make_request) for _ in range(level)]
                responses = [future.result() for future in concurrent.futures.as_completed(futures)]
                end_time = time.time()
            
            # 統計結果
            successful_requests = sum(1 for r in responses if r['status_code'] == 200)
            avg_response_time = sum(r['response_time'] for r in responses) / len(responses)
            total_time = (end_time - start_time) * 1000
            
            results[level] = {
                'successful_requests': successful_requests,
                'total_requests': level,
                'success_rate': successful_requests / level * 100,
                'avg_response_time': avg_response_time,
                'total_time': total_time
            }
            
            print(f"     成功率: {results[level]['success_rate']:.1f}%")
            print(f"     平均響應時間: {avg_response_time:.2f}ms")
        
        return results
    
    def test_large_script_handling(self):
        """測試大型腳本處理"""
        print("🔄 測試大型腳本處理...")
        
        # 創建不同大小的腳本內容
        script_sizes = [1, 10, 50, 100]  # KB
        results = {}
        
        for size_kb in script_sizes:
            print(f"   測試腳本大小: {size_kb}KB")
            
            # 生成指定大小的腳本內容
            content_size = size_kb * 1024
            script_content = f'# 大型腳本測試 - {size_kb}KB\n'
            script_content += 'print("Hello, World!")\n' * (content_size // 25)
            
            # 創建臨時腳本文件
            script_path = os.path.join(self.scripts_dir, f'large_script_{size_kb}kb.py')
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # 測試腳本驗證性能
            script_executor = getattr(self.app, 'script_executor', None)
            if script_executor:
                start_time = time.time()
                validation_result = script_executor._validate_script(script_path)
                end_time = time.time()
                
                validation_time = (end_time - start_time) * 1000
                
                results[size_kb] = {
                    'validation_time': validation_time,
                    'validation_success': validation_result.get('valid', False),
                    'file_size': os.path.getsize(script_path)
                }
                
                print(f"     驗證時間: {validation_time:.2f}ms")
                print(f"     驗證結果: {'成功' if validation_result.get('valid') else '失敗'}")
        
        return results
    
    def test_database_performance(self):
        """測試數據庫性能"""
        print("🔄 測試數據庫性能...")
        
        # 測試批量插入
        batch_sizes = [10, 50, 100, 200]
        results = {}
        
        for batch_size in batch_sizes:
            print(f"   測試批量插入: {batch_size}條記錄")
            
            start_time = time.time()
            
            scripts = []
            for i in range(batch_size):
                script = Script(
                    name=f'batch_test_{batch_size}_{i}.py',
                    description=f'批量測試腳本 {i}',
                    file_path=f'/fake/path/batch_test_{batch_size}_{i}.py'
                )
                scripts.append(script)
                db.session.add(script)
            
            db.session.commit()
            end_time = time.time()
            
            insert_time = (end_time - start_time) * 1000
            
            # 測試查詢性能
            start_time = time.time()
            all_scripts = Script.query.all()
            end_time = time.time()
            
            query_time = (end_time - start_time) * 1000
            
            results[batch_size] = {
                'insert_time': insert_time,
                'query_time': query_time,
                'records_inserted': batch_size,
                'total_records': len(all_scripts)
            }
            
            print(f"     插入時間: {insert_time:.2f}ms")
            print(f"     查詢時間: {query_time:.2f}ms")
        
        return results
    
    def run_all_tests(self):
        """運行所有性能測試"""
        print("🚀 開始性能與壓力測試...")
        
        try:
            self.setup()
            
            results = {
                'api_response_time': self.test_api_response_time(),
                'concurrent_requests': self.test_concurrent_requests(),
                'large_script_handling': self.test_large_script_handling(),
                'database_performance': self.test_database_performance()
            }
            
            print("\n🎉 所有性能測試完成！")
            return results
            
        finally:
            self.teardown()

if __name__ == '__main__':
    test = PerformanceTest()
    results = test.run_all_tests()
    
    print("\n📊 性能測試總結:")
    print("=" * 50)
    
    # API響應時間
    api_results = results['api_response_time']
    print(f"API平均響應時間: {api_results['avg_time']:.2f}ms")
    
    # 並發處理
    concurrent_results = results['concurrent_requests']
    max_concurrent = max(concurrent_results.keys())
    max_success_rate = concurrent_results[max_concurrent]['success_rate']
    print(f"最大並發級別: {max_concurrent} (成功率: {max_success_rate:.1f}%)")
    
    # 大型腳本處理
    large_script_results = results['large_script_handling']
    max_size = max(large_script_results.keys())
    max_validation_time = large_script_results[max_size]['validation_time']
    print(f"最大腳本處理: {max_size}KB (驗證時間: {max_validation_time:.2f}ms)")
    
    print("=" * 50)
