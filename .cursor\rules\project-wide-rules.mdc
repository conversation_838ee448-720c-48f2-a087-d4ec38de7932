---
description: 
globs: 
alwaysApply: true
---

# Project-Specific Rules for Python Script Manager & Scheduler

## [Upload Function Standards]
- **檔案存放與命名**：
  - 所有上傳的 Python 腳本必須存放於 `uploads/` 目錄中。
  - 檔案名稱需遵循 PEP8 命名規範（小寫字母與底線），例如 `data_processor_v1.py`。

- **元數據要求**：
  - 每個上傳腳本必須包含文件註解，至少包括描述與類別標籤。
 
    ```

- **安全性與格式驗證**：
  - 上傳前必須檢查檔案格式與內容，防止惡意代碼注入與格式錯誤。
  - 若驗證失敗，必須返回明確的錯誤提示並記錄相關日誌。

## [Script Manager Module Guidelines]
- **模組化設計**：
  - 每個管理模組必須保持單一職責：新增、修改、刪除操作需透過統一接口函式進行。
  - 應將邏輯拆分為獨立的子模組（例如：上傳管理、狀態更新、版本控制）。

- **版本控制與日誌記錄**：
  - 每次操作（新增、修改、刪除）均需記錄版本歷史與操作日誌，格式應包含操作時間、操作者及操作詳情。
  - 確保變更可以追溯，方便團隊協作與問題排查。

## [Scheduler Functionality & Monitoring]
- **多線程與超時機制**：
  - 排程系統必須支持多線程，並實現任務超時機制，避免單個任務長時間佔用資源。
  - 每個排程任務應包含超時檢查與自動重試邏輯。

- **任務狀態監控**：
  - 每個任務必須標明狀態：例如「等待中」、「執行中」、「完成」、「失敗」。
  - 任務狀態變化需實時反饋至管理介面，並記錄詳細日誌。

## [UI/UX Consistency]
- **視覺風格**：
  - 所有 UI 元件必須遵循統一的視覺風格，參照「Python_Script_Scheduler_UI_Style.md」中的設計指南。
  - 統一字體、按鈕、間距與色彩搭配，保證良好用戶體驗與一致性。

- **響應式設計與交互提示**：
  - UI 介面必須支援響應式設計，適應不同解析度與裝置。
  - 提供即時、直觀的交互提示，當任務狀態更新、操作成功或發生錯誤時，均需及時顯示提示訊息。

## [Documentation & Code Comments]
- **註解規範**：
  - 每個模組與重要函式需包含詳細註解，解釋功能與邏輯，便於團隊溝通與後續維護。
  - 對於關鍵業務邏輯，應在代碼中引用相關文檔或內部規範說明。
