import threading
import time
from datetime import datetime
import logging
from typing import Optional, Dict, Any
from ..models.schedule import Schedule, ScheduleStatus, ScheduleType
from ..models.execution_log import ExecutionLog
from .schedule_manager import ScheduleManager
from .script_executor import ScriptExecutor
from flask_socketio import emit
import os
from ..utils.logger import setup_logger
from .. import db, socketio
import asyncio
from flask import current_app

logger = setup_logger(__name__)

class ScheduleExecutor:
    """排程執行器服務類別"""
    
    def __init__(self, app=None):
        """初始化排程執行器
        
        Args:
            app: Flask 應用程式實例
        """
        self.app = app
        self.schedule_manager = None
        self.script_executor = None
        self.logger = logger
        self._running = False
        self._thread = None
        self._lock = threading.Lock()
        self._active_schedules = {}
        self._timeout = 3600  # 預設超時時間：1小時
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._paused_tasks: Dict[str, asyncio.Task] = {}
        
        if app is not None:
            self.init_app(app)
            
    def init_app(self, app):
        """初始化 Flask 應用程式
        
        Args:
            app: Flask 應用程式實例
        """
        self.app = app
        
        # 檢查並獲取需要的服務
        if hasattr(app, 'schedule_manager'):
            self.schedule_manager = app.schedule_manager
        else:
            from .schedule_manager import ScheduleManager
            self.schedule_manager = ScheduleManager()
            self.schedule_manager.init_app(app)
            app.schedule_manager = self.schedule_manager
            
        if hasattr(app, 'script_executor'):
            self.script_executor = app.script_executor
        else:
            from .script_executor import ScriptExecutor
            self.script_executor = ScriptExecutor()
            self.script_executor.init_app(app)
            app.script_executor = self.script_executor
            
        self._timeout = app.config.get('SCHEDULE_TIMEOUT', 3600)
        
        # 啟動排程執行器
        self.start()
    
    def start(self):
        """啟動排程執行器"""
        with self._lock:
            if self._running:
                return
                
            self._running = True
            self._thread = threading.Thread(target=self._run_scheduler)
            self._thread.daemon = True
            self._thread.start()
            logger.info("排程執行器已啟動")
            
    def stop(self):
        """停止排程執行器"""
        with self._lock:
            if not self._running:
                return
                
            self._running = False
            if self._thread:
                self._thread.join(timeout=5)
            logger.info("排程執行器已停止")
            
    def _run_scheduler(self):
        """執行排程檢查循環"""
        while self._running:
            try:
                if self.app:
                    with self.app.app_context():
                        # 獲取待執行的排程
                        pending_schedules = self.schedule_manager.get_pending_schedules()
                        
                        for schedule in pending_schedules:
                            if not self._running:
                                break
                                
                            if schedule.id not in self._active_schedules:
                                self._execute_schedule(schedule)
                else:
                    logger.error("排程執行器未設置應用程式實例")
                    time.sleep(5)
                                
                time.sleep(1)  # 避免過度消耗 CPU
                
            except Exception as e:
                logger.error(f"排程執行器發生錯誤: {str(e)}")
                time.sleep(5)  # 發生錯誤時等待較長時間
                
    def _execute_schedule(self, schedule: Schedule):
        """執行單個排程"""
        try:
            # 更新排程狀態
            schedule.status = ScheduleStatus.RUNNING.value
            db.session.commit()
            
            # 建立執行記錄
            log = ExecutionLog(
                schedule_id=schedule.id,
                status='running',
                start_time=datetime.now()
            )
            db.session.add(log)
            db.session.commit()
            
            # 執行腳本
            success, output, error = self.script_executor.execute_script(schedule.script_name)
            
            # 更新執行記錄
            log.end_time = datetime.now()
            log.status = 'completed' if success else 'failed'
            log.output = output
            log.error = error
            db.session.commit()
            
            # 更新排程狀態
            schedule.status = ScheduleStatus.COMPLETED.value if success else ScheduleStatus.FAILED.value
            schedule.last_run = datetime.now()
            schedule.next_run = schedule._calculate_next_run()
            db.session.commit()
            
            logger.info(f"排程 {schedule.id} 執行完成，狀態: {schedule.status}")
            
        except Exception as e:
            logger.error(f"執行排程 {schedule.id} 時發生錯誤: {str(e)}")
            
            # 更新執行記錄
            if 'log' in locals():
                log.end_time = datetime.now()
                log.status = 'failed'
                log.error = str(e)
                db.session.commit()
                
            # 更新排程狀態
            schedule.status = ScheduleStatus.FAILED.value
            schedule.error_message = str(e)
            db.session.commit()
            
        finally:
            # 從活動排程中移除
            if schedule.id in self._active_schedules:
                del self._active_schedules[schedule.id]
                
    def execute_schedule_now(self, schedule_id: int) -> bool:
        """立即執行指定的排程"""
        try:
            schedule = self.schedule_manager.get_schedule(schedule_id)
            if not schedule:
                logger.error(f"找不到排程 {schedule_id}")
                return False
                
            if schedule.id in self._active_schedules:
                logger.warning(f"排程 {schedule_id} 正在執行中")
                return False
                
            # 將排程加入活動列表
            self._active_schedules[schedule.id] = schedule
            
            # 在新線程中執行排程
            def execute_with_context():
                with self.app.app_context():
                    self._execute_schedule(schedule)
            
            thread = threading.Thread(
                target=execute_with_context,
                daemon=True
            )
            thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"立即執行排程 {schedule_id} 時發生錯誤: {str(e)}")
            return False
            
    def pause_schedule(self, schedule_id: int) -> bool:
        """暫停排程"""
        try:
            schedule = self.schedule_manager.get_schedule(schedule_id)
            if not schedule:
                return False
                
            schedule.status = ScheduleStatus.PAUSED.value
            db.session.commit()
            return True
            
        except Exception as e:
            logger.error(f"暫停排程 {schedule_id} 時發生錯誤: {str(e)}")
            return False
            
    def resume_schedule(self, schedule_id: int) -> bool:
        """恢復排程"""
        try:
            schedule = self.schedule_manager.get_schedule(schedule_id)
            if not schedule:
                return False
                
            schedule.status = ScheduleStatus.WAITING.value
            schedule.next_run = schedule._calculate_next_run()
            db.session.commit()
            return True
            
        except Exception as e:
            logger.error(f"恢復排程 {schedule_id} 時發生錯誤: {str(e)}")
            return False
            
    def stop_schedule(self, schedule_id: int) -> bool:
        """停止排程"""
        try:
            schedule = self.schedule_manager.get_schedule(schedule_id)
            if not schedule:
                return False
                
            schedule.status = ScheduleStatus.STOPPED.value
            schedule.is_active = False
            db.session.commit()
            return True
            
        except Exception as e:
            logger.error(f"停止排程 {schedule_id} 時發生錯誤: {str(e)}")
            return False
            
    def get_schedule_status(self, schedule_id: int) -> Optional[str]:
        """獲取排程狀態"""
        try:
            schedule = self.schedule_manager.get_schedule(schedule_id)
            return schedule.status if schedule else None
            
        except Exception as e:
            logger.error(f"獲取排程 {schedule_id} 狀態時發生錯誤: {str(e)}")
            return None

    def add_schedule(self, schedule_id, schedule_data):
        """添加新的排程"""
        with self.lock:
            self.schedules[schedule_id] = {
                'data': schedule_data,
                'status': 'pending',
                'last_run': None,
                'next_run': None
            }
            self.logger.info(f'Added schedule {schedule_id}')
    
    def remove_schedule(self, schedule_id):
        """移除排程"""
        with self.lock:
            if schedule_id in self.schedules:
                del self.schedules[schedule_id]
                self.logger.info(f'Removed schedule {schedule_id}')
    
    def update_schedule_status(self, schedule_id, status):
        """更新排程狀態"""
        with self.lock:
            if schedule_id in self.schedules:
                self.schedules[schedule_id]['status'] = status
                self.logger.info(f'Updated schedule {schedule_id} status to {status}')
    
    def get_all_schedules(self):
        """獲取所有排程資訊"""
        return self.schedules.copy()
    
    def execute_schedule(self, schedule_id):
        """執行排程任務"""
        if schedule_id not in self.schedules:
            self.logger.error(f'Schedule {schedule_id} not found')
            return False
        
        try:
            self.update_schedule_status(schedule_id, 'running')
            # TODO: 實現實際的排程執行邏輯
            self.schedules[schedule_id]['last_run'] = datetime.now()
            self.update_schedule_status(schedule_id, 'completed')
            return True
        except Exception as e:
            self.logger.error(f'Error executing schedule {schedule_id}: {str(e)}')
            self.update_schedule_status(schedule_id, 'failed')
            return False

    def start_schedule(self, schedule: Schedule):
        """啟動排程任務"""
        with self._lock:
            if schedule.id in self.running_tasks:
                logger.warning(f"排程 {schedule.id} 已在執行中")
                return

            task_thread = threading.Thread(
                target=self._execute_schedule,
                args=(schedule,)
            )
            task_thread.daemon = True
            self.running_tasks[schedule.id] = task_thread
            task_thread.start()

            # 發送狀態更新
            self._emit_status_update(schedule.id, ScheduleStatus.RUNNING, 0)

    def stop_task(self, schedule_id: int):
        """停止任務執行"""
        with self._lock:
            if schedule_id in self.running_tasks:
                self.stop_schedule(Schedule.query.get(schedule_id))

    def retry_task(self, schedule_id: int):
        """重試失敗的任務"""
        schedule = Schedule.query.get(schedule_id)
        if schedule and schedule.status == ScheduleStatus.FAILED:
            schedule.status = ScheduleStatus.WAITING
            schedule.save()
            self.start_schedule(schedule)

    def _calculate_progress(self, process):
        """計算任務進度"""
        # 這裡可以根據實際需求實作進度計算邏輯
        # 目前使用模擬進度
        return min(95, int(time.time() * 10) % 100)

    def _emit_status_update(self, schedule_id: int, status: ScheduleStatus, progress: int):
        """發送狀態更新事件"""
        socketio.emit('status_update', {
            'schedule_id': schedule_id,
            'status': status.value,
            'progress': progress
        })

    def _emit_log_update(self, schedule_id: int, message: str):
        """發送日誌更新事件"""
        socketio.emit('log_update', {
            'schedule_id': schedule_id,
            'message': message
        })

    async def execute_schedule(self, schedule_id: str) -> bool:
        """執行排程"""
        try:
            schedule = self.schedule_manager.get_schedule(schedule_id)
            if not schedule:
                logger.error(f"找不到排程: {schedule_id}")
                return False
                
            # 創建執行日誌
            log = ExecutionLog.create_log(schedule_id, "running")
            
            # 更新排程狀態
            schedule.status = ScheduleStatus.RUNNING
            self.schedule_manager.update_schedule(schedule)
            
            # 發送狀態更新
            await self._emit_status_update(schedule_id, "running")
            
            # 執行腳本
            success, output, error = await self.script_executor.execute_script(
                schedule.script_name,
                timeout=schedule.timeout
            )
            
            # 更新執行日誌
            if success:
                log.complete("success", output=output)
                schedule.status = ScheduleStatus.COMPLETED
            else:
                log.complete("error", error_message=error)
                schedule.status = ScheduleStatus.FAILED
                
            # 更新排程狀態
            self.schedule_manager.update_schedule(schedule)
            
            # 發送狀態更新
            await self._emit_status_update(schedule_id, schedule.status.value)
            
            return success
            
        except Exception as e:
            logger.error(f"執行排程時發生錯誤: {str(e)}")
            if 'log' in locals():
                log.complete("error", error_message=str(e))
            if 'schedule' in locals():
                schedule.status = ScheduleStatus.FAILED
                self.schedule_manager.update_schedule(schedule)
            return False
            
    async def start_schedule(self, schedule: Schedule):
        """啟動排程"""
        if schedule.id in self._running_tasks:
            logger.warning(f"排程 {schedule.id} 已在執行中")
            return
            
        # 創建執行任務
        task = asyncio.create_task(self._schedule_task(schedule))
        self._running_tasks[schedule.id] = task
        
        # 發送狀態更新
        await self._emit_status_update(schedule.id, "running")
        
    async def stop_schedule(self, schedule: Schedule):
        """停止排程"""
        if schedule.id in self._running_tasks:
            task = self._running_tasks.pop(schedule.id)
            task.cancel()
            
        if schedule.id in self._paused_tasks:
            task = self._paused_tasks.pop(schedule.id)
            task.cancel()
            
        # 更新排程狀態
        schedule.status = ScheduleStatus.STOPPED
        self.schedule_manager.update_schedule(schedule)
        
        # 發送狀態更新
        await self._emit_status_update(schedule.id, "stopped")
        
    async def pause_task(self, schedule_id: str):
        """暫停排程任務"""
        if schedule_id in self._running_tasks:
            task = self._running_tasks.pop(schedule_id)
            self._paused_tasks[schedule_id] = task
            task.cancel()
            
            # 更新排程狀態
            schedule = self.schedule_manager.get_schedule(schedule_id)
            if schedule:
                schedule.status = ScheduleStatus.PAUSED
                self.schedule_manager.update_schedule(schedule)
                
                # 發送狀態更新
                await self._emit_status_update(schedule_id, "paused")
                
    async def resume_task(self, schedule_id: str):
        """恢復排程任務"""
        if schedule_id in self._paused_tasks:
            task = self._paused_tasks.pop(schedule_id)
            schedule = self.schedule_manager.get_schedule(schedule_id)
            if schedule:
                await self.start_schedule(schedule)
                
    async def retry_task(self, schedule_id: str):
        """重試排程任務"""
        await self.stop_schedule(self.schedule_manager.get_schedule(schedule_id))
        await self.execute_schedule(schedule_id)
        
    async def _schedule_task(self, schedule: Schedule):
        """排程任務執行器"""
        try:
            while True:
                # 檢查排程是否應該執行
                if not schedule.is_active:
                    break
                    
                # 執行排程
                await self.execute_schedule(schedule.id)
                
                # 等待下一次執行
                next_run = schedule.get_next_run()
                if not next_run:
                    break
                    
                wait_time = (next_run - datetime.now()).total_seconds()
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
                    
        except asyncio.CancelledError:
            logger.info(f"排程 {schedule.id} 已取消")
        except Exception as e:
            logger.error(f"排程任務執行錯誤: {str(e)}")
            
    async def _emit_status_update(self, schedule_id: str, status: str):
        """發送狀態更新事件"""
        try:
            await socketio.emit('schedule_status_update', {
                'schedule_id': schedule_id,
                'status': status,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            logger.error(f"發送狀態更新事件失敗: {str(e)}")
            
    def get_schedule_status(self, schedule_id: str) -> Optional[str]:
        """獲取排程狀態"""
        schedule = self.schedule_manager.get_schedule(schedule_id)
        return schedule.status.value if schedule else None
        
    def get_all_schedules(self) -> list:
        """獲取所有排程"""
        return self.schedule_manager.get_all_schedules() 