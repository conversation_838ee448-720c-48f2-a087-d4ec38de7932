import os
import ast
import importlib
import pkg_resources

def analyze_imports(script_path):
    """
    分析腳本中的import語句，找出所有依賴項
    
    Args:
        script_path: 腳本文件路徑
        
    Returns:
        dict: {'installed': [...], 'missing': [...]}
    """
    try:
        with open(script_path, 'r', encoding='utf-8') as file:
            tree = ast.parse(file.read())
    except Exception as e:
        return {'error': f'無法解析腳本: {str(e)}'}

    imports = set()
    
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for name in node.names:
                imports.add(name.name.split('.')[0])
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                imports.add(node.module.split('.')[0])

    # 過濾標準庫
    standard_libs = set(importlib.stdlib_module_names)
    third_party = {lib for lib in imports if lib not in standard_libs}
    
    # 檢查是否已安裝
    installed = set()
    missing = set()
    for lib in third_party:
        try:
            pkg_resources.require(lib)
            installed.add(lib)
        except (pkg_resources.DistributionNotFound, pkg_resources.VersionConflict):
            missing.add(lib)
            
    return {
        'installed': list(installed),
        'missing': list(missing)
    }

def validate_script(script_path):
    """
    驗證腳本是否符合基本要求
    
    Args:
        script_path: 腳本文件路徑
        
    Returns:
        tuple: (is_valid, message)
    """
    if not os.path.exists(script_path):
        return False, "腳本文件不存在"
        
    if not script_path.endswith('.py'):
        return False, "不是Python腳本文件"
        
    try:
        with open(script_path, 'r', encoding='utf-8') as file:
            ast.parse(file.read())
        return True, "腳本驗證通過"
    except SyntaxError as e:
        return False, f"腳本語法錯誤: {str(e)}"
    except Exception as e:
        return False, f"腳本驗證失敗: {str(e)}"

def setup_output_config(script_path, output_dir):
    """
    在腳本開頭添加輸出路徑設置
    
    Args:
        script_path: 腳本文件路徑
        output_dir: 指定的輸出目錄
    """
    config_code = f'''
import os
# 設置輸出目錄
OUTPUT_DIR = r"{output_dir}"
os.makedirs(OUTPUT_DIR, exist_ok=True)
'''
    
    try:
        with open(script_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        with open(script_path, 'w', encoding='utf-8') as file:
            file.write(config_code + content)
            
        return True, "成功設置輸出配置"
    except Exception as e:
        return False, f"設置輸出配置失敗: {str(e)}"

def get_safe_filename(filename):
    """
    確保文件名安全（移除危險字符）
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 安全的文件名
    """
    # 只保留字母、數字、點和下劃線
    safe_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789._')
    return ''.join(c for c in filename if c in safe_chars)
