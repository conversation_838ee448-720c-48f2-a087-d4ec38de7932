[2025-06-12 13:33:47,094] INFO in api_logger: [1749706427093] 請求開始: GET /script/
[2025-06-12 13:33:47,099] DEBUG in api_logger: [1749706427093] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:47,108] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:47,110] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.13ms
[2025-06-12 13:33:47,110] INFO in api_logger: [1749706427093] 請求完成: GET /script/ - 耗時: 8.70ms
[2025-06-12 13:33:47,140] INFO in api_logger: [1749706427140] 請求開始: GET /script/
[2025-06-12 13:33:47,141] DEBUG in api_logger: [1749706427140] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:47,151] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:47,153] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.91ms
[2025-06-12 13:33:47,153] INFO in api_logger: [1749706427140] 請求完成: GET /script/ - 耗時: 9.46ms
[2025-06-12 13:33:58,585] INFO in api_logger: [1749706438585] 請求開始: GET /script/
[2025-06-12 13:33:58,586] DEBUG in api_logger: [1749706438585] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:58,595] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:58,597] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.67ms
[2025-06-12 13:33:58,598] INFO in api_logger: [1749706438585] 請求完成: GET /script/ - 耗時: 7.81ms
[2025-06-12 13:35:31,994] INFO in api_logger: [1749706531994] 請求開始: GET /script/
[2025-06-12 13:35:31,995] DEBUG in api_logger: [1749706531994] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:35:32,002] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:35:32,003] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.99ms
[2025-06-12 13:35:32,004] INFO in api_logger: [1749706531994] 請求完成: GET /script/ - 耗時: 7.50ms
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:57:18,774] INFO in api_logger: [1751957838772] 請求開始: GET /script/
[2025-07-08 14:57:18,774] DEBUG in api_logger: [1751957838772] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:57:18,778] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:57:18,780] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:57:18,780] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.28ms
[2025-07-08 14:57:18,780] INFO in api_logger: [1751957838772] 請求完成: GET /script/ - 耗時: 5.41ms
[2025-07-08 14:57:29,699] INFO in api_logger: [1751957849697] 請求開始: GET /script/
[2025-07-08 14:57:29,699] DEBUG in api_logger: [1751957849697] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:57:29,702] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:57:29,703] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.31ms
[2025-07-08 14:57:29,703] INFO in api_logger: [1751957849697] 請求完成: GET /script/ - 耗時: 4.42ms
[2025-07-08 14:58:19,439] INFO in api_logger: [1751957899437] 請求開始: GET /script/info_test.py
[2025-07-08 14:58:19,439] INFO in api_logger: [1751957899437] 請求開始: GET /script/info_test.py
[2025-07-08 14:58:19,439] INFO in api_logger: [1751957899437] 請求開始: GET /script/info_test.py
[2025-07-08 14:58:19,440] DEBUG in api_logger: [1751957899437] 請求數據: {"method": "GET", "path": "/script/info_test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,440] DEBUG in api_logger: [1751957899437] 請求數據: {"method": "GET", "path": "/script/info_test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,440] DEBUG in api_logger: [1751957899437] 請求數據: {"method": "GET", "path": "/script/info_test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,441] WARNING in script_routes: 腳本文件不存在: /fake/path/info_test.py
[2025-07-08 14:58:19,441] WARNING in script_routes: 腳本文件不存在: /fake/path/info_test.py
[2025-07-08 14:58:19,441] WARNING in script_routes: 腳本文件不存在: /fake/path/info_test.py
[2025-07-08 14:58:19,442] INFO in api_logger: [1751957899437] 請求完成: GET /script/info_test.py - 耗時: 1.67ms
[2025-07-08 14:58:19,442] INFO in api_logger: [1751957899437] 請求完成: GET /script/info_test.py - 耗時: 1.67ms
[2025-07-08 14:58:19,442] INFO in api_logger: [1751957899437] 請求完成: GET /script/info_test.py - 耗時: 1.67ms
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:56,331] INFO in api_logger: [1751957936330] 請求開始: GET /script/
[2025-07-08 14:58:56,332] DEBUG in api_logger: [1751957936330] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:56,336] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:56,336] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.59ms
[2025-07-08 14:58:56,337] INFO in api_logger: [1751957936330] 請求完成: GET /script/ - 耗時: 3.97ms
[2025-07-08 14:59:12,860] INFO in api_logger: [1751957952858] 請求開始: GET /script/
[2025-07-08 14:59:12,861] DEBUG in api_logger: [1751957952858] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:59:12,863] WARNING in script_routes: 資料庫中的腳本 test.py 文件不存在於磁碟: /fake/path/test.py
[2025-07-08 14:59:12,864] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:59:12,867] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.46ms
[2025-07-08 14:59:12,868] INFO in api_logger: [1751957952858] 請求完成: GET /script/ - 耗時: 6.91ms
[2025-07-08 14:59:12,872] INFO in api_logger: [1751957952871] 請求開始: GET /script/test.py
[2025-07-08 14:59:12,872] DEBUG in api_logger: [1751957952871] 請求數據: {"method": "GET", "path": "/script/test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:59:12,877] WARNING in script_routes: 腳本文件不存在: /fake/path/test.py
[2025-07-08 14:59:12,877] INFO in api_logger: [1751957952871] 請求完成: GET /script/test.py - 耗時: 4.58ms
[2025-07-08 14:59:26,626] INFO in api_logger: [1751957966624] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:59:26,627] DEBUG in api_logger: [1751957966624] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:59:26,628] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:59:26,629] INFO in api_logger: [1751957966624] 請求完成: GET /script/nonexistent.py - 耗時: 1.62ms
[2025-07-08 15:02:34,412] INFO in api_logger: [1751958154410] 請求開始: GET /script/
[2025-07-08 15:02:34,413] DEBUG in api_logger: [1751958154410] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:02:34,418] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:02:34,418] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.67ms
[2025-07-08 15:02:34,419] INFO in api_logger: [1751958154410] 請求完成: GET /script/ - 耗時: 5.09ms
[2025-07-08 15:02:34,421] INFO in api_logger: [1751958154421] 請求開始: GET /script/
[2025-07-08 15:02:34,421] DEBUG in api_logger: [1751958154421] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:02:34,428] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:02:34,429] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.71ms
[2025-07-08 15:02:34,430] INFO in api_logger: [1751958154421] 請求完成: GET /script/ - 耗時: 7.23ms
[2025-07-08 15:02:56,971] INFO in api_logger: [1751958176971] 請求開始: GET /script/
[2025-07-08 15:02:56,972] DEBUG in api_logger: [1751958176971] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:02:56,976] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:02:56,977] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.98ms
[2025-07-08 15:02:56,977] INFO in api_logger: [1751958176971] 請求完成: GET /script/ - 耗時: 4.30ms
[2025-07-08 15:03:47,312] INFO in api_logger: [1751958227310] 請求開始: GET /script/
[2025-07-08 15:03:47,312] DEBUG in api_logger: [1751958227310] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:03:47,313] WARNING in script_routes: 資料庫中的腳本 integration_manual_test.py 文件不存在於磁碟: /fake/path/test.py
[2025-07-08 15:03:47,314] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 15:03:47,315] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.06ms
[2025-07-08 15:03:47,316] INFO in api_logger: [1751958227310] 請求完成: GET /script/ - 耗時: 3.30ms
[2025-07-08 15:13:40,648] INFO in api_logger: [1751958820648] 請求開始: GET /script/
[2025-07-08 15:13:40,649] DEBUG in api_logger: [1751958820648] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:13:40,655] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:13:40,656] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.17ms
[2025-07-08 15:13:40,657] INFO in api_logger: [1751958820648] 請求完成: GET /script/ - 耗時: 5.73ms
[2025-07-08 15:15:40,875] INFO in api_logger: [1751958940875] 請求開始: GET /script/
[2025-07-08 15:15:40,876] DEBUG in api_logger: [1751958940875] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:15:40,880] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:15:40,880] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.31ms
[2025-07-08 15:15:40,880] INFO in api_logger: [1751958940875] 請求完成: GET /script/ - 耗時: 3.66ms
[2025-07-08 15:15:49,263] INFO in api_logger: [1751958949263] 請求開始: GET /script/
[2025-07-08 15:15:49,264] DEBUG in api_logger: [1751958949263] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:15:49,269] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:15:49,270] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.83ms
[2025-07-08 15:15:49,271] INFO in api_logger: [1751958949263] 請求完成: GET /script/ - 耗時: 6.30ms
[2025-07-08 15:15:54,453] INFO in api_logger: [1751958954452] 請求開始: GET /script/
[2025-07-08 15:15:54,453] DEBUG in api_logger: [1751958954452] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:15:54,457] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:15:54,458] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.60ms
[2025-07-08 15:15:54,459] INFO in api_logger: [1751958954452] 請求完成: GET /script/ - 耗時: 4.32ms
[2025-07-08 15:17:54,377] INFO in api_logger: [1751959074376] 請求開始: POST /script/validate
[2025-07-08 15:17:54,377] DEBUG in api_logger: [1751959074376] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "215", "Content-Type": "multipart/form-data; boundary=5c0245b3671ca3f5be2fa8ac312eec2b"}, "args": {}}
[2025-07-08 15:17:54,378] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:17:54,378] ERROR in script_routes: 腳本執行器未初始化
[2025-07-08 15:17:54,379] INFO in api_logger: [1751959074376] 請求完成: POST /script/validate - 耗時: 0.78ms
[2025-07-08 15:19:18,555] INFO in api_logger: [1751959158555] 請求開始: POST /script/validate
[2025-07-08 15:19:18,556] DEBUG in api_logger: [1751959158555] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "215", "Content-Type": "multipart/form-data; boundary=0d6935327ed090522707c3c7f7173520"}, "args": {}}
[2025-07-08 15:19:18,557] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:19:18,567] INFO in script_routes: 腳本驗證完成: test.py, 有效: True
[2025-07-08 15:19:18,569] INFO in api_logger: [1751959158555] 請求完成: POST /script/validate - 耗時: 12.43ms
[2025-07-08 15:22:35,371] INFO in api_logger: [1751959355368] 請求開始: GET /script/
[2025-07-08 15:22:35,371] DEBUG in api_logger: [1751959355368] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,373] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,374] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,374] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,374] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,375] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,375] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,376] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,376] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,376] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,377] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,377] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,387] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.41ms
[2025-07-08 15:22:35,387] INFO in api_logger: [1751959355368] 請求完成: GET /script/ - 耗時: 15.77ms
[2025-07-08 15:22:35,388] INFO in api_logger: [1751959355388] 請求開始: GET /script/
[2025-07-08 15:22:35,388] DEBUG in api_logger: [1751959355388] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,389] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,390] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,390] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,390] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,390] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,391] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,391] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,391] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,391] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,392] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,392] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,400] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 10.97ms
[2025-07-08 15:22:35,400] INFO in api_logger: [1751959355388] 請求完成: GET /script/ - 耗時: 11.24ms
[2025-07-08 15:22:35,400] INFO in api_logger: [1751959355400] 請求開始: GET /script/
[2025-07-08 15:22:35,401] DEBUG in api_logger: [1751959355400] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,402] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,402] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,402] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,402] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,403] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,403] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,403] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,403] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,404] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,404] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,405] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,412] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.03ms
[2025-07-08 15:22:35,413] INFO in api_logger: [1751959355400] 請求完成: GET /script/ - 耗時: 11.56ms
[2025-07-08 15:22:35,414] INFO in api_logger: [1751959355414] 請求開始: GET /script/
[2025-07-08 15:22:35,414] DEBUG in api_logger: [1751959355414] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,417] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,417] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,417] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,418] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,418] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,418] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,418] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,419] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,419] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,419] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,420] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,426] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.28ms
[2025-07-08 15:22:35,426] INFO in api_logger: [1751959355414] 請求完成: GET /script/ - 耗時: 11.53ms
[2025-07-08 15:22:35,427] INFO in api_logger: [1751959355427] 請求開始: GET /script/
[2025-07-08 15:22:35,427] DEBUG in api_logger: [1751959355427] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,428] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,429] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,429] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,429] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,430] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,430] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,431] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,431] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,431] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,431] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,433] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,440] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 12.03ms
[2025-07-08 15:22:35,440] INFO in api_logger: [1751959355427] 請求完成: GET /script/ - 耗時: 12.24ms
[2025-07-08 15:22:35,440] INFO in api_logger: [1751959355440] 請求開始: GET /script/
[2025-07-08 15:22:35,441] DEBUG in api_logger: [1751959355440] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,441] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,442] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,442] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,442] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,443] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,443] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,443] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,443] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,444] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,444] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,445] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,453] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.74ms
[2025-07-08 15:22:35,453] INFO in api_logger: [1751959355440] 請求完成: GET /script/ - 耗時: 11.99ms
[2025-07-08 15:22:35,453] INFO in api_logger: [1751959355453] 請求開始: GET /script/
[2025-07-08 15:22:35,454] DEBUG in api_logger: [1751959355453] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,455] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,455] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,455] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,455] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,456] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,456] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,456] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,456] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,457] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,457] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,458] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,463] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 9.51ms
[2025-07-08 15:22:35,464] INFO in api_logger: [1751959355453] 請求完成: GET /script/ - 耗時: 9.75ms
[2025-07-08 15:22:35,465] INFO in api_logger: [1751959355465] 請求開始: GET /script/
[2025-07-08 15:22:35,466] DEBUG in api_logger: [1751959355465] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,467] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,467] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,467] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,468] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,468] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,468] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,468] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,469] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,469] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,469] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,470] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,475] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 9.27ms
[2025-07-08 15:22:35,476] INFO in api_logger: [1751959355465] 請求完成: GET /script/ - 耗時: 9.48ms
[2025-07-08 15:22:35,476] INFO in api_logger: [1751959355476] 請求開始: GET /script/
[2025-07-08 15:22:35,476] DEBUG in api_logger: [1751959355476] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,477] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,478] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,478] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,478] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,478] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,479] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,479] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,479] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,480] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,480] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,482] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,527] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 50.18ms
[2025-07-08 15:22:35,527] INFO in api_logger: [1751959355476] 請求完成: GET /script/ - 耗時: 50.59ms
[2025-07-08 15:22:35,535] INFO in api_logger: [1751959355534] 請求開始: GET /script/
[2025-07-08 15:22:35,538] DEBUG in api_logger: [1751959355534] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,540] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,541] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,541] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,542] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,542] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,543] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,543] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,544] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,544] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,545] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,546] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,555] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.15ms
[2025-07-08 15:22:35,555] INFO in api_logger: [1751959355534] 請求完成: GET /script/ - 耗時: 16.36ms
[2025-07-08 15:41:37,727] INFO in api_logger: [1751960497725] 請求開始: GET /script/
[2025-07-08 15:41:37,728] DEBUG in api_logger: [1751960497725] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:41:37,732] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:41:37,733] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.37ms
[2025-07-08 15:41:37,733] INFO in api_logger: [1751960497725] 請求完成: GET /script/ - 耗時: 4.76ms
[2025-07-08 15:41:37,970] INFO in api_logger: [1751960497970] 請求開始: GET /script/
[2025-07-08 15:41:37,971] DEBUG in api_logger: [1751960497970] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:41:37,975] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:41:37,976] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.79ms
[2025-07-08 15:41:37,976] INFO in api_logger: [1751960497970] 請求完成: GET /script/ - 耗時: 4.22ms
[2025-07-08 15:41:40,271] INFO in api_logger: [1751960500271] 請求開始: GET /script/
[2025-07-08 15:41:40,272] DEBUG in api_logger: [1751960500271] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:41:40,275] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:41:40,276] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.18ms
[2025-07-08 15:41:40,276] INFO in api_logger: [1751960500271] 請求完成: GET /script/ - 耗時: 3.45ms
[2025-07-08 15:41:52,483] INFO in api_logger: [1751960512482] 請求開始: POST /script/validate
[2025-07-08 15:41:52,484] DEBUG in api_logger: [1751960512482] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3522", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryDH3BONZzpoXsCNhA", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:41:52,485] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:41:52,668] INFO in script_routes: 腳本驗證完成: output_booking.py, 有效: True
[2025-07-08 15:41:52,671] INFO in api_logger: [1751960512482] 請求完成: POST /script/validate - 耗時: 186.36ms
[2025-07-08 15:42:06,419] INFO in api_logger: [1751960526419] 請求開始: POST /script/validate
[2025-07-08 15:42:06,420] DEBUG in api_logger: [1751960526419] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "443", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryAxACv9FMgrAX6uAE", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:06,421] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:42:06,425] INFO in script_routes: 腳本驗證完成: test_script.py, 有效: True
[2025-07-08 15:42:06,428] INFO in api_logger: [1751960526419] 請求完成: POST /script/validate - 耗時: 6.94ms
[2025-07-08 15:42:17,904] INFO in api_logger: [1751960537903] 請求開始: POST /script/validate
[2025-07-08 15:42:17,905] DEBUG in api_logger: [1751960537903] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "5959", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarySAQuOrTmy3x9kITL", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:17,907] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:42:17,916] INFO in script_routes: 腳本驗證完成: output_news.py, 有效: True
[2025-07-08 15:42:17,920] INFO in api_logger: [1751960537903] 請求完成: POST /script/validate - 耗時: 12.58ms
[2025-07-08 15:42:42,915] INFO in api_logger: [1751960562915] 請求開始: GET /script/
[2025-07-08 15:42:42,916] DEBUG in api_logger: [1751960562915] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:42,919] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:42:42,920] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.67ms
[2025-07-08 15:42:42,920] INFO in api_logger: [1751960562915] 請求完成: GET /script/ - 耗時: 4.00ms
[2025-07-08 15:42:46,074] INFO in api_logger: [1751960566073] 請求開始: GET /script/
[2025-07-08 15:42:46,074] DEBUG in api_logger: [1751960566073] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:46,078] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:42:46,078] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.46ms
[2025-07-08 15:42:46,079] INFO in api_logger: [1751960566073] 請求完成: GET /script/ - 耗時: 3.93ms
[2025-07-08 15:42:46,353] INFO in api_logger: [1751960566353] 請求開始: GET /script/
[2025-07-08 15:42:46,354] DEBUG in api_logger: [1751960566353] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:46,357] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:42:46,358] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.52ms
[2025-07-08 15:42:46,359] INFO in api_logger: [1751960566353] 請求完成: GET /script/ - 耗時: 3.88ms
[2025-07-08 15:42:48,249] INFO in api_logger: [1751960568249] 請求開始: GET /script/
[2025-07-08 15:42:48,250] DEBUG in api_logger: [1751960568249] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:48,252] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:42:48,253] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 2.69ms
[2025-07-08 15:42:48,253] INFO in api_logger: [1751960568249] 請求完成: GET /script/ - 耗時: 3.00ms
[2025-07-08 16:21:34,140] INFO in api_logger: [1751962894140] 請求開始: GET /script/
[2025-07-08 16:21:34,141] DEBUG in api_logger: [1751962894140] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:21:34,145] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:21:34,145] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.47ms
[2025-07-08 16:21:34,146] INFO in api_logger: [1751962894140] 請求完成: GET /script/ - 耗時: 3.87ms
[2025-07-08 16:21:59,352] INFO in api_logger: [1751962919351] 請求開始: POST /script/validate
[2025-07-08 16:21:59,353] DEBUG in api_logger: [1751962919351] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6002", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryZwqu6xAsQC5bUXoy", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:21:59,354] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 16:21:59,360] INFO in script_routes: 腳本驗證完成: output_news.py, 有效: True
[2025-07-08 16:21:59,364] INFO in api_logger: [1751962919351] 請求完成: POST /script/validate - 耗時: 10.21ms
[2025-07-08 16:23:15,913] INFO in api_logger: [1751962995913] 請求開始: POST /script/
[2025-07-08 16:23:15,914] DEBUG in api_logger: [1751962995913] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6226", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarybZ6U6jVrcysNB8hC", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:23:15,915] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:23:15,915] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 16:23:15,920] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('output_news.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:23:15,921] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 6.07ms
[2025-07-08 16:23:15,921] INFO in api_logger: [1751962995913] 請求完成: POST /script/ - 耗時: 6.37ms
[2025-07-08 16:23:24,583] INFO in api_logger: [1751963004582] 請求開始: POST /script/
[2025-07-08 16:23:24,584] DEBUG in api_logger: [1751963004582] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6226", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryyd3Iru5U7qi2Tx99", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:23:24,585] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:23:24,585] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 16:23:24,589] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('output_news.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:23:24,590] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 5.52ms
[2025-07-08 16:23:24,591] INFO in api_logger: [1751963004582] 請求完成: POST /script/ - 耗時: 6.03ms
[2025-07-08 16:23:31,248] INFO in api_logger: [1751963011248] 請求開始: POST /script/
[2025-07-08 16:23:31,249] DEBUG in api_logger: [1751963011248] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6226", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary0py9VBb1YlcM0tuw", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:23:31,250] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:23:31,250] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 16:23:31,253] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('output_news.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:23:31,254] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 3.89ms
[2025-07-08 16:23:31,254] INFO in api_logger: [1751963011248] 請求完成: POST /script/ - 耗時: 4.12ms
[2025-07-08 16:23:50,656] INFO in api_logger: [1751963030656] 請求開始: GET /script/
[2025-07-08 16:23:50,657] DEBUG in api_logger: [1751963030656] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:23:50,661] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:23:50,661] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.00ms
[2025-07-08 16:23:50,662] INFO in api_logger: [1751963030656] 請求完成: GET /script/ - 耗時: 4.45ms
[2025-07-08 16:28:57,273] INFO in api_logger: [1751963337273] 請求開始: GET /script/
[2025-07-08 16:28:57,274] DEBUG in api_logger: [1751963337273] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:28:57,278] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:28:57,278] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.48ms
[2025-07-08 16:28:57,279] INFO in api_logger: [1751963337273] 請求完成: GET /script/ - 耗時: 3.88ms
[2025-07-08 16:30:52,774] INFO in api_logger: [1751963452774] 請求開始: GET /script/
[2025-07-08 16:30:52,775] DEBUG in api_logger: [1751963452774] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:30:52,779] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:30:52,780] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.91ms
[2025-07-08 16:30:52,781] INFO in api_logger: [1751963452774] 請求完成: GET /script/ - 耗時: 5.22ms
[2025-07-08 16:31:46,261] INFO in api_logger: [1751963506260] 請求開始: POST /script/
[2025-07-08 16:31:46,261] DEBUG in api_logger: [1751963506260] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "332", "Content-Type": "multipart/form-data; boundary=550b2658a923b4997f0303d862b8e08e"}, "args": {}, "form": {"description": "測試上傳腳本"}}
[2025-07-08 16:31:46,262] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:31:46,262] DEBUG in script_routes: 準備上傳腳本: test_upload.py
[2025-07-08 16:31:46,267] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('test_upload.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:31:46,268] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 6.07ms
[2025-07-08 16:31:46,268] INFO in api_logger: [1751963506260] 請求完成: POST /script/ - 耗時: 6.51ms
[2025-07-08 16:32:36,368] INFO in api_logger: [1751963556360] 請求開始: GET /script/
[2025-07-08 16:32:36,368] DEBUG in api_logger: [1751963556360] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:32:36,376] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:32:36,376] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.71ms
[2025-07-08 16:32:36,377] INFO in api_logger: [1751963556360] 請求完成: GET /script/ - 耗時: 7.19ms
[2025-07-08 16:32:36,673] INFO in api_logger: [1751963556673] 請求開始: GET /script/
[2025-07-08 16:32:36,673] DEBUG in api_logger: [1751963556673] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:32:36,677] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:32:36,678] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.69ms
[2025-07-08 16:32:36,678] INFO in api_logger: [1751963556673] 請求完成: GET /script/ - 耗時: 4.06ms
[2025-07-08 16:33:37,712] INFO in api_logger: [1751963617712] 請求開始: POST /script/
[2025-07-08 16:33:37,713] DEBUG in api_logger: [1751963617712] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "332", "Content-Type": "multipart/form-data; boundary=a64b5fc72a5c7f7f37fa3538d74593a8"}, "args": {}, "form": {"description": "測試上傳腳本"}}
[2025-07-08 16:33:37,714] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:33:37,714] DEBUG in script_routes: 準備上傳腳本: test_upload.py
[2025-07-08 16:33:37,719] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('test_upload.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:33:37,722] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 8.30ms
[2025-07-08 16:33:37,723] INFO in api_logger: [1751963617712] 請求完成: POST /script/ - 耗時: 8.76ms
[2025-07-08 16:35:12,444] INFO in api_logger: [1751963712443] 請求開始: POST /script/
[2025-07-08 16:35:12,445] DEBUG in api_logger: [1751963712443] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "332", "Content-Type": "multipart/form-data; boundary=0a904c5ad362b21b4bbcd0667787c547"}, "args": {}, "form": {"description": "測試上傳腳本"}}
[2025-07-08 16:35:12,445] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:35:12,446] DEBUG in script_routes: 準備上傳腳本: test_upload.py
[2025-07-08 16:35:12,451] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\test_upload.py
[2025-07-08 16:35:12,469] INFO in script_routes: 腳本上傳成功: test_upload.py
[2025-07-08 16:35:12,475] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 29.36ms
[2025-07-08 16:35:12,475] INFO in api_logger: [1751963712443] 請求完成: POST /script/ - 耗時: 29.74ms
[2025-07-08 16:37:31,325] INFO in api_logger: [1751963851321] 請求開始: GET /script/
[2025-07-08 16:37:31,328] DEBUG in api_logger: [1751963851321] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:37:31,343] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:37:31,349] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 19.54ms
[2025-07-08 16:37:31,349] INFO in api_logger: [1751963851321] 請求完成: GET /script/ - 耗時: 20.13ms
[2025-07-08 16:37:31,645] INFO in api_logger: [1751963851645] 請求開始: GET /script/
[2025-07-08 16:37:31,646] DEBUG in api_logger: [1751963851645] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:37:31,655] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:37:31,659] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.35ms
[2025-07-08 16:37:31,659] INFO in api_logger: [1751963851645] 請求完成: GET /script/ - 耗時: 11.71ms
[2025-07-08 16:39:11,318] INFO in api_logger: [1751963951317] 請求開始: GET /script/
[2025-07-08 16:39:11,319] DEBUG in api_logger: [1751963951317] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:39:11,327] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:39:11,333] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 13.05ms
[2025-07-08 16:39:11,335] INFO in api_logger: [1751963951317] 請求完成: GET /script/ - 耗時: 15.52ms
[2025-07-08 16:39:11,577] INFO in api_logger: [1751963951577] 請求開始: GET /script/
[2025-07-08 16:39:11,577] DEBUG in api_logger: [1751963951577] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:39:11,582] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:39:11,586] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 7.99ms
[2025-07-08 16:39:11,586] INFO in api_logger: [1751963951577] 請求完成: GET /script/ - 耗時: 8.27ms
[2025-07-08 16:39:13,535] INFO in api_logger: [1751963953534] 請求開始: GET /script/
[2025-07-08 16:39:13,536] DEBUG in api_logger: [1751963953534] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:39:13,544] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:39:13,548] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 10.53ms
[2025-07-08 16:39:13,548] INFO in api_logger: [1751963953534] 請求完成: GET /script/ - 耗時: 11.19ms
[2025-07-08 16:39:43,124] INFO in api_logger: [1751963983122] 請求開始: POST /script/validate
[2025-07-08 16:39:43,125] DEBUG in api_logger: [1751963983122] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "5959", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarycZY5w7qi5EN5MlAc", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:39:43,127] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 16:39:43,311] INFO in script_routes: 腳本驗證完成: output_news.py, 有效: True
[2025-07-08 16:39:43,315] INFO in api_logger: [1751963983122] 請求完成: POST /script/validate - 耗時: 187.96ms
[2025-07-08 16:40:02,939] INFO in api_logger: [1751964002938] 請求開始: POST /script/
[2025-07-08 16:40:02,941] DEBUG in api_logger: [1751964002938] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6183", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryFbUfu2gltmoSqTTM", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:40:02,943] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:40:02,944] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 16:40:02,950] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_news.py
[2025-07-08 16:40:02,968] INFO in script_routes: 腳本上傳成功: output_news.py
[2025-07-08 16:40:02,972] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 29.12ms
[2025-07-08 16:40:02,972] INFO in api_logger: [1751964002938] 請求完成: POST /script/ - 耗時: 29.47ms
[2025-07-08 16:40:08,120] INFO in api_logger: [1751964008120] 請求開始: GET /script/
[2025-07-08 16:40:08,121] DEBUG in api_logger: [1751964008120] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:08,130] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:40:08,134] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 12.22ms
[2025-07-08 16:40:08,135] INFO in api_logger: [1751964008120] 請求完成: GET /script/ - 耗時: 12.65ms
[2025-07-08 16:40:12,922] INFO in api_logger: [1751964012922] 請求開始: GET /script/
[2025-07-08 16:40:12,926] DEBUG in api_logger: [1751964012922] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:13,038] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:40:13,197] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 262.82ms
[2025-07-08 16:40:13,200] INFO in api_logger: [1751964012922] 請求完成: GET /script/ - 耗時: 265.51ms
[2025-07-08 16:40:13,325] INFO in api_logger: [1751964013324] 請求開始: GET /script/
[2025-07-08 16:40:13,336] DEBUG in api_logger: [1751964013324] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:13,528] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:40:13,636] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 289.81ms
[2025-07-08 16:40:13,659] INFO in api_logger: [1751964013324] 請求完成: GET /script/ - 耗時: 312.79ms
[2025-07-08 16:40:15,105] INFO in api_logger: [1751964015104] 請求開始: GET /script/
[2025-07-08 16:40:15,135] DEBUG in api_logger: [1751964015104] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:15,537] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:40:15,596] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 397.69ms
[2025-07-08 16:40:15,602] INFO in api_logger: [1751964015104] 請求完成: GET /script/ - 耗時: 403.65ms
[2025-07-08 16:40:38,451] INFO in api_logger: [1751964038451] 請求開始: GET /script/
[2025-07-08 16:40:38,453] DEBUG in api_logger: [1751964038451] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:38,465] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:40:38,470] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 14.80ms
[2025-07-08 16:40:38,470] INFO in api_logger: [1751964038451] 請求完成: GET /script/ - 耗時: 15.19ms
[2025-07-08 16:40:44,875] INFO in api_logger: [1751964044875] 請求開始: GET /script/
[2025-07-08 16:40:44,877] DEBUG in api_logger: [1751964044875] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:44,883] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:40:44,886] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.37ms
[2025-07-08 16:40:44,887] INFO in api_logger: [1751964044875] 請求完成: GET /script/ - 耗時: 8.72ms
[2025-07-08 16:40:55,609] INFO in api_logger: [1751964055608] 請求開始: POST /script/validate
[2025-07-08 16:40:55,611] DEBUG in api_logger: [1751964055608] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3522", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryzLpMcYCqbpFHAJtE", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:55,612] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 16:40:55,622] INFO in script_routes: 腳本驗證完成: output_booking.py, 有效: True
[2025-07-08 16:40:55,627] INFO in api_logger: [1751964055608] 請求完成: POST /script/validate - 耗時: 14.44ms
[2025-07-08 16:41:49,972] INFO in api_logger: [1751964109971] 請求開始: POST /script/
[2025-07-08 16:41:49,973] DEBUG in api_logger: [1751964109971] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3743", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary9qx0KFYy4cOCDQ3e", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "訂房網內容爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:41:49,974] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:41:49,975] DEBUG in script_routes: 準備上傳腳本: output_booking.py
[2025-07-08 16:41:49,980] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_booking.py
[2025-07-08 16:41:50,012] INFO in script_routes: 腳本上傳成功: output_booking.py
[2025-07-08 16:41:50,015] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 40.79ms
[2025-07-08 16:41:50,015] INFO in api_logger: [1751964109971] 請求完成: POST /script/ - 耗時: 41.09ms
[2025-07-08 16:41:56,068] INFO in api_logger: [1751964116068] 請求開始: GET /script/
[2025-07-08 16:41:56,069] DEBUG in api_logger: [1751964116068] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:41:56,080] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:41:56,086] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.27ms
[2025-07-08 16:41:56,087] INFO in api_logger: [1751964116068] 請求完成: GET /script/ - 耗時: 16.78ms
[2025-07-08 16:41:58,685] INFO in api_logger: [1751964118685] 請求開始: GET /script/
[2025-07-08 16:41:58,686] DEBUG in api_logger: [1751964118685] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:41:58,701] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:41:58,709] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.30ms
[2025-07-08 16:41:58,709] INFO in api_logger: [1751964118685] 請求完成: GET /script/ - 耗時: 22.71ms
[2025-07-08 16:41:59,051] INFO in api_logger: [1751964119050] 請求開始: GET /script/
[2025-07-08 16:41:59,051] DEBUG in api_logger: [1751964119050] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:41:59,062] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:41:59,069] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.68ms
[2025-07-08 16:41:59,070] INFO in api_logger: [1751964119050] 請求完成: GET /script/ - 耗時: 17.06ms
[2025-07-08 16:42:00,418] INFO in api_logger: [1751964120418] 請求開始: GET /script/
[2025-07-08 16:42:00,418] DEBUG in api_logger: [1751964120418] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:42:00,425] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:42:00,430] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 10.93ms
[2025-07-08 16:42:00,430] INFO in api_logger: [1751964120418] 請求完成: GET /script/ - 耗時: 11.43ms
[2025-07-08 16:45:22,835] INFO in api_logger: [1751964322835] 請求開始: GET /script/
[2025-07-08 16:45:22,836] DEBUG in api_logger: [1751964322835] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:45:22,849] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:45:22,855] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.96ms
[2025-07-08 16:45:22,856] INFO in api_logger: [1751964322835] 請求完成: GET /script/ - 耗時: 17.42ms
[2025-07-08 16:45:23,153] INFO in api_logger: [1751964323153] 請求開始: GET /script/
[2025-07-08 16:45:23,153] DEBUG in api_logger: [1751964323153] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:45:23,159] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:45:23,162] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.23ms
[2025-07-08 16:45:23,162] INFO in api_logger: [1751964323153] 請求完成: GET /script/ - 耗時: 8.56ms
[2025-07-08 16:45:41,485] INFO in api_logger: [1751964341485] 請求開始: GET /script/
[2025-07-08 16:45:41,486] DEBUG in api_logger: [1751964341485] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:45:41,492] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:45:41,496] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 9.27ms
[2025-07-08 16:45:41,496] INFO in api_logger: [1751964341485] 請求完成: GET /script/ - 耗時: 9.64ms
[2025-07-08 17:02:49,402] INFO in api_logger: [1751965369401] 請求開始: GET /script/
[2025-07-08 17:02:49,403] DEBUG in api_logger: [1751965369401] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:02:49,409] INFO in script_routes: 發現未記錄的腳本文件: output_booking.py，同步到資料庫
[2025-07-08 17:02:49,417] INFO in script_routes: 發現未記錄的腳本文件: test_upload.py，同步到資料庫
[2025-07-08 17:02:49,432] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 17:02:49,432] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.87ms
[2025-07-08 17:02:49,433] INFO in api_logger: [1751965369401] 請求完成: GET /script/ - 耗時: 29.28ms
[2025-07-08 17:02:49,610] INFO in api_logger: [1751965369609] 請求開始: GET /script/
[2025-07-08 17:02:49,611] DEBUG in api_logger: [1751965369609] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:02:49,630] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:02:49,644] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 31.97ms
[2025-07-08 17:02:49,645] INFO in api_logger: [1751965369609] 請求完成: GET /script/ - 耗時: 32.59ms
[2025-07-08 17:02:50,705] INFO in api_logger: [1751965370705] 請求開始: GET /script/
[2025-07-08 17:02:50,706] DEBUG in api_logger: [1751965370705] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:02:50,716] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:02:50,724] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.51ms
[2025-07-08 17:02:50,725] INFO in api_logger: [1751965370705] 請求完成: GET /script/ - 耗時: 17.43ms
[2025-07-08 17:02:57,453] INFO in api_logger: [1751965377453] 請求開始: GET /script/
[2025-07-08 17:02:57,454] DEBUG in api_logger: [1751965377453] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:02:57,466] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 17:02:57,472] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.88ms
[2025-07-08 17:02:57,473] INFO in api_logger: [1751965377453] 請求完成: GET /script/ - 耗時: 16.54ms
[2025-07-08 17:03:09,871] INFO in api_logger: [1751965389870] 請求開始: POST /script/validate
[2025-07-08 17:03:09,872] DEBUG in api_logger: [1751965389870] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "5959", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarysDUCAAPGupLSYO0P", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:03:09,874] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:03:10,011] INFO in script_routes: 腳本驗證完成: output_news.py, 有效: True
[2025-07-08 17:03:10,013] INFO in api_logger: [1751965389870] 請求完成: POST /script/validate - 耗時: 139.80ms
[2025-07-08 17:03:28,221] INFO in api_logger: [1751965408220] 請求開始: POST /script/
[2025-07-08 17:03:28,222] DEBUG in api_logger: [1751965408220] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6183", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryo7tHxxzXeAXgQV0F", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:03:28,223] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:03:28,224] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 17:03:28,228] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_news.py
[2025-07-08 17:03:28,255] INFO in script_routes: 腳本上傳成功: output_news.py
[2025-07-08 17:03:28,261] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.93ms
[2025-07-08 17:03:28,262] INFO in api_logger: [1751965408220] 請求完成: POST /script/ - 耗時: 38.45ms
[2025-07-08 17:03:37,949] INFO in api_logger: [1751965417949] 請求開始: GET /script/
[2025-07-08 17:03:37,950] DEBUG in api_logger: [1751965417949] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:03:37,957] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:03:37,963] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 12.42ms
[2025-07-08 17:03:37,964] INFO in api_logger: [1751965417949] 請求完成: GET /script/ - 耗時: 12.76ms
[2025-07-08 17:05:58,023] INFO in api_logger: [1751965558023] 請求開始: GET /script/
[2025-07-08 17:05:58,024] DEBUG in api_logger: [1751965558023] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:05:58,034] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:05:58,045] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.39ms
[2025-07-08 17:05:58,046] INFO in api_logger: [1751965558023] 請求完成: GET /script/ - 耗時: 21.03ms
[2025-07-08 17:06:31,122] INFO in api_logger: [1751965591122] 請求開始: GET /script/
[2025-07-08 17:06:31,123] DEBUG in api_logger: [1751965591122] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:06:31,129] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:06:31,133] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 9.00ms
[2025-07-08 17:06:31,133] INFO in api_logger: [1751965591122] 請求完成: GET /script/ - 耗時: 9.33ms
[2025-07-08 17:06:42,251] INFO in api_logger: [1751965602251] 請求開始: GET /script/
[2025-07-08 17:06:42,252] DEBUG in api_logger: [1751965602251] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (iPad; CPU OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:06:42,261] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:06:42,265] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.99ms
[2025-07-08 17:06:42,265] INFO in api_logger: [1751965602251] 請求完成: GET /script/ - 耗時: 12.48ms
[2025-07-08 17:08:16,060] INFO in api_logger: [1751965696060] 請求開始: POST /script/
[2025-07-08 17:08:16,061] DEBUG in api_logger: [1751965696060] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "500", "Content-Type": "multipart/form-data; boundary=e742702eae2809993fcb53fb54057280"}, "args": {}, "form": {"description": "網頁爬蟲測試腳本", "tags": "網頁爬蟲"}}
[2025-07-08 17:08:16,061] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:08:16,061] DEBUG in script_routes: 準備上傳腳本: web_crawler_test.py
[2025-07-08 17:08:16,066] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\web_crawler_test.py
[2025-07-08 17:08:16,087] INFO in script_routes: 腳本上傳成功: web_crawler_test.py
[2025-07-08 17:08:16,092] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 30.57ms
[2025-07-08 17:08:16,092] INFO in api_logger: [1751965696060] 請求完成: POST /script/ - 耗時: 30.88ms
[2025-07-08 17:08:58,230] INFO in api_logger: [1751965738228] 請求開始: GET /script/
[2025-07-08 17:08:58,230] DEBUG in api_logger: [1751965738228] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-TW) WindowsPowerShell/5.1.22621.4391", "Host": "localhost:5000", "Connection": "Keep-Alive"}, "args": {}}
[2025-07-08 17:08:58,240] INFO in script_routes: 成功獲取腳本列表，共 3 個腳本
[2025-07-08 17:08:58,245] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 14.68ms
[2025-07-08 17:08:58,246] INFO in api_logger: [1751965738228] 請求完成: GET /script/ - 耗時: 15.05ms
[2025-07-08 17:16:11,522] INFO in api_logger: [1751966171516] 請求開始: GET /script/
[2025-07-08 17:16:11,523] DEBUG in api_logger: [1751966171516] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:16:11,539] INFO in script_routes: 成功獲取腳本列表，共 3 個腳本
[2025-07-08 17:16:11,546] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.52ms
[2025-07-08 17:16:11,547] INFO in api_logger: [1751966171516] 請求完成: GET /script/ - 耗時: 22.79ms
[2025-07-08 17:16:36,534] INFO in api_logger: [1751966196533] 請求開始: POST /script/validate
[2025-07-08 17:16:36,534] DEBUG in api_logger: [1751966196533] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3522", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryylA5lpgrqzERYLl6", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:16:36,535] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:16:36,699] INFO in script_routes: 腳本驗證完成: output_booking.py, 有效: True
[2025-07-08 17:16:36,702] INFO in api_logger: [1751966196533] 請求完成: POST /script/validate - 耗時: 167.35ms
[2025-07-08 17:17:03,201] INFO in api_logger: [1751966223199] 請求開始: POST /script/
[2025-07-08 17:17:03,202] DEBUG in api_logger: [1751966223199] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3743", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarytB2fuJIHkAbu2IsJ", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "訂房網內容爬取", "tag": "檢核通知"}}
[2025-07-08 17:17:03,203] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:17:03,204] DEBUG in script_routes: 準備上傳腳本: output_booking.py
[2025-07-08 17:17:03,209] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_booking.py
[2025-07-08 17:17:03,237] INFO in script_routes: 腳本上傳成功: output_booking.py
[2025-07-08 17:17:03,241] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 38.07ms
[2025-07-08 17:17:03,242] INFO in api_logger: [1751966223199] 請求完成: POST /script/ - 耗時: 38.44ms
[2025-07-08 17:17:15,677] INFO in api_logger: [1751966235677] 請求開始: GET /script/
[2025-07-08 17:17:15,678] DEBUG in api_logger: [1751966235677] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:17:15,692] INFO in script_routes: 成功獲取腳本列表，共 4 個腳本
[2025-07-08 17:17:15,701] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 21.37ms
[2025-07-08 17:17:15,701] INFO in api_logger: [1751966235677] 請求完成: GET /script/ - 耗時: 21.69ms
[2025-07-08 17:18:15,019] INFO in api_logger: [1751966295018] 請求開始: POST /script/validate
[2025-07-08 17:18:15,019] DEBUG in api_logger: [1751966295018] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "443", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryMZxVA3GIB3iwmaGB", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:18:15,021] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:18:15,028] INFO in script_routes: 腳本驗證完成: test_script.py, 有效: True
[2025-07-08 17:18:15,032] INFO in api_logger: [1751966295018] 請求完成: POST /script/validate - 耗時: 11.13ms
[2025-07-08 17:18:20,754] INFO in api_logger: [1751966300754] 請求開始: GET /script/
[2025-07-08 17:18:20,755] DEBUG in api_logger: [1751966300754] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:18:20,764] INFO in script_routes: 成功獲取腳本列表，共 4 個腳本
[2025-07-08 17:18:20,770] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 14.59ms
[2025-07-08 17:18:20,771] INFO in api_logger: [1751966300754] 請求完成: GET /script/ - 耗時: 14.87ms
[2025-07-08 17:19:22,510] INFO in api_logger: [1751966362510] 請求開始: GET /script/
[2025-07-08 17:19:22,512] DEBUG in api_logger: [1751966362510] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:19:22,540] INFO in script_routes: 成功獲取腳本列表，共 4 個腳本
[2025-07-08 17:19:22,555] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 41.07ms
[2025-07-08 17:19:22,556] INFO in api_logger: [1751966362510] 請求完成: GET /script/ - 耗時: 42.12ms
[2025-07-08 17:22:29,828] INFO in api_logger: [1751966549824] 請求開始: GET /script/
[2025-07-08 17:22:29,830] DEBUG in api_logger: [1751966549824] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:22:29,842] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:22:29,912] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:22:29,941] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 108.04ms
[2025-07-08 17:22:29,941] INFO in api_logger: [1751966549824] 請求完成: GET /script/ - 耗時: 108.51ms
[2025-07-08 17:22:30,144] INFO in api_logger: [1751966550143] 請求開始: GET /script/
[2025-07-08 17:22:30,144] DEBUG in api_logger: [1751966550143] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:22:30,149] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:22:30,155] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:22:30,164] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 18.90ms
[2025-07-08 17:22:30,164] INFO in api_logger: [1751966550143] 請求完成: GET /script/ - 耗時: 19.23ms
[2025-07-08 17:22:34,797] INFO in api_logger: [1751966554797] 請求開始: GET /script/
[2025-07-08 17:22:34,798] DEBUG in api_logger: [1751966554797] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:22:34,809] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:22:34,815] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:22:34,830] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 31.65ms
[2025-07-08 17:22:34,831] INFO in api_logger: [1751966554797] 請求完成: GET /script/ - 耗時: 32.27ms
[2025-07-08 17:22:35,174] INFO in api_logger: [1751966555174] 請求開始: GET /script/
[2025-07-08 17:22:35,175] DEBUG in api_logger: [1751966555174] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:22:35,187] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:22:35,195] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:22:35,206] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 30.58ms
[2025-07-08 17:22:35,207] INFO in api_logger: [1751966555174] 請求完成: GET /script/ - 耗時: 31.06ms
[2025-07-08 17:23:16,395] INFO in api_logger: [1751966596395] 請求開始: GET /script/
[2025-07-08 17:23:16,395] DEBUG in api_logger: [1751966596395] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive"}, "args": {}}
[2025-07-08 17:23:16,466] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:23:16,477] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:23:16,497] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:23:16,522] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 60.72ms
[2025-07-08 17:23:16,530] INFO in api_logger: [1751966596395] 請求完成: GET /script/ - 耗時: 68.45ms
[2025-07-08 17:23:16,546] INFO in api_logger: [1751966596545] 請求開始: POST /script/
[2025-07-08 17:23:16,557] DEBUG in api_logger: [1751966596545] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "516", "Content-Type": "multipart/form-data; boundary=0373d71f3f70430d2a042e6c6153b7b0"}, "args": {}, "form": {"description": "檢核通知測試腳本", "tags": "檢核通知"}}
[2025-07-08 17:23:16,566] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:23:16,577] DEBUG in script_routes: 準備上傳腳本: notification_test.py
[2025-07-08 17:23:16,592] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\notification_test.py
[2025-07-08 17:23:16,632] INFO in script_routes: 腳本上傳成功: notification_test.py
[2025-07-08 17:23:16,648] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 82.16ms
[2025-07-08 17:23:16,660] INFO in api_logger: [1751966596545] 請求完成: POST /script/ - 耗時: 94.74ms
[2025-07-08 17:23:16,679] INFO in api_logger: [1751966596679] 請求開始: GET /script/
[2025-07-08 17:23:16,695] DEBUG in api_logger: [1751966596679] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive"}, "args": {}}
[2025-07-08 17:23:16,712] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:23:16,723] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:23:16,747] INFO in script_routes: 成功獲取腳本列表，共 7 個腳本
[2025-07-08 17:23:16,772] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 64.49ms
[2025-07-08 17:23:16,781] INFO in api_logger: [1751966596679] 請求完成: GET /script/ - 耗時: 73.78ms
[2025-07-08 17:25:20,493] INFO in api_logger: [1751966720493] 請求開始: GET /script/
[2025-07-08 17:25:20,536] DEBUG in api_logger: [1751966720493] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:25:20,594] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:25:20,607] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:25:20,618] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:25:20,642] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:25:20,675] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 92.47ms
[2025-07-08 17:25:20,688] INFO in api_logger: [1751966720493] 請求完成: GET /script/ - 耗時: 105.29ms
[2025-07-08 17:25:21,015] INFO in api_logger: [1751966721015] 請求開始: GET /script/
[2025-07-08 17:25:21,026] DEBUG in api_logger: [1751966721015] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:25:21,045] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:25:21,058] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:25:21,069] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:25:21,092] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:25:21,125] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 86.11ms
[2025-07-08 17:25:21,140] INFO in api_logger: [1751966721015] 請求完成: GET /script/ - 耗時: 100.64ms
[2025-07-08 17:27:22,651] INFO in api_logger: [1751966842650] 請求開始: GET /script/
[2025-07-08 17:27:22,652] DEBUG in api_logger: [1751966842650] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:27:22,658] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:27:22,658] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:27:22,659] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:27:22,668] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:27:22,681] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.02ms
[2025-07-08 17:27:22,681] INFO in api_logger: [1751966842650] 請求完成: GET /script/ - 耗時: 28.36ms
[2025-07-08 17:27:50,356] INFO in api_logger: [1751966870354] 請求開始: POST /script/validate
[2025-07-08 17:27:50,357] DEBUG in api_logger: [1751966870354] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryLcY9lSmD2QSnM7n2", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:27:50,358] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:27:50,551] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:27:50,553] INFO in api_logger: [1751966870354] 請求完成: POST /script/validate - 耗時: 195.31ms
[2025-07-08 17:28:15,184] INFO in api_logger: [1751966895183] 請求開始: POST /script/
[2025-07-08 17:28:15,185] DEBUG in api_logger: [1751966895183] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary8qDAcFudVHMVG5E3", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "備份還原"}}
[2025-07-08 17:28:15,186] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:28:15,187] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:28:15,192] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:28:15,220] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:28:15,224] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.99ms
[2025-07-08 17:28:15,225] INFO in api_logger: [1751966895183] 請求完成: POST /script/ - 耗時: 38.29ms
[2025-07-08 17:28:19,418] INFO in api_logger: [1751966899418] 請求開始: GET /script/
[2025-07-08 17:28:19,419] DEBUG in api_logger: [1751966899418] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:28:19,424] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:28:19,424] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:28:19,425] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:28:19,437] INFO in script_routes: 成功獲取腳本列表，共 9 個腳本
[2025-07-08 17:28:19,447] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.50ms
[2025-07-08 17:28:19,447] INFO in api_logger: [1751966899418] 請求完成: GET /script/ - 耗時: 27.86ms
[2025-07-08 17:28:52,417] INFO in api_logger: [1751966932417] 請求開始: GET /script/
[2025-07-08 17:28:52,417] DEBUG in api_logger: [1751966932417] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:28:52,422] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:28:52,423] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:28:52,423] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:28:52,435] INFO in script_routes: 成功獲取腳本列表，共 9 個腳本
[2025-07-08 17:28:52,447] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 29.13ms
[2025-07-08 17:28:52,448] INFO in api_logger: [1751966932417] 請求完成: GET /script/ - 耗時: 29.46ms
[2025-07-08 17:29:02,780] INFO in api_logger: [1751966942779] 請求開始: GET /script/
[2025-07-08 17:29:02,780] DEBUG in api_logger: [1751966942779] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:02,785] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:02,786] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:02,786] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:02,795] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:29:02,807] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 26.33ms
[2025-07-08 17:29:02,807] INFO in api_logger: [1751966942779] 請求完成: GET /script/ - 耗時: 26.69ms
[2025-07-08 17:29:07,351] INFO in api_logger: [1751966947351] 請求開始: GET /script/
[2025-07-08 17:29:07,352] DEBUG in api_logger: [1751966947351] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:07,356] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:07,357] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:07,358] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:07,368] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:29:07,380] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.31ms
[2025-07-08 17:29:07,380] INFO in api_logger: [1751966947351] 請求完成: GET /script/ - 耗時: 27.77ms
[2025-07-08 17:29:25,416] INFO in api_logger: [1751966965416] 請求開始: GET /script/
[2025-07-08 17:29:25,417] DEBUG in api_logger: [1751966965416] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:25,423] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:25,423] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:25,424] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:25,437] INFO in script_routes: 成功獲取腳本列表，共 7 個腳本
[2025-07-08 17:29:25,454] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 36.29ms
[2025-07-08 17:29:25,455] INFO in api_logger: [1751966965416] 請求完成: GET /script/ - 耗時: 36.92ms
[2025-07-08 17:29:30,514] INFO in api_logger: [1751966970513] 請求開始: GET /script/
[2025-07-08 17:29:30,515] DEBUG in api_logger: [1751966970513] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:30,523] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:30,524] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:30,525] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:30,531] INFO in script_routes: 成功獲取腳本列表，共 7 個腳本
[2025-07-08 17:29:30,542] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.21ms
[2025-07-08 17:29:30,542] INFO in api_logger: [1751966970513] 請求完成: GET /script/ - 耗時: 25.51ms
[2025-07-08 17:29:37,899] INFO in api_logger: [1751966977898] 請求開始: GET /script/
[2025-07-08 17:29:37,899] DEBUG in api_logger: [1751966977898] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:37,905] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:37,906] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:37,907] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:37,914] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:29:37,927] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 26.33ms
[2025-07-08 17:29:37,927] INFO in api_logger: [1751966977898] 請求完成: GET /script/ - 耗時: 26.75ms
[2025-07-08 17:29:45,453] INFO in api_logger: [1751966985453] 請求開始: GET /script/
[2025-07-08 17:29:45,453] DEBUG in api_logger: [1751966985453] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:45,457] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:45,458] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:45,458] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:45,467] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:29:45,475] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.49ms
[2025-07-08 17:29:45,475] INFO in api_logger: [1751966985453] 請求完成: GET /script/ - 耗時: 20.95ms
[2025-07-08 17:29:47,866] INFO in api_logger: [1751966987866] 請求開始: GET /script/
[2025-07-08 17:29:47,867] DEBUG in api_logger: [1751966987866] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:47,877] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:47,878] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:47,879] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:47,885] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:29:47,893] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.27ms
[2025-07-08 17:29:47,893] INFO in api_logger: [1751966987866] 請求完成: GET /script/ - 耗時: 23.61ms
[2025-07-08 17:29:56,285] INFO in api_logger: [1751966996284] 請求開始: POST /script/validate
[2025-07-08 17:29:56,285] DEBUG in api_logger: [1751966996284] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryVwkCa1kJG0QIR41j", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:56,287] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:29:56,318] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:29:56,325] INFO in api_logger: [1751966996284] 請求完成: POST /script/validate - 耗時: 37.17ms
[2025-07-08 17:30:06,052] INFO in api_logger: [1751967006052] 請求開始: POST /script/
[2025-07-08 17:30:06,053] DEBUG in api_logger: [1751967006052] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryTPk7n5a2gwsHxavY", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:30:06,054] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:30:06,055] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:30:06,061] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:30:06,094] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:30:06,101] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 46.65ms
[2025-07-08 17:30:06,102] INFO in api_logger: [1751967006052] 請求完成: POST /script/ - 耗時: 47.32ms
[2025-07-08 17:30:11,866] INFO in api_logger: [1751967011862] 請求開始: GET /script/
[2025-07-08 17:30:11,867] DEBUG in api_logger: [1751967011862] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:30:11,873] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:30:11,874] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:30:11,874] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:30:11,882] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:30:11,893] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.41ms
[2025-07-08 17:30:11,894] INFO in api_logger: [1751967011862] 請求完成: GET /script/ - 耗時: 25.77ms
[2025-07-08 17:32:04,336] INFO in api_logger: [1751967124333] 請求開始: GET /script/
[2025-07-08 17:32:04,337] DEBUG in api_logger: [1751967124333] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:32:04,344] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:32:04,344] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:32:04,345] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:32:04,354] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:32:04,365] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.14ms
[2025-07-08 17:32:04,366] INFO in api_logger: [1751967124333] 請求完成: GET /script/ - 耗時: 27.53ms
[2025-07-08 17:32:19,517] INFO in api_logger: [1751967139514] 請求開始: GET /script/
[2025-07-08 17:32:19,518] DEBUG in api_logger: [1751967139514] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:32:19,526] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:32:19,527] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:32:19,528] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:32:19,536] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:32:19,547] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.28ms
[2025-07-08 17:32:19,547] INFO in api_logger: [1751967139514] 請求完成: GET /script/ - 耗時: 27.68ms
[2025-07-08 17:33:20,798] INFO in api_logger: [1751967200798] 請求開始: GET /script/
[2025-07-08 17:33:20,802] DEBUG in api_logger: [1751967200798] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:20,813] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:20,814] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:20,815] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:20,821] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:33:20,840] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.03ms
[2025-07-08 17:33:20,840] INFO in api_logger: [1751967200798] 請求完成: GET /script/ - 耗時: 35.47ms
[2025-07-08 17:33:21,114] INFO in api_logger: [1751967201114] 請求開始: GET /script/
[2025-07-08 17:33:21,115] DEBUG in api_logger: [1751967201114] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:21,120] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:21,120] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:21,121] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:21,125] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:33:21,134] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 17.78ms
[2025-07-08 17:33:21,134] INFO in api_logger: [1751967201114] 請求完成: GET /script/ - 耗時: 18.10ms
[2025-07-08 17:33:22,700] INFO in api_logger: [1751967202700] 請求開始: GET /script/
[2025-07-08 17:33:22,701] DEBUG in api_logger: [1751967202700] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:22,704] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:22,705] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:22,705] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:22,714] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:33:22,724] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.05ms
[2025-07-08 17:33:22,724] INFO in api_logger: [1751967202700] 請求完成: GET /script/ - 耗時: 23.35ms
[2025-07-08 17:33:27,929] INFO in api_logger: [1751967207929] 請求開始: GET /script/
[2025-07-08 17:33:27,931] DEBUG in api_logger: [1751967207929] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:27,939] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:27,940] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:27,942] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:27,948] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:33:27,958] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.94ms
[2025-07-08 17:33:27,959] INFO in api_logger: [1751967207929] 請求完成: GET /script/ - 耗時: 26.42ms
[2025-07-08 17:33:49,764] INFO in api_logger: [1751967229763] 請求開始: POST /script/validate
[2025-07-08 17:33:49,764] DEBUG in api_logger: [1751967229763] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary3sPCws3SRUfyU2MC", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:49,765] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:33:49,877] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:33:49,880] INFO in api_logger: [1751967229763] 請求完成: POST /script/validate - 耗時: 115.32ms
[2025-07-08 17:34:26,171] INFO in api_logger: [1751967266170] 請求開始: POST /script/
[2025-07-08 17:34:26,172] DEBUG in api_logger: [1751967266170] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryoTDraM0oAHMcyZCu", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:34:26,173] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:34:26,174] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:34:26,178] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:34:26,186] INFO in script_routes: 腳本上傳調試信息:
[2025-07-08 17:34:26,187] INFO in script_routes:   文件名: output_www.py
[2025-07-08 17:34:26,187] INFO in script_routes:   描述: 官網資訊爬取
[2025-07-08 17:34:26,188] INFO in script_routes:   標籤: 未分類
[2025-07-08 17:34:26,188] INFO in script_routes:   表單數據: {'description': '官網資訊爬取', 'tag': '網頁爬蟲'}
[2025-07-08 17:34:26,208] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:34:26,214] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 40.85ms
[2025-07-08 17:34:26,215] INFO in api_logger: [1751967266170] 請求完成: POST /script/ - 耗時: 41.95ms
[2025-07-08 17:34:42,617] INFO in api_logger: [1751967282614] 請求開始: GET /script/
[2025-07-08 17:34:42,618] DEBUG in api_logger: [1751967282614] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:34:42,624] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:34:42,625] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:34:42,626] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:34:42,634] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:34:42,656] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 36.75ms
[2025-07-08 17:34:42,657] INFO in api_logger: [1751967282614] 請求完成: GET /script/ - 耗時: 37.35ms
[2025-07-08 17:38:02,698] INFO in api_logger: [1751967482696] 請求開始: GET /script/
[2025-07-08 17:38:02,699] DEBUG in api_logger: [1751967482696] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:02,704] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:02,704] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:02,705] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:02,715] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:02,727] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.82ms
[2025-07-08 17:38:02,728] INFO in api_logger: [1751967482696] 請求完成: GET /script/ - 耗時: 28.32ms
[2025-07-08 17:38:02,930] INFO in api_logger: [1751967482930] 請求開始: GET /script/
[2025-07-08 17:38:02,931] DEBUG in api_logger: [1751967482930] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:02,944] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:02,948] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:02,950] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:02,956] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:02,970] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.97ms
[2025-07-08 17:38:02,970] INFO in api_logger: [1751967482930] 請求完成: GET /script/ - 耗時: 36.48ms
[2025-07-08 17:38:05,028] INFO in api_logger: [1751967485027] 請求開始: GET /script/
[2025-07-08 17:38:05,029] DEBUG in api_logger: [1751967485027] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:05,037] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:05,037] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:05,038] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:05,045] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:05,055] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.80ms
[2025-07-08 17:38:05,055] INFO in api_logger: [1751967485027] 請求完成: GET /script/ - 耗時: 24.13ms
[2025-07-08 17:38:09,075] INFO in api_logger: [1751967489075] 請求開始: GET /script/
[2025-07-08 17:38:09,076] DEBUG in api_logger: [1751967489075] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:09,080] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:09,081] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:09,081] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:09,085] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:38:09,092] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.70ms
[2025-07-08 17:38:09,093] INFO in api_logger: [1751967489075] 請求完成: GET /script/ - 耗時: 16.07ms
[2025-07-08 17:38:15,879] INFO in api_logger: [1751967495878] 請求開始: POST /script/validate
[2025-07-08 17:38:15,880] DEBUG in api_logger: [1751967495878] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryHg3UCjCJQ4isrzxt", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:15,881] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:38:16,021] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:38:16,024] INFO in api_logger: [1751967495878] 請求完成: POST /script/validate - 耗時: 143.41ms
[2025-07-08 17:38:26,197] INFO in api_logger: [1751967506195] 請求開始: POST /script/
[2025-07-08 17:38:26,198] DEBUG in api_logger: [1751967506195] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarypOeYwEgYY6OQrAuc", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:38:26,200] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:38:26,200] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:38:26,207] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:38:26,216] INFO in script_routes: 腳本上傳調試信息:
[2025-07-08 17:38:26,217] INFO in script_routes:   文件名: output_www.py
[2025-07-08 17:38:26,217] INFO in script_routes:   描述: 官網資訊爬取
[2025-07-08 17:38:26,217] INFO in script_routes:   標籤: 未分類
[2025-07-08 17:38:26,218] INFO in script_routes:   表單數據: {'description': '官網資訊爬取', 'tag': '網頁爬蟲'}
[2025-07-08 17:38:26,233] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:38:26,237] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.38ms
[2025-07-08 17:38:26,237] INFO in api_logger: [1751967506195] 請求完成: POST /script/ - 耗時: 37.71ms
