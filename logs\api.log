[2025-06-12 13:33:47,094] INFO in api_logger: [1749706427093] 請求開始: GET /script/
[2025-06-12 13:33:47,099] DEBUG in api_logger: [1749706427093] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:47,108] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:47,110] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.13ms
[2025-06-12 13:33:47,110] INFO in api_logger: [1749706427093] 請求完成: GET /script/ - 耗時: 8.70ms
[2025-06-12 13:33:47,140] INFO in api_logger: [1749706427140] 請求開始: GET /script/
[2025-06-12 13:33:47,141] DEBUG in api_logger: [1749706427140] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:47,151] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:47,153] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.91ms
[2025-06-12 13:33:47,153] INFO in api_logger: [1749706427140] 請求完成: GET /script/ - 耗時: 9.46ms
[2025-06-12 13:33:58,585] INFO in api_logger: [1749706438585] 請求開始: GET /script/
[2025-06-12 13:33:58,586] DEBUG in api_logger: [1749706438585] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:58,595] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:58,597] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.67ms
[2025-06-12 13:33:58,598] INFO in api_logger: [1749706438585] 請求完成: GET /script/ - 耗時: 7.81ms
[2025-06-12 13:35:31,994] INFO in api_logger: [1749706531994] 請求開始: GET /script/
[2025-06-12 13:35:31,995] DEBUG in api_logger: [1749706531994] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:35:32,002] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:35:32,003] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.99ms
[2025-06-12 13:35:32,004] INFO in api_logger: [1749706531994] 請求完成: GET /script/ - 耗時: 7.50ms
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:57:18,774] INFO in api_logger: [1751957838772] 請求開始: GET /script/
[2025-07-08 14:57:18,774] DEBUG in api_logger: [1751957838772] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:57:18,778] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:57:18,780] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:57:18,780] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.28ms
[2025-07-08 14:57:18,780] INFO in api_logger: [1751957838772] 請求完成: GET /script/ - 耗時: 5.41ms
[2025-07-08 14:57:29,699] INFO in api_logger: [1751957849697] 請求開始: GET /script/
[2025-07-08 14:57:29,699] DEBUG in api_logger: [1751957849697] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:57:29,702] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:57:29,703] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.31ms
[2025-07-08 14:57:29,703] INFO in api_logger: [1751957849697] 請求完成: GET /script/ - 耗時: 4.42ms
[2025-07-08 14:58:19,439] INFO in api_logger: [1751957899437] 請求開始: GET /script/info_test.py
[2025-07-08 14:58:19,439] INFO in api_logger: [1751957899437] 請求開始: GET /script/info_test.py
[2025-07-08 14:58:19,439] INFO in api_logger: [1751957899437] 請求開始: GET /script/info_test.py
[2025-07-08 14:58:19,440] DEBUG in api_logger: [1751957899437] 請求數據: {"method": "GET", "path": "/script/info_test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,440] DEBUG in api_logger: [1751957899437] 請求數據: {"method": "GET", "path": "/script/info_test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,440] DEBUG in api_logger: [1751957899437] 請求數據: {"method": "GET", "path": "/script/info_test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,441] WARNING in script_routes: 腳本文件不存在: /fake/path/info_test.py
[2025-07-08 14:58:19,441] WARNING in script_routes: 腳本文件不存在: /fake/path/info_test.py
[2025-07-08 14:58:19,441] WARNING in script_routes: 腳本文件不存在: /fake/path/info_test.py
[2025-07-08 14:58:19,442] INFO in api_logger: [1751957899437] 請求完成: GET /script/info_test.py - 耗時: 1.67ms
[2025-07-08 14:58:19,442] INFO in api_logger: [1751957899437] 請求完成: GET /script/info_test.py - 耗時: 1.67ms
[2025-07-08 14:58:19,442] INFO in api_logger: [1751957899437] 請求完成: GET /script/info_test.py - 耗時: 1.67ms
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:56,331] INFO in api_logger: [1751957936330] 請求開始: GET /script/
[2025-07-08 14:58:56,332] DEBUG in api_logger: [1751957936330] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:56,336] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:56,336] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.59ms
[2025-07-08 14:58:56,337] INFO in api_logger: [1751957936330] 請求完成: GET /script/ - 耗時: 3.97ms
[2025-07-08 14:59:12,860] INFO in api_logger: [1751957952858] 請求開始: GET /script/
[2025-07-08 14:59:12,861] DEBUG in api_logger: [1751957952858] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:59:12,863] WARNING in script_routes: 資料庫中的腳本 test.py 文件不存在於磁碟: /fake/path/test.py
[2025-07-08 14:59:12,864] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:59:12,867] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.46ms
[2025-07-08 14:59:12,868] INFO in api_logger: [1751957952858] 請求完成: GET /script/ - 耗時: 6.91ms
[2025-07-08 14:59:12,872] INFO in api_logger: [1751957952871] 請求開始: GET /script/test.py
[2025-07-08 14:59:12,872] DEBUG in api_logger: [1751957952871] 請求數據: {"method": "GET", "path": "/script/test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:59:12,877] WARNING in script_routes: 腳本文件不存在: /fake/path/test.py
[2025-07-08 14:59:12,877] INFO in api_logger: [1751957952871] 請求完成: GET /script/test.py - 耗時: 4.58ms
[2025-07-08 14:59:26,626] INFO in api_logger: [1751957966624] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:59:26,627] DEBUG in api_logger: [1751957966624] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:59:26,628] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:59:26,629] INFO in api_logger: [1751957966624] 請求完成: GET /script/nonexistent.py - 耗時: 1.62ms
[2025-07-08 15:02:34,412] INFO in api_logger: [1751958154410] 請求開始: GET /script/
[2025-07-08 15:02:34,413] DEBUG in api_logger: [1751958154410] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:02:34,418] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:02:34,418] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.67ms
[2025-07-08 15:02:34,419] INFO in api_logger: [1751958154410] 請求完成: GET /script/ - 耗時: 5.09ms
[2025-07-08 15:02:34,421] INFO in api_logger: [1751958154421] 請求開始: GET /script/
[2025-07-08 15:02:34,421] DEBUG in api_logger: [1751958154421] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:02:34,428] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:02:34,429] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.71ms
[2025-07-08 15:02:34,430] INFO in api_logger: [1751958154421] 請求完成: GET /script/ - 耗時: 7.23ms
[2025-07-08 15:02:56,971] INFO in api_logger: [1751958176971] 請求開始: GET /script/
[2025-07-08 15:02:56,972] DEBUG in api_logger: [1751958176971] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:02:56,976] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:02:56,977] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.98ms
[2025-07-08 15:02:56,977] INFO in api_logger: [1751958176971] 請求完成: GET /script/ - 耗時: 4.30ms
[2025-07-08 15:03:47,312] INFO in api_logger: [1751958227310] 請求開始: GET /script/
[2025-07-08 15:03:47,312] DEBUG in api_logger: [1751958227310] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:03:47,313] WARNING in script_routes: 資料庫中的腳本 integration_manual_test.py 文件不存在於磁碟: /fake/path/test.py
[2025-07-08 15:03:47,314] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 15:03:47,315] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.06ms
[2025-07-08 15:03:47,316] INFO in api_logger: [1751958227310] 請求完成: GET /script/ - 耗時: 3.30ms
[2025-07-08 15:13:40,648] INFO in api_logger: [1751958820648] 請求開始: GET /script/
[2025-07-08 15:13:40,649] DEBUG in api_logger: [1751958820648] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:13:40,655] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:13:40,656] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.17ms
[2025-07-08 15:13:40,657] INFO in api_logger: [1751958820648] 請求完成: GET /script/ - 耗時: 5.73ms
[2025-07-08 15:15:40,875] INFO in api_logger: [1751958940875] 請求開始: GET /script/
[2025-07-08 15:15:40,876] DEBUG in api_logger: [1751958940875] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:15:40,880] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:15:40,880] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.31ms
[2025-07-08 15:15:40,880] INFO in api_logger: [1751958940875] 請求完成: GET /script/ - 耗時: 3.66ms
[2025-07-08 15:15:49,263] INFO in api_logger: [1751958949263] 請求開始: GET /script/
[2025-07-08 15:15:49,264] DEBUG in api_logger: [1751958949263] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:15:49,269] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:15:49,270] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.83ms
[2025-07-08 15:15:49,271] INFO in api_logger: [1751958949263] 請求完成: GET /script/ - 耗時: 6.30ms
[2025-07-08 15:15:54,453] INFO in api_logger: [1751958954452] 請求開始: GET /script/
[2025-07-08 15:15:54,453] DEBUG in api_logger: [1751958954452] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:15:54,457] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:15:54,458] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.60ms
[2025-07-08 15:15:54,459] INFO in api_logger: [1751958954452] 請求完成: GET /script/ - 耗時: 4.32ms
[2025-07-08 15:17:54,377] INFO in api_logger: [1751959074376] 請求開始: POST /script/validate
[2025-07-08 15:17:54,377] DEBUG in api_logger: [1751959074376] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "215", "Content-Type": "multipart/form-data; boundary=5c0245b3671ca3f5be2fa8ac312eec2b"}, "args": {}}
[2025-07-08 15:17:54,378] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:17:54,378] ERROR in script_routes: 腳本執行器未初始化
[2025-07-08 15:17:54,379] INFO in api_logger: [1751959074376] 請求完成: POST /script/validate - 耗時: 0.78ms
[2025-07-08 15:19:18,555] INFO in api_logger: [1751959158555] 請求開始: POST /script/validate
[2025-07-08 15:19:18,556] DEBUG in api_logger: [1751959158555] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "215", "Content-Type": "multipart/form-data; boundary=0d6935327ed090522707c3c7f7173520"}, "args": {}}
[2025-07-08 15:19:18,557] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:19:18,567] INFO in script_routes: 腳本驗證完成: test.py, 有效: True
[2025-07-08 15:19:18,569] INFO in api_logger: [1751959158555] 請求完成: POST /script/validate - 耗時: 12.43ms
[2025-07-08 15:22:35,371] INFO in api_logger: [1751959355368] 請求開始: GET /script/
[2025-07-08 15:22:35,371] DEBUG in api_logger: [1751959355368] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,373] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,374] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,374] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,374] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,375] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,375] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,376] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,376] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,376] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,377] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,377] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,387] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.41ms
[2025-07-08 15:22:35,387] INFO in api_logger: [1751959355368] 請求完成: GET /script/ - 耗時: 15.77ms
[2025-07-08 15:22:35,388] INFO in api_logger: [1751959355388] 請求開始: GET /script/
[2025-07-08 15:22:35,388] DEBUG in api_logger: [1751959355388] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,389] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,390] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,390] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,390] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,390] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,391] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,391] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,391] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,391] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,392] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,392] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,400] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 10.97ms
[2025-07-08 15:22:35,400] INFO in api_logger: [1751959355388] 請求完成: GET /script/ - 耗時: 11.24ms
[2025-07-08 15:22:35,400] INFO in api_logger: [1751959355400] 請求開始: GET /script/
[2025-07-08 15:22:35,401] DEBUG in api_logger: [1751959355400] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,402] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,402] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,402] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,402] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,403] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,403] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,403] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,403] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,404] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,404] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,405] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,412] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.03ms
[2025-07-08 15:22:35,413] INFO in api_logger: [1751959355400] 請求完成: GET /script/ - 耗時: 11.56ms
[2025-07-08 15:22:35,414] INFO in api_logger: [1751959355414] 請求開始: GET /script/
[2025-07-08 15:22:35,414] DEBUG in api_logger: [1751959355414] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,417] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,417] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,417] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,418] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,418] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,418] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,418] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,419] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,419] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,419] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,420] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,426] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.28ms
[2025-07-08 15:22:35,426] INFO in api_logger: [1751959355414] 請求完成: GET /script/ - 耗時: 11.53ms
[2025-07-08 15:22:35,427] INFO in api_logger: [1751959355427] 請求開始: GET /script/
[2025-07-08 15:22:35,427] DEBUG in api_logger: [1751959355427] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,428] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,429] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,429] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,429] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,430] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,430] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,431] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,431] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,431] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,431] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,433] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,440] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 12.03ms
[2025-07-08 15:22:35,440] INFO in api_logger: [1751959355427] 請求完成: GET /script/ - 耗時: 12.24ms
[2025-07-08 15:22:35,440] INFO in api_logger: [1751959355440] 請求開始: GET /script/
[2025-07-08 15:22:35,441] DEBUG in api_logger: [1751959355440] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,441] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,442] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,442] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,442] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,443] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,443] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,443] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,443] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,444] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,444] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,445] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,453] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.74ms
[2025-07-08 15:22:35,453] INFO in api_logger: [1751959355440] 請求完成: GET /script/ - 耗時: 11.99ms
[2025-07-08 15:22:35,453] INFO in api_logger: [1751959355453] 請求開始: GET /script/
[2025-07-08 15:22:35,454] DEBUG in api_logger: [1751959355453] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,455] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,455] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,455] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,455] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,456] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,456] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,456] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,456] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,457] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,457] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,458] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,463] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 9.51ms
[2025-07-08 15:22:35,464] INFO in api_logger: [1751959355453] 請求完成: GET /script/ - 耗時: 9.75ms
[2025-07-08 15:22:35,465] INFO in api_logger: [1751959355465] 請求開始: GET /script/
[2025-07-08 15:22:35,466] DEBUG in api_logger: [1751959355465] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,467] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,467] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,467] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,468] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,468] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,468] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,468] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,469] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,469] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,469] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,470] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,475] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 9.27ms
[2025-07-08 15:22:35,476] INFO in api_logger: [1751959355465] 請求完成: GET /script/ - 耗時: 9.48ms
[2025-07-08 15:22:35,476] INFO in api_logger: [1751959355476] 請求開始: GET /script/
[2025-07-08 15:22:35,476] DEBUG in api_logger: [1751959355476] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,477] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,478] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,478] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,478] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,478] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,479] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,479] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,479] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,480] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,480] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,482] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,527] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 50.18ms
[2025-07-08 15:22:35,527] INFO in api_logger: [1751959355476] 請求完成: GET /script/ - 耗時: 50.59ms
[2025-07-08 15:22:35,535] INFO in api_logger: [1751959355534] 請求開始: GET /script/
[2025-07-08 15:22:35,538] DEBUG in api_logger: [1751959355534] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:22:35,540] WARNING in script_routes: 資料庫中的腳本 perf_test_0.py 文件不存在於磁碟: /fake/path/perf_test_0.py
[2025-07-08 15:22:35,541] WARNING in script_routes: 資料庫中的腳本 perf_test_1.py 文件不存在於磁碟: /fake/path/perf_test_1.py
[2025-07-08 15:22:35,541] WARNING in script_routes: 資料庫中的腳本 perf_test_2.py 文件不存在於磁碟: /fake/path/perf_test_2.py
[2025-07-08 15:22:35,542] WARNING in script_routes: 資料庫中的腳本 perf_test_3.py 文件不存在於磁碟: /fake/path/perf_test_3.py
[2025-07-08 15:22:35,542] WARNING in script_routes: 資料庫中的腳本 perf_test_4.py 文件不存在於磁碟: /fake/path/perf_test_4.py
[2025-07-08 15:22:35,543] WARNING in script_routes: 資料庫中的腳本 perf_test_5.py 文件不存在於磁碟: /fake/path/perf_test_5.py
[2025-07-08 15:22:35,543] WARNING in script_routes: 資料庫中的腳本 perf_test_6.py 文件不存在於磁碟: /fake/path/perf_test_6.py
[2025-07-08 15:22:35,544] WARNING in script_routes: 資料庫中的腳本 perf_test_7.py 文件不存在於磁碟: /fake/path/perf_test_7.py
[2025-07-08 15:22:35,544] WARNING in script_routes: 資料庫中的腳本 perf_test_8.py 文件不存在於磁碟: /fake/path/perf_test_8.py
[2025-07-08 15:22:35,545] WARNING in script_routes: 資料庫中的腳本 perf_test_9.py 文件不存在於磁碟: /fake/path/perf_test_9.py
[2025-07-08 15:22:35,546] INFO in script_routes: 成功獲取腳本列表，共 10 個腳本
[2025-07-08 15:22:35,555] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.15ms
[2025-07-08 15:22:35,555] INFO in api_logger: [1751959355534] 請求完成: GET /script/ - 耗時: 16.36ms
[2025-07-08 15:41:37,727] INFO in api_logger: [1751960497725] 請求開始: GET /script/
[2025-07-08 15:41:37,728] DEBUG in api_logger: [1751960497725] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:41:37,732] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:41:37,733] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.37ms
[2025-07-08 15:41:37,733] INFO in api_logger: [1751960497725] 請求完成: GET /script/ - 耗時: 4.76ms
[2025-07-08 15:41:37,970] INFO in api_logger: [1751960497970] 請求開始: GET /script/
[2025-07-08 15:41:37,971] DEBUG in api_logger: [1751960497970] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:41:37,975] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:41:37,976] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.79ms
[2025-07-08 15:41:37,976] INFO in api_logger: [1751960497970] 請求完成: GET /script/ - 耗時: 4.22ms
[2025-07-08 15:41:40,271] INFO in api_logger: [1751960500271] 請求開始: GET /script/
[2025-07-08 15:41:40,272] DEBUG in api_logger: [1751960500271] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:41:40,275] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:41:40,276] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.18ms
[2025-07-08 15:41:40,276] INFO in api_logger: [1751960500271] 請求完成: GET /script/ - 耗時: 3.45ms
[2025-07-08 15:41:52,483] INFO in api_logger: [1751960512482] 請求開始: POST /script/validate
[2025-07-08 15:41:52,484] DEBUG in api_logger: [1751960512482] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3522", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryDH3BONZzpoXsCNhA", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:41:52,485] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:41:52,668] INFO in script_routes: 腳本驗證完成: output_booking.py, 有效: True
[2025-07-08 15:41:52,671] INFO in api_logger: [1751960512482] 請求完成: POST /script/validate - 耗時: 186.36ms
[2025-07-08 15:42:06,419] INFO in api_logger: [1751960526419] 請求開始: POST /script/validate
[2025-07-08 15:42:06,420] DEBUG in api_logger: [1751960526419] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "443", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryAxACv9FMgrAX6uAE", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:06,421] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:42:06,425] INFO in script_routes: 腳本驗證完成: test_script.py, 有效: True
[2025-07-08 15:42:06,428] INFO in api_logger: [1751960526419] 請求完成: POST /script/validate - 耗時: 6.94ms
[2025-07-08 15:42:17,904] INFO in api_logger: [1751960537903] 請求開始: POST /script/validate
[2025-07-08 15:42:17,905] DEBUG in api_logger: [1751960537903] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "5959", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarySAQuOrTmy3x9kITL", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:17,907] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 15:42:17,916] INFO in script_routes: 腳本驗證完成: output_news.py, 有效: True
[2025-07-08 15:42:17,920] INFO in api_logger: [1751960537903] 請求完成: POST /script/validate - 耗時: 12.58ms
[2025-07-08 15:42:42,915] INFO in api_logger: [1751960562915] 請求開始: GET /script/
[2025-07-08 15:42:42,916] DEBUG in api_logger: [1751960562915] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:42,919] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:42:42,920] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.67ms
[2025-07-08 15:42:42,920] INFO in api_logger: [1751960562915] 請求完成: GET /script/ - 耗時: 4.00ms
[2025-07-08 15:42:46,074] INFO in api_logger: [1751960566073] 請求開始: GET /script/
[2025-07-08 15:42:46,074] DEBUG in api_logger: [1751960566073] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:46,078] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:42:46,078] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.46ms
[2025-07-08 15:42:46,079] INFO in api_logger: [1751960566073] 請求完成: GET /script/ - 耗時: 3.93ms
[2025-07-08 15:42:46,353] INFO in api_logger: [1751960566353] 請求開始: GET /script/
[2025-07-08 15:42:46,354] DEBUG in api_logger: [1751960566353] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:46,357] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:42:46,358] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.52ms
[2025-07-08 15:42:46,359] INFO in api_logger: [1751960566353] 請求完成: GET /script/ - 耗時: 3.88ms
[2025-07-08 15:42:48,249] INFO in api_logger: [1751960568249] 請求開始: GET /script/
[2025-07-08 15:42:48,250] DEBUG in api_logger: [1751960568249] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:42:48,252] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:42:48,253] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 2.69ms
[2025-07-08 15:42:48,253] INFO in api_logger: [1751960568249] 請求完成: GET /script/ - 耗時: 3.00ms
[2025-07-08 16:21:34,140] INFO in api_logger: [1751962894140] 請求開始: GET /script/
[2025-07-08 16:21:34,141] DEBUG in api_logger: [1751962894140] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:21:34,145] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:21:34,145] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.47ms
[2025-07-08 16:21:34,146] INFO in api_logger: [1751962894140] 請求完成: GET /script/ - 耗時: 3.87ms
[2025-07-08 16:21:59,352] INFO in api_logger: [1751962919351] 請求開始: POST /script/validate
[2025-07-08 16:21:59,353] DEBUG in api_logger: [1751962919351] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6002", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryZwqu6xAsQC5bUXoy", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:21:59,354] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 16:21:59,360] INFO in script_routes: 腳本驗證完成: output_news.py, 有效: True
[2025-07-08 16:21:59,364] INFO in api_logger: [1751962919351] 請求完成: POST /script/validate - 耗時: 10.21ms
[2025-07-08 16:23:15,913] INFO in api_logger: [1751962995913] 請求開始: POST /script/
[2025-07-08 16:23:15,914] DEBUG in api_logger: [1751962995913] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6226", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarybZ6U6jVrcysNB8hC", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:23:15,915] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:23:15,915] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 16:23:15,920] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('output_news.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:23:15,921] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 6.07ms
[2025-07-08 16:23:15,921] INFO in api_logger: [1751962995913] 請求完成: POST /script/ - 耗時: 6.37ms
[2025-07-08 16:23:24,583] INFO in api_logger: [1751963004582] 請求開始: POST /script/
[2025-07-08 16:23:24,584] DEBUG in api_logger: [1751963004582] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6226", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryyd3Iru5U7qi2Tx99", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:23:24,585] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:23:24,585] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 16:23:24,589] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('output_news.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:23:24,590] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 5.52ms
[2025-07-08 16:23:24,591] INFO in api_logger: [1751963004582] 請求完成: POST /script/ - 耗時: 6.03ms
[2025-07-08 16:23:31,248] INFO in api_logger: [1751963011248] 請求開始: POST /script/
[2025-07-08 16:23:31,249] DEBUG in api_logger: [1751963011248] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6226", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary0py9VBb1YlcM0tuw", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:23:31,250] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:23:31,250] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 16:23:31,253] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('output_news.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:23:31,254] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 3.89ms
[2025-07-08 16:23:31,254] INFO in api_logger: [1751963011248] 請求完成: POST /script/ - 耗時: 4.12ms
[2025-07-08 16:23:50,656] INFO in api_logger: [1751963030656] 請求開始: GET /script/
[2025-07-08 16:23:50,657] DEBUG in api_logger: [1751963030656] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:23:50,661] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:23:50,661] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.00ms
[2025-07-08 16:23:50,662] INFO in api_logger: [1751963030656] 請求完成: GET /script/ - 耗時: 4.45ms
[2025-07-08 16:28:57,273] INFO in api_logger: [1751963337273] 請求開始: GET /script/
[2025-07-08 16:28:57,274] DEBUG in api_logger: [1751963337273] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:28:57,278] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:28:57,278] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.48ms
[2025-07-08 16:28:57,279] INFO in api_logger: [1751963337273] 請求完成: GET /script/ - 耗時: 3.88ms
[2025-07-08 16:30:52,774] INFO in api_logger: [1751963452774] 請求開始: GET /script/
[2025-07-08 16:30:52,775] DEBUG in api_logger: [1751963452774] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:30:52,779] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:30:52,780] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.91ms
[2025-07-08 16:30:52,781] INFO in api_logger: [1751963452774] 請求完成: GET /script/ - 耗時: 5.22ms
[2025-07-08 16:31:46,261] INFO in api_logger: [1751963506260] 請求開始: POST /script/
[2025-07-08 16:31:46,261] DEBUG in api_logger: [1751963506260] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "332", "Content-Type": "multipart/form-data; boundary=550b2658a923b4997f0303d862b8e08e"}, "args": {}, "form": {"description": "測試上傳腳本"}}
[2025-07-08 16:31:46,262] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:31:46,262] DEBUG in script_routes: 準備上傳腳本: test_upload.py
[2025-07-08 16:31:46,267] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('test_upload.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:31:46,268] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 6.07ms
[2025-07-08 16:31:46,268] INFO in api_logger: [1751963506260] 請求完成: POST /script/ - 耗時: 6.51ms
[2025-07-08 16:32:36,368] INFO in api_logger: [1751963556360] 請求開始: GET /script/
[2025-07-08 16:32:36,368] DEBUG in api_logger: [1751963556360] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:32:36,376] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:32:36,376] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.71ms
[2025-07-08 16:32:36,377] INFO in api_logger: [1751963556360] 請求完成: GET /script/ - 耗時: 7.19ms
[2025-07-08 16:32:36,673] INFO in api_logger: [1751963556673] 請求開始: GET /script/
[2025-07-08 16:32:36,673] DEBUG in api_logger: [1751963556673] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:32:36,677] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:32:36,678] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.69ms
[2025-07-08 16:32:36,678] INFO in api_logger: [1751963556673] 請求完成: GET /script/ - 耗時: 4.06ms
[2025-07-08 16:33:37,712] INFO in api_logger: [1751963617712] 請求開始: POST /script/
[2025-07-08 16:33:37,713] DEBUG in api_logger: [1751963617712] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "332", "Content-Type": "multipart/form-data; boundary=a64b5fc72a5c7f7f37fa3538d74593a8"}, "args": {}, "form": {"description": "測試上傳腳本"}}
[2025-07-08 16:33:37,714] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:33:37,714] DEBUG in script_routes: 準備上傳腳本: test_upload.py
[2025-07-08 16:33:37,719] ERROR in script_routes: 腳本上傳過程發生未處理的錯誤: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts 
WHERE scripts.name = ?
 LIMIT ? OFFSET ?]
[parameters: ('test_upload.py', 1, 0)]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 16:33:37,722] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 8.30ms
[2025-07-08 16:33:37,723] INFO in api_logger: [1751963617712] 請求完成: POST /script/ - 耗時: 8.76ms
[2025-07-08 16:35:12,444] INFO in api_logger: [1751963712443] 請求開始: POST /script/
[2025-07-08 16:35:12,445] DEBUG in api_logger: [1751963712443] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "332", "Content-Type": "multipart/form-data; boundary=0a904c5ad362b21b4bbcd0667787c547"}, "args": {}, "form": {"description": "測試上傳腳本"}}
[2025-07-08 16:35:12,445] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:35:12,446] DEBUG in script_routes: 準備上傳腳本: test_upload.py
[2025-07-08 16:35:12,451] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\test_upload.py
[2025-07-08 16:35:12,469] INFO in script_routes: 腳本上傳成功: test_upload.py
[2025-07-08 16:35:12,475] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 29.36ms
[2025-07-08 16:35:12,475] INFO in api_logger: [1751963712443] 請求完成: POST /script/ - 耗時: 29.74ms
[2025-07-08 16:37:31,325] INFO in api_logger: [1751963851321] 請求開始: GET /script/
[2025-07-08 16:37:31,328] DEBUG in api_logger: [1751963851321] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:37:31,343] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:37:31,349] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 19.54ms
[2025-07-08 16:37:31,349] INFO in api_logger: [1751963851321] 請求完成: GET /script/ - 耗時: 20.13ms
[2025-07-08 16:37:31,645] INFO in api_logger: [1751963851645] 請求開始: GET /script/
[2025-07-08 16:37:31,646] DEBUG in api_logger: [1751963851645] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:37:31,655] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:37:31,659] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.35ms
[2025-07-08 16:37:31,659] INFO in api_logger: [1751963851645] 請求完成: GET /script/ - 耗時: 11.71ms
[2025-07-08 16:39:11,318] INFO in api_logger: [1751963951317] 請求開始: GET /script/
[2025-07-08 16:39:11,319] DEBUG in api_logger: [1751963951317] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:39:11,327] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:39:11,333] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 13.05ms
[2025-07-08 16:39:11,335] INFO in api_logger: [1751963951317] 請求完成: GET /script/ - 耗時: 15.52ms
[2025-07-08 16:39:11,577] INFO in api_logger: [1751963951577] 請求開始: GET /script/
[2025-07-08 16:39:11,577] DEBUG in api_logger: [1751963951577] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:39:11,582] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:39:11,586] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 7.99ms
[2025-07-08 16:39:11,586] INFO in api_logger: [1751963951577] 請求完成: GET /script/ - 耗時: 8.27ms
[2025-07-08 16:39:13,535] INFO in api_logger: [1751963953534] 請求開始: GET /script/
[2025-07-08 16:39:13,536] DEBUG in api_logger: [1751963953534] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:39:13,544] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:39:13,548] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 10.53ms
[2025-07-08 16:39:13,548] INFO in api_logger: [1751963953534] 請求完成: GET /script/ - 耗時: 11.19ms
[2025-07-08 16:39:43,124] INFO in api_logger: [1751963983122] 請求開始: POST /script/validate
[2025-07-08 16:39:43,125] DEBUG in api_logger: [1751963983122] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "5959", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarycZY5w7qi5EN5MlAc", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:39:43,127] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 16:39:43,311] INFO in script_routes: 腳本驗證完成: output_news.py, 有效: True
[2025-07-08 16:39:43,315] INFO in api_logger: [1751963983122] 請求完成: POST /script/validate - 耗時: 187.96ms
[2025-07-08 16:40:02,939] INFO in api_logger: [1751964002938] 請求開始: POST /script/
[2025-07-08 16:40:02,941] DEBUG in api_logger: [1751964002938] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6183", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryFbUfu2gltmoSqTTM", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:40:02,943] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:40:02,944] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 16:40:02,950] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_news.py
[2025-07-08 16:40:02,968] INFO in script_routes: 腳本上傳成功: output_news.py
[2025-07-08 16:40:02,972] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 29.12ms
[2025-07-08 16:40:02,972] INFO in api_logger: [1751964002938] 請求完成: POST /script/ - 耗時: 29.47ms
[2025-07-08 16:40:08,120] INFO in api_logger: [1751964008120] 請求開始: GET /script/
[2025-07-08 16:40:08,121] DEBUG in api_logger: [1751964008120] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:08,130] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:40:08,134] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 12.22ms
[2025-07-08 16:40:08,135] INFO in api_logger: [1751964008120] 請求完成: GET /script/ - 耗時: 12.65ms
[2025-07-08 16:40:12,922] INFO in api_logger: [1751964012922] 請求開始: GET /script/
[2025-07-08 16:40:12,926] DEBUG in api_logger: [1751964012922] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:13,038] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:40:13,197] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 262.82ms
[2025-07-08 16:40:13,200] INFO in api_logger: [1751964012922] 請求完成: GET /script/ - 耗時: 265.51ms
[2025-07-08 16:40:13,325] INFO in api_logger: [1751964013324] 請求開始: GET /script/
[2025-07-08 16:40:13,336] DEBUG in api_logger: [1751964013324] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:13,528] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:40:13,636] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 289.81ms
[2025-07-08 16:40:13,659] INFO in api_logger: [1751964013324] 請求完成: GET /script/ - 耗時: 312.79ms
[2025-07-08 16:40:15,105] INFO in api_logger: [1751964015104] 請求開始: GET /script/
[2025-07-08 16:40:15,135] DEBUG in api_logger: [1751964015104] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:15,537] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:40:15,596] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 397.69ms
[2025-07-08 16:40:15,602] INFO in api_logger: [1751964015104] 請求完成: GET /script/ - 耗時: 403.65ms
[2025-07-08 16:40:38,451] INFO in api_logger: [1751964038451] 請求開始: GET /script/
[2025-07-08 16:40:38,453] DEBUG in api_logger: [1751964038451] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:38,465] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:40:38,470] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 14.80ms
[2025-07-08 16:40:38,470] INFO in api_logger: [1751964038451] 請求完成: GET /script/ - 耗時: 15.19ms
[2025-07-08 16:40:44,875] INFO in api_logger: [1751964044875] 請求開始: GET /script/
[2025-07-08 16:40:44,877] DEBUG in api_logger: [1751964044875] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:44,883] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 16:40:44,886] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.37ms
[2025-07-08 16:40:44,887] INFO in api_logger: [1751964044875] 請求完成: GET /script/ - 耗時: 8.72ms
[2025-07-08 16:40:55,609] INFO in api_logger: [1751964055608] 請求開始: POST /script/validate
[2025-07-08 16:40:55,611] DEBUG in api_logger: [1751964055608] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3522", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryzLpMcYCqbpFHAJtE", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:40:55,612] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 16:40:55,622] INFO in script_routes: 腳本驗證完成: output_booking.py, 有效: True
[2025-07-08 16:40:55,627] INFO in api_logger: [1751964055608] 請求完成: POST /script/validate - 耗時: 14.44ms
[2025-07-08 16:41:49,972] INFO in api_logger: [1751964109971] 請求開始: POST /script/
[2025-07-08 16:41:49,973] DEBUG in api_logger: [1751964109971] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3743", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary9qx0KFYy4cOCDQ3e", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "訂房網內容爬取", "tag": "網頁爬蟲"}}
[2025-07-08 16:41:49,974] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 16:41:49,975] DEBUG in script_routes: 準備上傳腳本: output_booking.py
[2025-07-08 16:41:49,980] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_booking.py
[2025-07-08 16:41:50,012] INFO in script_routes: 腳本上傳成功: output_booking.py
[2025-07-08 16:41:50,015] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 40.79ms
[2025-07-08 16:41:50,015] INFO in api_logger: [1751964109971] 請求完成: POST /script/ - 耗時: 41.09ms
[2025-07-08 16:41:56,068] INFO in api_logger: [1751964116068] 請求開始: GET /script/
[2025-07-08 16:41:56,069] DEBUG in api_logger: [1751964116068] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:41:56,080] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:41:56,086] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.27ms
[2025-07-08 16:41:56,087] INFO in api_logger: [1751964116068] 請求完成: GET /script/ - 耗時: 16.78ms
[2025-07-08 16:41:58,685] INFO in api_logger: [1751964118685] 請求開始: GET /script/
[2025-07-08 16:41:58,686] DEBUG in api_logger: [1751964118685] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:41:58,701] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:41:58,709] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.30ms
[2025-07-08 16:41:58,709] INFO in api_logger: [1751964118685] 請求完成: GET /script/ - 耗時: 22.71ms
[2025-07-08 16:41:59,051] INFO in api_logger: [1751964119050] 請求開始: GET /script/
[2025-07-08 16:41:59,051] DEBUG in api_logger: [1751964119050] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:41:59,062] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:41:59,069] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.68ms
[2025-07-08 16:41:59,070] INFO in api_logger: [1751964119050] 請求完成: GET /script/ - 耗時: 17.06ms
[2025-07-08 16:42:00,418] INFO in api_logger: [1751964120418] 請求開始: GET /script/
[2025-07-08 16:42:00,418] DEBUG in api_logger: [1751964120418] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:42:00,425] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:42:00,430] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 10.93ms
[2025-07-08 16:42:00,430] INFO in api_logger: [1751964120418] 請求完成: GET /script/ - 耗時: 11.43ms
[2025-07-08 16:45:22,835] INFO in api_logger: [1751964322835] 請求開始: GET /script/
[2025-07-08 16:45:22,836] DEBUG in api_logger: [1751964322835] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:45:22,849] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:45:22,855] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.96ms
[2025-07-08 16:45:22,856] INFO in api_logger: [1751964322835] 請求完成: GET /script/ - 耗時: 17.42ms
[2025-07-08 16:45:23,153] INFO in api_logger: [1751964323153] 請求開始: GET /script/
[2025-07-08 16:45:23,153] DEBUG in api_logger: [1751964323153] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:45:23,159] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:45:23,162] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.23ms
[2025-07-08 16:45:23,162] INFO in api_logger: [1751964323153] 請求完成: GET /script/ - 耗時: 8.56ms
[2025-07-08 16:45:41,485] INFO in api_logger: [1751964341485] 請求開始: GET /script/
[2025-07-08 16:45:41,486] DEBUG in api_logger: [1751964341485] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 16:45:41,492] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 16:45:41,496] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 9.27ms
[2025-07-08 16:45:41,496] INFO in api_logger: [1751964341485] 請求完成: GET /script/ - 耗時: 9.64ms
[2025-07-08 17:02:49,402] INFO in api_logger: [1751965369401] 請求開始: GET /script/
[2025-07-08 17:02:49,403] DEBUG in api_logger: [1751965369401] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:02:49,409] INFO in script_routes: 發現未記錄的腳本文件: output_booking.py，同步到資料庫
[2025-07-08 17:02:49,417] INFO in script_routes: 發現未記錄的腳本文件: test_upload.py，同步到資料庫
[2025-07-08 17:02:49,432] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 17:02:49,432] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.87ms
[2025-07-08 17:02:49,433] INFO in api_logger: [1751965369401] 請求完成: GET /script/ - 耗時: 29.28ms
[2025-07-08 17:02:49,610] INFO in api_logger: [1751965369609] 請求開始: GET /script/
[2025-07-08 17:02:49,611] DEBUG in api_logger: [1751965369609] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:02:49,630] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:02:49,644] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 31.97ms
[2025-07-08 17:02:49,645] INFO in api_logger: [1751965369609] 請求完成: GET /script/ - 耗時: 32.59ms
[2025-07-08 17:02:50,705] INFO in api_logger: [1751965370705] 請求開始: GET /script/
[2025-07-08 17:02:50,706] DEBUG in api_logger: [1751965370705] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:02:50,716] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:02:50,724] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.51ms
[2025-07-08 17:02:50,725] INFO in api_logger: [1751965370705] 請求完成: GET /script/ - 耗時: 17.43ms
[2025-07-08 17:02:57,453] INFO in api_logger: [1751965377453] 請求開始: GET /script/
[2025-07-08 17:02:57,454] DEBUG in api_logger: [1751965377453] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:02:57,466] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 17:02:57,472] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.88ms
[2025-07-08 17:02:57,473] INFO in api_logger: [1751965377453] 請求完成: GET /script/ - 耗時: 16.54ms
[2025-07-08 17:03:09,871] INFO in api_logger: [1751965389870] 請求開始: POST /script/validate
[2025-07-08 17:03:09,872] DEBUG in api_logger: [1751965389870] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "5959", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarysDUCAAPGupLSYO0P", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:03:09,874] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:03:10,011] INFO in script_routes: 腳本驗證完成: output_news.py, 有效: True
[2025-07-08 17:03:10,013] INFO in api_logger: [1751965389870] 請求完成: POST /script/validate - 耗時: 139.80ms
[2025-07-08 17:03:28,221] INFO in api_logger: [1751965408220] 請求開始: POST /script/
[2025-07-08 17:03:28,222] DEBUG in api_logger: [1751965408220] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "6183", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryo7tHxxzXeAXgQV0F", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網最新消息爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:03:28,223] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:03:28,224] DEBUG in script_routes: 準備上傳腳本: output_news.py
[2025-07-08 17:03:28,228] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_news.py
[2025-07-08 17:03:28,255] INFO in script_routes: 腳本上傳成功: output_news.py
[2025-07-08 17:03:28,261] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.93ms
[2025-07-08 17:03:28,262] INFO in api_logger: [1751965408220] 請求完成: POST /script/ - 耗時: 38.45ms
[2025-07-08 17:03:37,949] INFO in api_logger: [1751965417949] 請求開始: GET /script/
[2025-07-08 17:03:37,950] DEBUG in api_logger: [1751965417949] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:03:37,957] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:03:37,963] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 12.42ms
[2025-07-08 17:03:37,964] INFO in api_logger: [1751965417949] 請求完成: GET /script/ - 耗時: 12.76ms
[2025-07-08 17:05:58,023] INFO in api_logger: [1751965558023] 請求開始: GET /script/
[2025-07-08 17:05:58,024] DEBUG in api_logger: [1751965558023] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:05:58,034] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:05:58,045] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.39ms
[2025-07-08 17:05:58,046] INFO in api_logger: [1751965558023] 請求完成: GET /script/ - 耗時: 21.03ms
[2025-07-08 17:06:31,122] INFO in api_logger: [1751965591122] 請求開始: GET /script/
[2025-07-08 17:06:31,123] DEBUG in api_logger: [1751965591122] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:06:31,129] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:06:31,133] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 9.00ms
[2025-07-08 17:06:31,133] INFO in api_logger: [1751965591122] 請求完成: GET /script/ - 耗時: 9.33ms
[2025-07-08 17:06:42,251] INFO in api_logger: [1751965602251] 請求開始: GET /script/
[2025-07-08 17:06:42,252] DEBUG in api_logger: [1751965602251] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (iPad; CPU OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:06:42,261] INFO in script_routes: 成功獲取腳本列表，共 2 個腳本
[2025-07-08 17:06:42,265] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 11.99ms
[2025-07-08 17:06:42,265] INFO in api_logger: [1751965602251] 請求完成: GET /script/ - 耗時: 12.48ms
[2025-07-08 17:08:16,060] INFO in api_logger: [1751965696060] 請求開始: POST /script/
[2025-07-08 17:08:16,061] DEBUG in api_logger: [1751965696060] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "500", "Content-Type": "multipart/form-data; boundary=e742702eae2809993fcb53fb54057280"}, "args": {}, "form": {"description": "網頁爬蟲測試腳本", "tags": "網頁爬蟲"}}
[2025-07-08 17:08:16,061] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:08:16,061] DEBUG in script_routes: 準備上傳腳本: web_crawler_test.py
[2025-07-08 17:08:16,066] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\web_crawler_test.py
[2025-07-08 17:08:16,087] INFO in script_routes: 腳本上傳成功: web_crawler_test.py
[2025-07-08 17:08:16,092] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 30.57ms
[2025-07-08 17:08:16,092] INFO in api_logger: [1751965696060] 請求完成: POST /script/ - 耗時: 30.88ms
[2025-07-08 17:08:58,230] INFO in api_logger: [1751965738228] 請求開始: GET /script/
[2025-07-08 17:08:58,230] DEBUG in api_logger: [1751965738228] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-TW) WindowsPowerShell/5.1.22621.4391", "Host": "localhost:5000", "Connection": "Keep-Alive"}, "args": {}}
[2025-07-08 17:08:58,240] INFO in script_routes: 成功獲取腳本列表，共 3 個腳本
[2025-07-08 17:08:58,245] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 14.68ms
[2025-07-08 17:08:58,246] INFO in api_logger: [1751965738228] 請求完成: GET /script/ - 耗時: 15.05ms
[2025-07-08 17:16:11,522] INFO in api_logger: [1751966171516] 請求開始: GET /script/
[2025-07-08 17:16:11,523] DEBUG in api_logger: [1751966171516] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:16:11,539] INFO in script_routes: 成功獲取腳本列表，共 3 個腳本
[2025-07-08 17:16:11,546] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.52ms
[2025-07-08 17:16:11,547] INFO in api_logger: [1751966171516] 請求完成: GET /script/ - 耗時: 22.79ms
[2025-07-08 17:16:36,534] INFO in api_logger: [1751966196533] 請求開始: POST /script/validate
[2025-07-08 17:16:36,534] DEBUG in api_logger: [1751966196533] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3522", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryylA5lpgrqzERYLl6", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:16:36,535] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:16:36,699] INFO in script_routes: 腳本驗證完成: output_booking.py, 有效: True
[2025-07-08 17:16:36,702] INFO in api_logger: [1751966196533] 請求完成: POST /script/validate - 耗時: 167.35ms
[2025-07-08 17:17:03,201] INFO in api_logger: [1751966223199] 請求開始: POST /script/
[2025-07-08 17:17:03,202] DEBUG in api_logger: [1751966223199] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "3743", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarytB2fuJIHkAbu2IsJ", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "訂房網內容爬取", "tag": "檢核通知"}}
[2025-07-08 17:17:03,203] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:17:03,204] DEBUG in script_routes: 準備上傳腳本: output_booking.py
[2025-07-08 17:17:03,209] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_booking.py
[2025-07-08 17:17:03,237] INFO in script_routes: 腳本上傳成功: output_booking.py
[2025-07-08 17:17:03,241] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 38.07ms
[2025-07-08 17:17:03,242] INFO in api_logger: [1751966223199] 請求完成: POST /script/ - 耗時: 38.44ms
[2025-07-08 17:17:15,677] INFO in api_logger: [1751966235677] 請求開始: GET /script/
[2025-07-08 17:17:15,678] DEBUG in api_logger: [1751966235677] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:17:15,692] INFO in script_routes: 成功獲取腳本列表，共 4 個腳本
[2025-07-08 17:17:15,701] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 21.37ms
[2025-07-08 17:17:15,701] INFO in api_logger: [1751966235677] 請求完成: GET /script/ - 耗時: 21.69ms
[2025-07-08 17:18:15,019] INFO in api_logger: [1751966295018] 請求開始: POST /script/validate
[2025-07-08 17:18:15,019] DEBUG in api_logger: [1751966295018] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "443", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryMZxVA3GIB3iwmaGB", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:18:15,021] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:18:15,028] INFO in script_routes: 腳本驗證完成: test_script.py, 有效: True
[2025-07-08 17:18:15,032] INFO in api_logger: [1751966295018] 請求完成: POST /script/validate - 耗時: 11.13ms
[2025-07-08 17:18:20,754] INFO in api_logger: [1751966300754] 請求開始: GET /script/
[2025-07-08 17:18:20,755] DEBUG in api_logger: [1751966300754] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:18:20,764] INFO in script_routes: 成功獲取腳本列表，共 4 個腳本
[2025-07-08 17:18:20,770] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 14.59ms
[2025-07-08 17:18:20,771] INFO in api_logger: [1751966300754] 請求完成: GET /script/ - 耗時: 14.87ms
[2025-07-08 17:19:22,510] INFO in api_logger: [1751966362510] 請求開始: GET /script/
[2025-07-08 17:19:22,512] DEBUG in api_logger: [1751966362510] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:19:22,540] INFO in script_routes: 成功獲取腳本列表，共 4 個腳本
[2025-07-08 17:19:22,555] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 41.07ms
[2025-07-08 17:19:22,556] INFO in api_logger: [1751966362510] 請求完成: GET /script/ - 耗時: 42.12ms
[2025-07-08 17:22:29,828] INFO in api_logger: [1751966549824] 請求開始: GET /script/
[2025-07-08 17:22:29,830] DEBUG in api_logger: [1751966549824] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:22:29,842] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:22:29,912] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:22:29,941] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 108.04ms
[2025-07-08 17:22:29,941] INFO in api_logger: [1751966549824] 請求完成: GET /script/ - 耗時: 108.51ms
[2025-07-08 17:22:30,144] INFO in api_logger: [1751966550143] 請求開始: GET /script/
[2025-07-08 17:22:30,144] DEBUG in api_logger: [1751966550143] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:22:30,149] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:22:30,155] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:22:30,164] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 18.90ms
[2025-07-08 17:22:30,164] INFO in api_logger: [1751966550143] 請求完成: GET /script/ - 耗時: 19.23ms
[2025-07-08 17:22:34,797] INFO in api_logger: [1751966554797] 請求開始: GET /script/
[2025-07-08 17:22:34,798] DEBUG in api_logger: [1751966554797] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:22:34,809] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:22:34,815] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:22:34,830] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 31.65ms
[2025-07-08 17:22:34,831] INFO in api_logger: [1751966554797] 請求完成: GET /script/ - 耗時: 32.27ms
[2025-07-08 17:22:35,174] INFO in api_logger: [1751966555174] 請求開始: GET /script/
[2025-07-08 17:22:35,175] DEBUG in api_logger: [1751966555174] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:22:35,187] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:22:35,195] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:22:35,206] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 30.58ms
[2025-07-08 17:22:35,207] INFO in api_logger: [1751966555174] 請求完成: GET /script/ - 耗時: 31.06ms
[2025-07-08 17:23:16,395] INFO in api_logger: [1751966596395] 請求開始: GET /script/
[2025-07-08 17:23:16,395] DEBUG in api_logger: [1751966596395] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive"}, "args": {}}
[2025-07-08 17:23:16,466] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:23:16,477] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:23:16,497] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:23:16,522] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 60.72ms
[2025-07-08 17:23:16,530] INFO in api_logger: [1751966596395] 請求完成: GET /script/ - 耗時: 68.45ms
[2025-07-08 17:23:16,546] INFO in api_logger: [1751966596545] 請求開始: POST /script/
[2025-07-08 17:23:16,557] DEBUG in api_logger: [1751966596545] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive", "Content-Length": "516", "Content-Type": "multipart/form-data; boundary=0373d71f3f70430d2a042e6c6153b7b0"}, "args": {}, "form": {"description": "檢核通知測試腳本", "tags": "檢核通知"}}
[2025-07-08 17:23:16,566] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:23:16,577] DEBUG in script_routes: 準備上傳腳本: notification_test.py
[2025-07-08 17:23:16,592] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\notification_test.py
[2025-07-08 17:23:16,632] INFO in script_routes: 腳本上傳成功: notification_test.py
[2025-07-08 17:23:16,648] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 82.16ms
[2025-07-08 17:23:16,660] INFO in api_logger: [1751966596545] 請求完成: POST /script/ - 耗時: 94.74ms
[2025-07-08 17:23:16,679] INFO in api_logger: [1751966596679] 請求開始: GET /script/
[2025-07-08 17:23:16,695] DEBUG in api_logger: [1751966596679] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate", "Accept": "*/*", "Connection": "keep-alive"}, "args": {}}
[2025-07-08 17:23:16,712] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:23:16,723] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:23:16,747] INFO in script_routes: 成功獲取腳本列表，共 7 個腳本
[2025-07-08 17:23:16,772] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 64.49ms
[2025-07-08 17:23:16,781] INFO in api_logger: [1751966596679] 請求完成: GET /script/ - 耗時: 73.78ms
[2025-07-08 17:25:20,493] INFO in api_logger: [1751966720493] 請求開始: GET /script/
[2025-07-08 17:25:20,536] DEBUG in api_logger: [1751966720493] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:25:20,594] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:25:20,607] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:25:20,618] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:25:20,642] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:25:20,675] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 92.47ms
[2025-07-08 17:25:20,688] INFO in api_logger: [1751966720493] 請求完成: GET /script/ - 耗時: 105.29ms
[2025-07-08 17:25:21,015] INFO in api_logger: [1751966721015] 請求開始: GET /script/
[2025-07-08 17:25:21,026] DEBUG in api_logger: [1751966721015] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:25:21,045] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:25:21,058] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:25:21,069] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:25:21,092] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:25:21,125] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 86.11ms
[2025-07-08 17:25:21,140] INFO in api_logger: [1751966721015] 請求完成: GET /script/ - 耗時: 100.64ms
[2025-07-08 17:27:22,651] INFO in api_logger: [1751966842650] 請求開始: GET /script/
[2025-07-08 17:27:22,652] DEBUG in api_logger: [1751966842650] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:27:22,658] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:27:22,658] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:27:22,659] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:27:22,668] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:27:22,681] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.02ms
[2025-07-08 17:27:22,681] INFO in api_logger: [1751966842650] 請求完成: GET /script/ - 耗時: 28.36ms
[2025-07-08 17:27:50,356] INFO in api_logger: [1751966870354] 請求開始: POST /script/validate
[2025-07-08 17:27:50,357] DEBUG in api_logger: [1751966870354] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryLcY9lSmD2QSnM7n2", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:27:50,358] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:27:50,551] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:27:50,553] INFO in api_logger: [1751966870354] 請求完成: POST /script/validate - 耗時: 195.31ms
[2025-07-08 17:28:15,184] INFO in api_logger: [1751966895183] 請求開始: POST /script/
[2025-07-08 17:28:15,185] DEBUG in api_logger: [1751966895183] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary8qDAcFudVHMVG5E3", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "備份還原"}}
[2025-07-08 17:28:15,186] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:28:15,187] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:28:15,192] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:28:15,220] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:28:15,224] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.99ms
[2025-07-08 17:28:15,225] INFO in api_logger: [1751966895183] 請求完成: POST /script/ - 耗時: 38.29ms
[2025-07-08 17:28:19,418] INFO in api_logger: [1751966899418] 請求開始: GET /script/
[2025-07-08 17:28:19,419] DEBUG in api_logger: [1751966899418] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:28:19,424] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:28:19,424] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:28:19,425] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:28:19,437] INFO in script_routes: 成功獲取腳本列表，共 9 個腳本
[2025-07-08 17:28:19,447] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.50ms
[2025-07-08 17:28:19,447] INFO in api_logger: [1751966899418] 請求完成: GET /script/ - 耗時: 27.86ms
[2025-07-08 17:28:52,417] INFO in api_logger: [1751966932417] 請求開始: GET /script/
[2025-07-08 17:28:52,417] DEBUG in api_logger: [1751966932417] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:28:52,422] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:28:52,423] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:28:52,423] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:28:52,435] INFO in script_routes: 成功獲取腳本列表，共 9 個腳本
[2025-07-08 17:28:52,447] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 29.13ms
[2025-07-08 17:28:52,448] INFO in api_logger: [1751966932417] 請求完成: GET /script/ - 耗時: 29.46ms
[2025-07-08 17:29:02,780] INFO in api_logger: [1751966942779] 請求開始: GET /script/
[2025-07-08 17:29:02,780] DEBUG in api_logger: [1751966942779] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:02,785] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:02,786] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:02,786] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:02,795] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:29:02,807] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 26.33ms
[2025-07-08 17:29:02,807] INFO in api_logger: [1751966942779] 請求完成: GET /script/ - 耗時: 26.69ms
[2025-07-08 17:29:07,351] INFO in api_logger: [1751966947351] 請求開始: GET /script/
[2025-07-08 17:29:07,352] DEBUG in api_logger: [1751966947351] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:07,356] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:07,357] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:07,358] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:07,368] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:29:07,380] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.31ms
[2025-07-08 17:29:07,380] INFO in api_logger: [1751966947351] 請求完成: GET /script/ - 耗時: 27.77ms
[2025-07-08 17:29:25,416] INFO in api_logger: [1751966965416] 請求開始: GET /script/
[2025-07-08 17:29:25,417] DEBUG in api_logger: [1751966965416] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:25,423] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:25,423] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:25,424] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:25,437] INFO in script_routes: 成功獲取腳本列表，共 7 個腳本
[2025-07-08 17:29:25,454] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 36.29ms
[2025-07-08 17:29:25,455] INFO in api_logger: [1751966965416] 請求完成: GET /script/ - 耗時: 36.92ms
[2025-07-08 17:29:30,514] INFO in api_logger: [1751966970513] 請求開始: GET /script/
[2025-07-08 17:29:30,515] DEBUG in api_logger: [1751966970513] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:30,523] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:30,524] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:30,525] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:30,531] INFO in script_routes: 成功獲取腳本列表，共 7 個腳本
[2025-07-08 17:29:30,542] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.21ms
[2025-07-08 17:29:30,542] INFO in api_logger: [1751966970513] 請求完成: GET /script/ - 耗時: 25.51ms
[2025-07-08 17:29:37,899] INFO in api_logger: [1751966977898] 請求開始: GET /script/
[2025-07-08 17:29:37,899] DEBUG in api_logger: [1751966977898] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:37,905] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:37,906] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:37,907] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:37,914] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:29:37,927] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 26.33ms
[2025-07-08 17:29:37,927] INFO in api_logger: [1751966977898] 請求完成: GET /script/ - 耗時: 26.75ms
[2025-07-08 17:29:45,453] INFO in api_logger: [1751966985453] 請求開始: GET /script/
[2025-07-08 17:29:45,453] DEBUG in api_logger: [1751966985453] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:45,457] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:45,458] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:45,458] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:45,467] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:29:45,475] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.49ms
[2025-07-08 17:29:45,475] INFO in api_logger: [1751966985453] 請求完成: GET /script/ - 耗時: 20.95ms
[2025-07-08 17:29:47,866] INFO in api_logger: [1751966987866] 請求開始: GET /script/
[2025-07-08 17:29:47,867] DEBUG in api_logger: [1751966987866] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:47,877] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:47,878] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:47,879] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:47,885] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:29:47,893] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.27ms
[2025-07-08 17:29:47,893] INFO in api_logger: [1751966987866] 請求完成: GET /script/ - 耗時: 23.61ms
[2025-07-08 17:29:56,285] INFO in api_logger: [1751966996284] 請求開始: POST /script/validate
[2025-07-08 17:29:56,285] DEBUG in api_logger: [1751966996284] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryVwkCa1kJG0QIR41j", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:56,287] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:29:56,318] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:29:56,325] INFO in api_logger: [1751966996284] 請求完成: POST /script/validate - 耗時: 37.17ms
[2025-07-08 17:30:06,052] INFO in api_logger: [1751967006052] 請求開始: POST /script/
[2025-07-08 17:30:06,053] DEBUG in api_logger: [1751967006052] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryTPk7n5a2gwsHxavY", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:30:06,054] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:30:06,055] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:30:06,061] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:30:06,094] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:30:06,101] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 46.65ms
[2025-07-08 17:30:06,102] INFO in api_logger: [1751967006052] 請求完成: POST /script/ - 耗時: 47.32ms
[2025-07-08 17:30:11,866] INFO in api_logger: [1751967011862] 請求開始: GET /script/
[2025-07-08 17:30:11,867] DEBUG in api_logger: [1751967011862] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:30:11,873] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:30:11,874] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:30:11,874] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:30:11,882] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:30:11,893] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.41ms
[2025-07-08 17:30:11,894] INFO in api_logger: [1751967011862] 請求完成: GET /script/ - 耗時: 25.77ms
[2025-07-08 17:32:04,336] INFO in api_logger: [1751967124333] 請求開始: GET /script/
[2025-07-08 17:32:04,337] DEBUG in api_logger: [1751967124333] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:32:04,344] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:32:04,344] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:32:04,345] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:32:04,354] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:32:04,365] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.14ms
[2025-07-08 17:32:04,366] INFO in api_logger: [1751967124333] 請求完成: GET /script/ - 耗時: 27.53ms
[2025-07-08 17:32:19,517] INFO in api_logger: [1751967139514] 請求開始: GET /script/
[2025-07-08 17:32:19,518] DEBUG in api_logger: [1751967139514] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:32:19,526] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:32:19,527] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:32:19,528] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:32:19,536] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:32:19,547] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.28ms
[2025-07-08 17:32:19,547] INFO in api_logger: [1751967139514] 請求完成: GET /script/ - 耗時: 27.68ms
[2025-07-08 17:33:20,798] INFO in api_logger: [1751967200798] 請求開始: GET /script/
[2025-07-08 17:33:20,802] DEBUG in api_logger: [1751967200798] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:20,813] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:20,814] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:20,815] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:20,821] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:33:20,840] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.03ms
[2025-07-08 17:33:20,840] INFO in api_logger: [1751967200798] 請求完成: GET /script/ - 耗時: 35.47ms
[2025-07-08 17:33:21,114] INFO in api_logger: [1751967201114] 請求開始: GET /script/
[2025-07-08 17:33:21,115] DEBUG in api_logger: [1751967201114] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:21,120] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:21,120] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:21,121] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:21,125] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:33:21,134] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 17.78ms
[2025-07-08 17:33:21,134] INFO in api_logger: [1751967201114] 請求完成: GET /script/ - 耗時: 18.10ms
[2025-07-08 17:33:22,700] INFO in api_logger: [1751967202700] 請求開始: GET /script/
[2025-07-08 17:33:22,701] DEBUG in api_logger: [1751967202700] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:22,704] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:22,705] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:22,705] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:22,714] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:33:22,724] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.05ms
[2025-07-08 17:33:22,724] INFO in api_logger: [1751967202700] 請求完成: GET /script/ - 耗時: 23.35ms
[2025-07-08 17:33:27,929] INFO in api_logger: [1751967207929] 請求開始: GET /script/
[2025-07-08 17:33:27,931] DEBUG in api_logger: [1751967207929] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:27,939] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:27,940] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:27,942] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:27,948] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:33:27,958] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.94ms
[2025-07-08 17:33:27,959] INFO in api_logger: [1751967207929] 請求完成: GET /script/ - 耗時: 26.42ms
[2025-07-08 17:33:49,764] INFO in api_logger: [1751967229763] 請求開始: POST /script/validate
[2025-07-08 17:33:49,764] DEBUG in api_logger: [1751967229763] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary3sPCws3SRUfyU2MC", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:49,765] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:33:49,877] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:33:49,880] INFO in api_logger: [1751967229763] 請求完成: POST /script/validate - 耗時: 115.32ms
[2025-07-08 17:34:26,171] INFO in api_logger: [1751967266170] 請求開始: POST /script/
[2025-07-08 17:34:26,172] DEBUG in api_logger: [1751967266170] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryoTDraM0oAHMcyZCu", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:34:26,173] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:34:26,174] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:34:26,178] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:34:26,186] INFO in script_routes: 腳本上傳調試信息:
[2025-07-08 17:34:26,187] INFO in script_routes:   文件名: output_www.py
[2025-07-08 17:34:26,187] INFO in script_routes:   描述: 官網資訊爬取
[2025-07-08 17:34:26,188] INFO in script_routes:   標籤: 未分類
[2025-07-08 17:34:26,188] INFO in script_routes:   表單數據: {'description': '官網資訊爬取', 'tag': '網頁爬蟲'}
[2025-07-08 17:34:26,208] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:34:26,214] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 40.85ms
[2025-07-08 17:34:26,215] INFO in api_logger: [1751967266170] 請求完成: POST /script/ - 耗時: 41.95ms
[2025-07-08 17:34:42,617] INFO in api_logger: [1751967282614] 請求開始: GET /script/
[2025-07-08 17:34:42,618] DEBUG in api_logger: [1751967282614] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:34:42,624] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:34:42,625] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:34:42,626] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:34:42,634] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:34:42,656] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 36.75ms
[2025-07-08 17:34:42,657] INFO in api_logger: [1751967282614] 請求完成: GET /script/ - 耗時: 37.35ms
[2025-07-08 17:38:02,698] INFO in api_logger: [1751967482696] 請求開始: GET /script/
[2025-07-08 17:38:02,699] DEBUG in api_logger: [1751967482696] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:02,704] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:02,704] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:02,705] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:02,715] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:02,727] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.82ms
[2025-07-08 17:38:02,728] INFO in api_logger: [1751967482696] 請求完成: GET /script/ - 耗時: 28.32ms
[2025-07-08 17:38:02,930] INFO in api_logger: [1751967482930] 請求開始: GET /script/
[2025-07-08 17:38:02,931] DEBUG in api_logger: [1751967482930] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:02,944] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:02,948] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:02,950] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:02,956] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:02,970] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.97ms
[2025-07-08 17:38:02,970] INFO in api_logger: [1751967482930] 請求完成: GET /script/ - 耗時: 36.48ms
[2025-07-08 17:38:05,028] INFO in api_logger: [1751967485027] 請求開始: GET /script/
[2025-07-08 17:38:05,029] DEBUG in api_logger: [1751967485027] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:05,037] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:05,037] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:05,038] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:05,045] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:05,055] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.80ms
[2025-07-08 17:38:05,055] INFO in api_logger: [1751967485027] 請求完成: GET /script/ - 耗時: 24.13ms
[2025-07-08 17:38:09,075] INFO in api_logger: [1751967489075] 請求開始: GET /script/
[2025-07-08 17:38:09,076] DEBUG in api_logger: [1751967489075] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:09,080] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:09,081] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:09,081] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:09,085] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:38:09,092] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.70ms
[2025-07-08 17:38:09,093] INFO in api_logger: [1751967489075] 請求完成: GET /script/ - 耗時: 16.07ms
[2025-07-08 17:38:15,879] INFO in api_logger: [1751967495878] 請求開始: POST /script/validate
[2025-07-08 17:38:15,880] DEBUG in api_logger: [1751967495878] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryHg3UCjCJQ4isrzxt", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:15,881] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:38:16,021] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:38:16,024] INFO in api_logger: [1751967495878] 請求完成: POST /script/validate - 耗時: 143.41ms
[2025-07-08 17:38:26,197] INFO in api_logger: [1751967506195] 請求開始: POST /script/
[2025-07-08 17:38:26,198] DEBUG in api_logger: [1751967506195] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarypOeYwEgYY6OQrAuc", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:38:26,200] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:38:26,200] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:38:26,207] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:38:26,216] INFO in script_routes: 腳本上傳調試信息:
[2025-07-08 17:38:26,217] INFO in script_routes:   文件名: output_www.py
[2025-07-08 17:38:26,217] INFO in script_routes:   描述: 官網資訊爬取
[2025-07-08 17:38:26,217] INFO in script_routes:   標籤: 未分類
[2025-07-08 17:38:26,218] INFO in script_routes:   表單數據: {'description': '官網資訊爬取', 'tag': '網頁爬蟲'}
[2025-07-08 17:38:26,233] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:38:26,237] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.38ms
[2025-07-08 17:38:26,237] INFO in api_logger: [1751967506195] 請求完成: POST /script/ - 耗時: 37.71ms
[2025-07-08 17:38:40,064] INFO in api_logger: [1751967520061] 請求開始: GET /script/
[2025-07-08 17:38:40,066] DEBUG in api_logger: [1751967520061] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:40,076] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:40,077] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:40,079] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:40,086] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:40,103] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.37ms
[2025-07-08 17:38:40,104] INFO in api_logger: [1751967520061] 請求完成: GET /script/ - 耗時: 36.79ms
[2025-07-08 17:38:40,378] INFO in api_logger: [1751967520378] 請求開始: GET /script/
[2025-07-08 17:38:40,379] DEBUG in api_logger: [1751967520378] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:40,383] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:40,384] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:40,384] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:40,390] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:40,400] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.22ms
[2025-07-08 17:38:40,400] INFO in api_logger: [1751967520378] 請求完成: GET /script/ - 耗時: 20.55ms
[2025-07-08 17:38:41,629] INFO in api_logger: [1751967521629] 請求開始: GET /script/
[2025-07-08 17:38:41,630] DEBUG in api_logger: [1751967521629] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:41,636] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:41,637] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:41,638] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:41,646] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:41,655] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.02ms
[2025-07-08 17:38:41,655] INFO in api_logger: [1751967521629] 請求完成: GET /script/ - 耗時: 23.37ms
[2025-07-08 17:39:30,262] INFO in api_logger: [1751967570261] 請求開始: GET /script/
[2025-07-08 17:39:30,263] DEBUG in api_logger: [1751967570261] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:39:30,270] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:39:30,271] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:39:30,272] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:39:30,278] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:39:30,286] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 21.14ms
[2025-07-08 17:39:30,286] INFO in api_logger: [1751967570261] 請求完成: GET /script/ - 耗時: 21.46ms
[2025-07-08 17:39:35,545] INFO in api_logger: [1751967575545] 請求開始: GET /script/
[2025-07-08 17:39:35,548] DEBUG in api_logger: [1751967575545] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:39:35,555] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:39:35,556] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:39:35,557] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:39:35,563] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:39:35,574] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.18ms
[2025-07-08 17:39:35,574] INFO in api_logger: [1751967575545] 請求完成: GET /script/ - 耗時: 25.66ms
[2025-07-08 17:39:35,897] INFO in api_logger: [1751967575896] 請求開始: GET /script/
[2025-07-08 17:39:35,897] DEBUG in api_logger: [1751967575896] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:39:35,905] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:39:35,906] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:39:35,907] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:39:35,915] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:39:35,923] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.96ms
[2025-07-08 17:39:35,923] INFO in api_logger: [1751967575896] 請求完成: GET /script/ - 耗時: 24.28ms
[2025-07-08 17:39:37,079] INFO in api_logger: [1751967577078] 請求開始: GET /script/
[2025-07-08 17:39:37,080] DEBUG in api_logger: [1751967577078] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:39:37,085] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:39:37,086] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:39:37,087] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:39:37,090] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:39:37,103] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.43ms
[2025-07-08 17:39:37,103] INFO in api_logger: [1751967577078] 請求完成: GET /script/ - 耗時: 22.95ms
[2025-07-08 17:40:01,531] INFO in api_logger: [1751967601531] 請求開始: GET /script/
[2025-07-08 17:40:01,532] DEBUG in api_logger: [1751967601531] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:40:01,547] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:40:01,548] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:40:01,550] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:40:01,556] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:40:01,571] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 37.89ms
[2025-07-08 17:40:01,571] INFO in api_logger: [1751967601531] 請求完成: GET /script/ - 耗時: 38.29ms
[2025-07-08 17:42:51,596] INFO in api_logger: [1751967771591] 請求開始: GET /script/
[2025-07-08 17:42:51,597] DEBUG in api_logger: [1751967771591] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:42:51,606] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:42:51,606] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:42:51,609] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:42:51,615] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:42:51,628] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.67ms
[2025-07-08 17:42:51,628] INFO in api_logger: [1751967771591] 請求完成: GET /script/ - 耗時: 29.25ms
[2025-07-08 17:42:51,912] INFO in api_logger: [1751967771911] 請求開始: GET /script/
[2025-07-08 17:42:51,913] DEBUG in api_logger: [1751967771911] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:42:51,936] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:42:51,947] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:42:51,983] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:42:52,013] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:42:52,048] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 131.99ms
[2025-07-08 17:42:52,049] INFO in api_logger: [1751967771911] 請求完成: GET /script/ - 耗時: 132.74ms
[2025-07-08 17:43:16,638] INFO in api_logger: [1751967796638] 請求開始: GET /script/
[2025-07-08 17:43:16,640] DEBUG in api_logger: [1751967796638] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:16,658] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:43:16,662] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:43:16,663] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:43:16,668] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:43:16,686] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 44.70ms
[2025-07-08 17:43:16,687] INFO in api_logger: [1751967796638] 請求完成: GET /script/ - 耗時: 45.17ms
[2025-07-08 17:43:16,975] INFO in api_logger: [1751967796975] 請求開始: GET /script/
[2025-07-08 17:43:16,975] DEBUG in api_logger: [1751967796975] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:16,979] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:43:16,980] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:43:16,980] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:43:16,985] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:43:16,995] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 18.71ms
[2025-07-08 17:43:16,995] INFO in api_logger: [1751967796975] 請求完成: GET /script/ - 耗時: 19.03ms
[2025-07-08 17:43:18,291] INFO in api_logger: [1751967798290] 請求開始: GET /script/
[2025-07-08 17:43:18,291] DEBUG in api_logger: [1751967798290] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:18,296] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:43:18,297] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:43:18,298] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:43:18,304] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:43:18,317] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 24.72ms
[2025-07-08 17:43:18,318] INFO in api_logger: [1751967798290] 請求完成: GET /script/ - 耗時: 25.31ms
[2025-07-08 17:43:31,409] INFO in api_logger: [1751967811408] 請求開始: POST /script/validate
[2025-07-08 17:43:31,409] DEBUG in api_logger: [1751967811408] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7073", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary1GFDZw0fo6W8ansH", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:31,410] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:43:31,519] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:43:31,522] INFO in api_logger: [1751967811408] 請求完成: POST /script/validate - 耗時: 112.45ms
[2025-07-08 17:43:40,230] INFO in api_logger: [1751967820230] 請求開始: POST /script/
[2025-07-08 17:43:40,231] DEBUG in api_logger: [1751967820230] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7291", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary6IjzaAuvgHADp73M", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:43:40,232] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:43:40,232] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:43:40,237] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:43:40,247] INFO in script_routes: 腳本上傳調試信息:
[2025-07-08 17:43:40,247] INFO in script_routes:   文件名: output_www.py
[2025-07-08 17:43:40,247] INFO in script_routes:   描述: 官網資訊爬取
[2025-07-08 17:43:40,248] INFO in script_routes:   標籤: 未分類
[2025-07-08 17:43:40,248] INFO in script_routes:   表單數據: {'description': '官網資訊爬取', 'tag': '網頁爬蟲'}
[2025-07-08 17:43:40,264] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:43:40,269] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.67ms
[2025-07-08 17:43:40,270] INFO in api_logger: [1751967820230] 請求完成: POST /script/ - 耗時: 38.12ms
[2025-07-08 17:43:50,106] INFO in api_logger: [1751967830105] 請求開始: GET /script/
[2025-07-08 17:43:50,107] DEBUG in api_logger: [1751967830105] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:50,113] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:43:50,114] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:43:50,115] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:43:50,121] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:43:50,130] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.03ms
[2025-07-08 17:43:50,131] INFO in api_logger: [1751967830105] 請求完成: GET /script/ - 耗時: 22.37ms
[2025-07-10 10:15:23,030] INFO in api_logger: [1752113723020] 請求開始: GET /script/
[2025-07-10 10:15:23,033] DEBUG in api_logger: [1752113723020] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:15:23,048] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:15:23,049] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:15:23,050] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:15:23,060] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:15:23,071] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 34.75ms
[2025-07-10 10:15:23,072] INFO in api_logger: [1752113723020] 請求完成: GET /script/ - 耗時: 35.19ms
[2025-07-10 10:15:23,382] INFO in api_logger: [1752113723382] 請求開始: GET /script/
[2025-07-10 10:15:23,383] DEBUG in api_logger: [1752113723382] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:15:23,388] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:15:23,388] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:15:23,389] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:15:23,396] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:15:23,407] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.77ms
[2025-07-10 10:15:23,408] INFO in api_logger: [1752113723382] 請求完成: GET /script/ - 耗時: 24.82ms
[2025-07-10 10:16:01,391] INFO in api_logger: [1752113761390] 請求開始: GET /script/
[2025-07-10 10:16:01,399] DEBUG in api_logger: [1752113761390] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:01,415] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:01,416] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:01,417] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:01,424] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:16:01,448] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 42.82ms
[2025-07-10 10:16:01,449] INFO in api_logger: [1752113761390] 請求完成: GET /script/ - 耗時: 43.31ms
[2025-07-10 10:16:01,738] INFO in api_logger: [1752113761737] 請求開始: GET /script/
[2025-07-10 10:16:01,739] DEBUG in api_logger: [1752113761737] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:01,750] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:01,751] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:01,754] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:01,765] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:16:01,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 34.83ms
[2025-07-10 10:16:01,777] INFO in api_logger: [1752113761737] 請求完成: GET /script/ - 耗時: 35.24ms
[2025-07-10 10:16:07,604] INFO in api_logger: [1752113767604] 請求開始: GET /script/
[2025-07-10 10:16:07,605] DEBUG in api_logger: [1752113767604] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:07,610] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:07,611] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:07,611] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:07,618] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:16:07,629] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.94ms
[2025-07-10 10:16:07,629] INFO in api_logger: [1752113767604] 請求完成: GET /script/ - 耗時: 23.29ms
[2025-07-10 10:16:14,160] INFO in api_logger: [1752113774160] 請求開始: GET /script/
[2025-07-10 10:16:14,161] DEBUG in api_logger: [1752113774160] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:14,167] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:14,168] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:14,168] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:14,172] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:16:14,185] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.57ms
[2025-07-10 10:16:14,185] INFO in api_logger: [1752113774160] 請求完成: GET /script/ - 耗時: 22.98ms
[2025-07-10 10:16:19,965] INFO in api_logger: [1752113779965] 請求開始: GET /script/
[2025-07-10 10:16:19,966] DEBUG in api_logger: [1752113779965] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:19,972] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:19,972] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:19,973] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:19,978] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:16:19,987] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 19.68ms
[2025-07-10 10:16:19,988] INFO in api_logger: [1752113779965] 請求完成: GET /script/ - 耗時: 20.12ms
[2025-07-10 10:16:45,099] INFO in api_logger: [1752113805098] 請求開始: POST /script/validate
[2025-07-10 10:16:45,100] DEBUG in api_logger: [1752113805098] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7073", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryeEfHuuXMVcRwcijb", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:45,101] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-10 10:16:45,337] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-10 10:16:45,340] INFO in api_logger: [1752113805098] 請求完成: POST /script/validate - 耗時: 239.70ms
[2025-07-10 10:17:50,920] INFO in api_logger: [1752113870919] 請求開始: POST /script/
[2025-07-10 10:17:50,920] DEBUG in api_logger: [1752113870919] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7285", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryQuVI8WKUTJVfOY4a", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}, "form": {"description": "測試腳本", "tag": "網頁爬蟲"}}
[2025-07-10 10:17:50,921] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-10 10:17:50,922] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-10 10:17:50,928] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-10 10:17:50,938] INFO in script_routes: 腳本上傳調試信息:
[2025-07-10 10:17:50,938] INFO in script_routes:   文件名: output_www.py
[2025-07-10 10:17:50,939] INFO in script_routes:   描述: 測試腳本
[2025-07-10 10:17:50,939] INFO in script_routes:   標籤: 未分類
[2025-07-10 10:17:50,940] INFO in script_routes:   表單數據: {'description': '測試腳本', 'tag': '網頁爬蟲'}
[2025-07-10 10:17:50,970] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-10 10:17:50,979] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 57.52ms
[2025-07-10 10:17:50,979] INFO in api_logger: [1752113870919] 請求完成: POST /script/ - 耗時: 58.05ms
[2025-07-10 10:17:58,796] INFO in api_logger: [1752113878794] 請求開始: POST /script/
[2025-07-10 10:17:58,798] DEBUG in api_logger: [1752113878794] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7285", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryyp2FVxXtt45i43lv", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}, "form": {"description": "測試腳本", "tag": "網頁爬蟲"}}
[2025-07-10 10:17:58,801] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-10 10:17:58,802] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-10 10:17:58,818] WARNING in script_routes: 腳本已存在: output_www.py
[2025-07-10 10:17:58,818] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 17.74ms
[2025-07-10 10:17:58,819] INFO in api_logger: [1752113878794] 請求完成: POST /script/ - 耗時: 18.63ms
[2025-07-10 10:18:44,583] INFO in api_logger: [1752113924579] 請求開始: GET /script/
[2025-07-10 10:18:44,584] DEBUG in api_logger: [1752113924579] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:18:44,592] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:18:44,593] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:18:44,594] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:18:44,599] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:18:44,609] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.90ms
[2025-07-10 10:18:44,609] INFO in api_logger: [1752113924579] 請求完成: GET /script/ - 耗時: 23.19ms
[2025-07-10 10:20:35,124] INFO in api_logger: [1752114035120] 請求開始: GET /script/
[2025-07-10 10:20:35,125] DEBUG in api_logger: [1752114035120] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:20:35,139] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:20:35,141] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:20:35,142] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:20:35,154] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:20:35,168] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 40.43ms
[2025-07-10 10:20:35,168] INFO in api_logger: [1752114035120] 請求完成: GET /script/ - 耗時: 40.78ms
[2025-07-10 10:20:41,637] INFO in api_logger: [1752114041636] 請求開始: GET /script/
[2025-07-10 10:20:41,638] DEBUG in api_logger: [1752114041636] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:20:41,645] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:20:41,645] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:20:41,646] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:20:41,650] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:20:41,662] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.51ms
[2025-07-10 10:20:41,662] INFO in api_logger: [1752114041636] 請求完成: GET /script/ - 耗時: 23.07ms
[2025-07-10 10:21:07,303] INFO in api_logger: [1752114067303] 請求開始: GET /script/
[2025-07-10 10:21:07,303] DEBUG in api_logger: [1752114067303] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:21:07,308] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:21:07,308] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:21:07,309] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:21:07,313] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:21:07,322] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 17.97ms
[2025-07-10 10:21:07,322] INFO in api_logger: [1752114067303] 請求完成: GET /script/ - 耗時: 18.32ms
[2025-07-10 10:22:36,215] INFO in api_logger: [1752114156215] 請求開始: GET /script/
[2025-07-10 10:22:36,216] DEBUG in api_logger: [1752114156215] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:22:36,224] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:22:36,225] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:22:36,225] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:22:36,230] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:22:36,246] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 29.08ms
[2025-07-10 10:22:36,246] INFO in api_logger: [1752114156215] 請求完成: GET /script/ - 耗時: 29.54ms
[2025-07-10 10:23:06,430] INFO in api_logger: [1752114186430] 請求開始: GET /script/
[2025-07-10 10:23:06,431] DEBUG in api_logger: [1752114186430] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:23:06,435] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:23:06,435] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:23:06,436] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:23:06,440] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:23:06,447] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.53ms
[2025-07-10 10:23:06,447] INFO in api_logger: [1752114186430] 請求完成: GET /script/ - 耗時: 15.79ms
[2025-07-10 10:44:37,057] INFO in api_logger: [1752115477056] 請求開始: GET /script/
[2025-07-10 10:44:37,060] DEBUG in api_logger: [1752115477056] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:44:37,065] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:44:37,066] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:44:37,066] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:44:37,074] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:44:37,114] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 53.01ms
[2025-07-10 10:44:37,114] INFO in api_logger: [1752115477056] 請求完成: GET /script/ - 耗時: 53.44ms
[2025-07-10 10:44:37,562] INFO in api_logger: [1752115477562] 請求開始: GET /script/
[2025-07-10 10:44:37,563] DEBUG in api_logger: [1752115477562] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:44:37,569] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:44:37,570] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:44:37,570] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:44:37,579] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:44:37,591] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.17ms
[2025-07-10 10:44:37,592] INFO in api_logger: [1752115477562] 請求完成: GET /script/ - 耗時: 28.23ms
[2025-07-10 10:44:44,986] INFO in api_logger: [1752115484986] 請求開始: GET /script/
[2025-07-10 10:44:44,987] DEBUG in api_logger: [1752115484986] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:44:44,992] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:44:44,993] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:44:44,994] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:44:44,998] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:44:45,005] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.99ms
[2025-07-10 10:44:45,005] INFO in api_logger: [1752115484986] 請求完成: GET /script/ - 耗時: 17.33ms
[2025-07-10 10:45:00,749] INFO in api_logger: [1752115500749] 請求開始: GET /script/
[2025-07-10 10:45:00,752] DEBUG in api_logger: [1752115500749] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:45:00,766] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:45:00,767] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:45:00,768] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:45:00,782] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:45:00,834] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 76.61ms
[2025-07-10 10:45:00,834] INFO in api_logger: [1752115500749] 請求完成: GET /script/ - 耗時: 77.27ms
[2025-07-10 10:45:01,373] INFO in api_logger: [1752115501373] 請求開始: GET /script/
[2025-07-10 10:45:01,375] DEBUG in api_logger: [1752115501373] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:45:01,378] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:45:01,379] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:45:01,380] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:45:01,383] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:45:01,389] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 13.23ms
[2025-07-10 10:45:01,389] INFO in api_logger: [1752115501373] 請求完成: GET /script/ - 耗時: 13.53ms
[2025-07-10 11:05:51,675] INFO in api_logger: [1752116751668] 請求開始: GET /script/
[2025-07-10 11:05:51,675] DEBUG in api_logger: [1752116751668] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:05:51,682] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:05:51,682] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:05:51,682] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:05:51,686] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:05:51,697] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 21.56ms
[2025-07-10 11:05:51,698] INFO in api_logger: [1752116751668] 請求完成: GET /script/ - 耗時: 22.00ms
[2025-07-10 11:05:52,325] INFO in api_logger: [1752116752325] 請求開始: GET /script/
[2025-07-10 11:05:52,327] DEBUG in api_logger: [1752116752325] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:05:52,332] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:05:52,333] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:05:52,334] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:05:52,337] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:05:52,343] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.29ms
[2025-07-10 11:05:52,343] INFO in api_logger: [1752116752325] 請求完成: GET /script/ - 耗時: 15.57ms
[2025-07-10 11:06:02,591] INFO in api_logger: [1752116762591] 請求開始: GET /script/
[2025-07-10 11:06:02,592] DEBUG in api_logger: [1752116762591] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:06:02,599] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:06:02,601] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:06:02,602] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:06:02,609] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:06:02,625] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 32.06ms
[2025-07-10 11:06:02,626] INFO in api_logger: [1752116762591] 請求完成: GET /script/ - 耗時: 32.53ms
[2025-07-10 11:06:03,267] INFO in api_logger: [1752116763267] 請求開始: GET /script/
[2025-07-10 11:06:03,268] DEBUG in api_logger: [1752116763267] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:06:03,273] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:06:03,273] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:06:03,274] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:06:03,277] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:06:03,286] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 17.46ms
[2025-07-10 11:06:03,287] INFO in api_logger: [1752116763267] 請求完成: GET /script/ - 耗時: 17.95ms
[2025-07-10 11:11:57,823] INFO in api_logger: [1752117117816] 請求開始: GET /script/
[2025-07-10 11:11:57,825] DEBUG in api_logger: [1752117117816] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:11:57,834] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:11:57,835] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:11:57,842] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:11:57,849] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:11:57,858] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 31.30ms
[2025-07-10 11:11:57,859] INFO in api_logger: [1752117117816] 請求完成: GET /script/ - 耗時: 32.00ms
[2025-07-10 11:11:58,177] INFO in api_logger: [1752117118177] 請求開始: GET /script/
[2025-07-10 11:11:58,177] DEBUG in api_logger: [1752117118177] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:11:58,181] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:11:58,182] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:11:58,182] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:11:58,185] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:11:58,193] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.64ms
[2025-07-10 11:11:58,194] INFO in api_logger: [1752117118177] 請求完成: GET /script/ - 耗時: 16.09ms
[2025-07-10 11:12:13,489] INFO in api_logger: [1752117133489] 請求開始: GET /script/
[2025-07-10 11:12:13,489] DEBUG in api_logger: [1752117133489] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:13,496] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:13,496] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:13,497] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:13,501] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:13,516] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.68ms
[2025-07-10 11:12:13,517] INFO in api_logger: [1752117133489] 請求完成: GET /script/ - 耗時: 26.73ms
[2025-07-10 11:12:14,169] INFO in api_logger: [1752117134169] 請求開始: GET /script/
[2025-07-10 11:12:14,170] DEBUG in api_logger: [1752117134169] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:14,175] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:14,176] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:14,176] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:14,179] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:14,186] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.29ms
[2025-07-10 11:12:14,187] INFO in api_logger: [1752117134169] 請求完成: GET /script/ - 耗時: 15.63ms
[2025-07-10 11:12:17,765] INFO in api_logger: [1752117137765] 請求開始: GET /script/
[2025-07-10 11:12:17,768] DEBUG in api_logger: [1752117137765] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:17,797] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:17,798] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:17,799] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:17,805] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:17,825] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 34.44ms
[2025-07-10 11:12:17,825] INFO in api_logger: [1752117137765] 請求完成: GET /script/ - 耗時: 34.87ms
[2025-07-10 11:12:18,419] INFO in api_logger: [1752117138419] 請求開始: GET /script/
[2025-07-10 11:12:18,420] DEBUG in api_logger: [1752117138419] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:18,424] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:18,424] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:18,425] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:18,428] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:18,435] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 14.82ms
[2025-07-10 11:12:18,436] INFO in api_logger: [1752117138419] 請求完成: GET /script/ - 耗時: 15.16ms
[2025-07-10 11:12:20,894] INFO in api_logger: [1752117140894] 請求開始: GET /script/
[2025-07-10 11:12:20,895] DEBUG in api_logger: [1752117140894] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:20,901] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:20,901] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:20,902] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:20,908] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:20,918] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.30ms
[2025-07-10 11:12:20,918] INFO in api_logger: [1752117140894] 請求完成: GET /script/ - 耗時: 22.69ms
[2025-07-10 11:12:21,535] INFO in api_logger: [1752117141535] 請求開始: GET /script/
[2025-07-10 11:12:21,536] DEBUG in api_logger: [1752117141535] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:21,543] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:21,544] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:21,545] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:21,548] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:21,567] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.98ms
[2025-07-10 11:12:21,569] INFO in api_logger: [1752117141535] 請求完成: GET /script/ - 耗時: 30.95ms
[2025-07-10 11:12:26,278] INFO in api_logger: [1752117146277] 請求開始: GET /script/
[2025-07-10 11:12:26,284] DEBUG in api_logger: [1752117146277] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:26,294] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:26,295] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:26,296] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:26,301] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:26,326] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 38.17ms
[2025-07-10 11:12:26,327] INFO in api_logger: [1752117146277] 請求完成: GET /script/ - 耗時: 38.68ms
[2025-07-10 11:12:26,901] INFO in api_logger: [1752117146901] 請求開始: GET /script/
[2025-07-10 11:12:26,902] DEBUG in api_logger: [1752117146901] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:26,907] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:26,908] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:26,909] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:26,912] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:26,920] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 17.07ms
[2025-07-10 11:12:26,921] INFO in api_logger: [1752117146901] 請求完成: GET /script/ - 耗時: 17.41ms
[2025-07-10 11:12:41,965] INFO in api_logger: [1752117161964] 請求開始: GET /script/
[2025-07-10 11:12:41,965] DEBUG in api_logger: [1752117161964] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:41,973] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:41,974] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:41,975] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:41,980] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:41,990] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.86ms
[2025-07-10 11:12:41,991] INFO in api_logger: [1752117161964] 請求完成: GET /script/ - 耗時: 24.22ms
[2025-07-10 11:12:42,653] INFO in api_logger: [1752117162653] 請求開始: GET /script/
[2025-07-10 11:12:42,654] DEBUG in api_logger: [1752117162653] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:12:42,659] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:12:42,659] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:12:42,660] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:12:42,663] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:12:42,671] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.47ms
[2025-07-10 11:12:42,671] INFO in api_logger: [1752117162653] 請求完成: GET /script/ - 耗時: 16.78ms
[2025-07-10 11:14:47,984] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:14:48,094] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 11:15:17,958] INFO in api_logger: [1752117317958] 請求開始: GET /script/
[2025-07-10 11:15:17,972] DEBUG in api_logger: [1752117317958] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:15:17,988] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:15:17,991] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:15:17,993] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:15:18,000] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:15:18,029] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 52.94ms
[2025-07-10 11:15:18,029] INFO in api_logger: [1752117317958] 請求完成: GET /script/ - 耗時: 53.32ms
[2025-07-10 11:15:18,603] INFO in api_logger: [1752117318602] 請求開始: GET /script/
[2025-07-10 11:15:18,604] DEBUG in api_logger: [1752117318602] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:15:18,614] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:15:18,615] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:15:18,617] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:15:18,625] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:15:18,641] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.49ms
[2025-07-10 11:15:18,642] INFO in api_logger: [1752117318602] 請求完成: GET /script/ - 耗時: 36.28ms
[2025-07-10 11:15:26,033] INFO in api_logger: [1752117326033] 請求開始: GET /script/
[2025-07-10 11:15:26,034] DEBUG in api_logger: [1752117326033] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:15:26,038] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:15:26,038] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:15:26,039] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:15:26,042] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:15:26,050] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.58ms
[2025-07-10 11:15:26,050] INFO in api_logger: [1752117326033] 請求完成: GET /script/ - 耗時: 15.96ms
[2025-07-10 11:15:33,529] INFO in api_logger: [1752117333528] 請求開始: GET /script/
[2025-07-10 11:15:33,529] DEBUG in api_logger: [1752117333528] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:15:33,534] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:15:33,535] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:15:33,536] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:15:33,540] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:15:33,547] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.99ms
[2025-07-10 11:15:33,548] INFO in api_logger: [1752117333528] 請求完成: GET /script/ - 耗時: 17.34ms
[2025-07-10 11:15:38,583] INFO in api_logger: [1752117338582] 請求開始: GET /script/
[2025-07-10 11:15:38,583] DEBUG in api_logger: [1752117338582] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:15:38,588] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:15:38,589] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:15:38,590] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:15:38,594] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:15:38,603] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 19.18ms
[2025-07-10 11:15:38,603] INFO in api_logger: [1752117338582] 請求完成: GET /script/ - 耗時: 19.58ms
[2025-07-10 11:16:35,711] INFO in api_logger: [1752117395711] 請求開始: DELETE /api/v1/schedules/1
[2025-07-10 11:16:35,712] DEBUG in api_logger: [1752117395711] 請求數據: {"method": "DELETE", "path": "/api/v1/schedules/1", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:16:35,712] INFO in schedules: 刪除排程: 1
[2025-07-10 11:16:35,712] ERROR in schedules: 刪除排程API失敗: 'ScheduleManager' object has no attribute 'get_schedule_by_id'
[2025-07-10 11:16:35,713] INFO in api_logger: 功能 [刪除排程] 正常運行 - 耗時: 0.77ms
[2025-07-10 11:16:35,713] INFO in api_logger: [1752117395711] 請求完成: DELETE /api/v1/schedules/1 - 耗時: 0.98ms
[2025-07-10 11:16:39,478] INFO in api_logger: [1752117399478] 請求開始: DELETE /api/v1/schedules/1
[2025-07-10 11:16:39,481] DEBUG in api_logger: [1752117399478] 請求數據: {"method": "DELETE", "path": "/api/v1/schedules/1", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:16:39,482] INFO in schedules: 刪除排程: 1
[2025-07-10 11:16:39,483] ERROR in schedules: 刪除排程API失敗: 'ScheduleManager' object has no attribute 'get_schedule_by_id'
[2025-07-10 11:16:39,484] INFO in api_logger: 功能 [刪除排程] 正常運行 - 耗時: 2.20ms
[2025-07-10 11:16:39,485] INFO in api_logger: [1752117399478] 請求完成: DELETE /api/v1/schedules/1 - 耗時: 2.83ms
[2025-07-10 11:16:48,611] INFO in api_logger: [1752117408610] 請求開始: GET /script/
[2025-07-10 11:16:48,615] DEBUG in api_logger: [1752117408610] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:16:48,627] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:16:48,628] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:16:48,629] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:16:48,637] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:16:48,648] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 30.13ms
[2025-07-10 11:16:48,649] INFO in api_logger: [1752117408610] 請求完成: GET /script/ - 耗時: 30.68ms
[2025-07-10 11:16:48,968] INFO in api_logger: [1752117408968] 請求開始: GET /script/
[2025-07-10 11:16:48,969] DEBUG in api_logger: [1752117408968] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:16:48,974] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:16:48,974] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:16:48,975] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:16:48,979] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:16:48,989] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.15ms
[2025-07-10 11:16:48,990] INFO in api_logger: [1752117408968] 請求完成: GET /script/ - 耗時: 20.55ms
[2025-07-10 11:16:53,341] INFO in api_logger: [1752117413341] 請求開始: DELETE /api/v1/schedules/1
[2025-07-10 11:16:53,342] DEBUG in api_logger: [1752117413341] 請求數據: {"method": "DELETE", "path": "/api/v1/schedules/1", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:16:53,344] INFO in schedules: 刪除排程: 1
[2025-07-10 11:16:53,345] ERROR in schedules: 刪除排程API失敗: 'ScheduleManager' object has no attribute 'get_schedule_by_id'
[2025-07-10 11:16:53,346] INFO in api_logger: 功能 [刪除排程] 正常運行 - 耗時: 2.35ms
[2025-07-10 11:16:53,347] INFO in api_logger: [1752117413341] 請求完成: DELETE /api/v1/schedules/1 - 耗時: 3.07ms
[2025-07-10 11:17:00,002] INFO in api_logger: [1752117420002] 請求開始: DELETE /api/v1/schedules/1
[2025-07-10 11:17:00,003] DEBUG in api_logger: [1752117420002] 請求數據: {"method": "DELETE", "path": "/api/v1/schedules/1", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:17:00,004] INFO in schedules: 刪除排程: 1
[2025-07-10 11:17:00,004] ERROR in schedules: 刪除排程API失敗: 'ScheduleManager' object has no attribute 'get_schedule_by_id'
[2025-07-10 11:17:00,005] INFO in api_logger: 功能 [刪除排程] 正常運行 - 耗時: 1.11ms
[2025-07-10 11:17:00,005] INFO in api_logger: [1752117420002] 請求完成: DELETE /api/v1/schedules/1 - 耗時: 1.50ms
[2025-07-10 11:17:03,827] INFO in api_logger: [1752117423826] 請求開始: GET /script/
[2025-07-10 11:17:03,830] DEBUG in api_logger: [1752117423826] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:17:03,842] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:17:03,843] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:17:03,844] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:17:03,849] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:17:03,859] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.69ms
[2025-07-10 11:17:03,859] INFO in api_logger: [1752117423826] 請求完成: GET /script/ - 耗時: 24.15ms
[2025-07-10 11:17:04,168] INFO in api_logger: [1752117424168] 請求開始: GET /script/
[2025-07-10 11:17:04,169] DEBUG in api_logger: [1752117424168] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:17:04,175] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:17:04,175] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:17:04,176] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:17:04,180] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:17:04,187] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.73ms
[2025-07-10 11:17:04,187] INFO in api_logger: [1752117424168] 請求完成: GET /script/ - 耗時: 17.04ms
[2025-07-10 11:17:08,172] INFO in api_logger: [1752117428171] 請求開始: GET /script/
[2025-07-10 11:17:08,177] DEBUG in api_logger: [1752117428171] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:17:08,199] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:17:08,203] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:17:08,204] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:17:08,210] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:17:08,222] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 41.20ms
[2025-07-10 11:17:08,222] INFO in api_logger: [1752117428171] 請求完成: GET /script/ - 耗時: 41.65ms
[2025-07-10 11:17:08,792] INFO in api_logger: [1752117428791] 請求開始: GET /script/
[2025-07-10 11:17:08,792] DEBUG in api_logger: [1752117428791] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:17:08,797] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:17:08,798] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:17:08,799] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:17:08,802] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:17:08,809] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.71ms
[2025-07-10 11:17:08,809] INFO in api_logger: [1752117428791] 請求完成: GET /script/ - 耗時: 16.04ms
[2025-07-10 11:17:10,617] INFO in api_logger: [1752117430617] 請求開始: GET /script/
[2025-07-10 11:17:10,618] DEBUG in api_logger: [1752117430617] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:17:10,622] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:17:10,623] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:17:10,623] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:17:10,627] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:17:10,635] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 16.29ms
[2025-07-10 11:17:10,635] INFO in api_logger: [1752117430617] 請求完成: GET /script/ - 耗時: 16.70ms
[2025-07-10 11:17:29,495] INFO in api_logger: [1752117449494] 請求開始: POST /script/validate
[2025-07-10 11:17:29,495] DEBUG in api_logger: [1752117449494] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7073", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarycL80CqbUuwqEVZJI", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:17:29,496] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-10 11:17:29,665] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-10 11:17:29,669] INFO in api_logger: [1752117449494] 請求完成: POST /script/validate - 耗時: 172.96ms
[2025-07-10 11:17:54,780] INFO in api_logger: [1752117474779] 請求開始: POST /script/
[2025-07-10 11:17:54,780] DEBUG in api_logger: [1752117474779] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7285", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryCNbyyxPgdKNGXR98", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}, "form": {"description": "測試腳本", "tag": "網頁爬蟲"}}
[2025-07-10 11:17:54,781] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-10 11:17:54,781] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-10 11:17:54,785] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-10 11:17:54,789] INFO in script_routes: 腳本上傳調試信息:
[2025-07-10 11:17:54,789] INFO in script_routes:   文件名: output_www.py
[2025-07-10 11:17:54,790] INFO in script_routes:   描述: 測試腳本
[2025-07-10 11:17:54,790] INFO in script_routes:   標籤: 未分類
[2025-07-10 11:17:54,790] INFO in script_routes:   表單數據: {'description': '測試腳本', 'tag': '網頁爬蟲'}
[2025-07-10 11:17:54,803] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-10 11:17:54,806] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 24.87ms
[2025-07-10 11:17:54,806] INFO in api_logger: [1752117474779] 請求完成: POST /script/ - 耗時: 25.20ms
[2025-07-10 11:19:28,438] INFO in api_logger: [1752117568438] 請求開始: GET /script/
[2025-07-10 11:19:28,439] DEBUG in api_logger: [1752117568438] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:19:28,445] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:19:28,446] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:19:28,447] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:19:28,452] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:19:28,461] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.34ms
[2025-07-10 11:19:28,461] INFO in api_logger: [1752117568438] 請求完成: GET /script/ - 耗時: 20.77ms
[2025-07-10 11:24:35,223] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:24:35,340] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 11:24:51,130] INFO in api_logger: [1752117891123] 請求開始: GET /script/
[2025-07-10 11:24:51,131] DEBUG in api_logger: [1752117891123] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:24:51,146] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:24:51,147] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:24:51,148] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:24:51,161] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:24:51,180] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 46.32ms
[2025-07-10 11:24:51,182] INFO in api_logger: [1752117891123] 請求完成: GET /script/ - 耗時: 48.29ms
[2025-07-10 11:24:51,508] INFO in api_logger: [1752117891508] 請求開始: GET /script/
[2025-07-10 11:24:51,509] DEBUG in api_logger: [1752117891508] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:24:51,519] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:24:51,520] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:24:51,522] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:24:51,530] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:24:51,541] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 30.29ms
[2025-07-10 11:24:51,542] INFO in api_logger: [1752117891508] 請求完成: GET /script/ - 耗時: 30.70ms
[2025-07-10 11:25:43,042] INFO in api_logger: [1752117943042] 請求開始: GET /script/
[2025-07-10 11:25:43,046] DEBUG in api_logger: [1752117943042] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:25:43,063] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:25:43,064] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:25:43,065] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:25:43,071] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:25:43,082] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 24.83ms
[2025-07-10 11:25:43,082] INFO in api_logger: [1752117943042] 請求完成: GET /script/ - 耗時: 25.31ms
[2025-07-10 11:25:43,671] INFO in api_logger: [1752117943670] 請求開始: GET /script/
[2025-07-10 11:25:43,671] DEBUG in api_logger: [1752117943670] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:25:43,677] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:25:43,677] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:25:43,678] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:25:43,682] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:25:43,691] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 19.09ms
[2025-07-10 11:25:43,692] INFO in api_logger: [1752117943670] 請求完成: GET /script/ - 耗時: 19.49ms
[2025-07-10 11:25:52,003] INFO in api_logger: [1752117952002] 請求開始: GET /script/
[2025-07-10 11:25:52,005] DEBUG in api_logger: [1752117952002] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:25:52,013] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:25:52,014] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:25:52,015] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:25:52,036] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:25:52,054] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 48.39ms
[2025-07-10 11:25:52,055] INFO in api_logger: [1752117952002] 請求完成: GET /script/ - 耗時: 48.70ms
[2025-07-10 11:25:52,378] INFO in api_logger: [1752117952378] 請求開始: GET /script/
[2025-07-10 11:25:52,380] DEBUG in api_logger: [1752117952378] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:25:52,390] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:25:52,391] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:25:52,393] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:25:52,402] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:25:52,413] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 31.89ms
[2025-07-10 11:25:52,414] INFO in api_logger: [1752117952378] 請求完成: GET /script/ - 耗時: 32.33ms
[2025-07-10 11:25:55,676] INFO in api_logger: [1752117955676] 請求開始: GET /script/
[2025-07-10 11:25:55,699] DEBUG in api_logger: [1752117955676] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:25:55,709] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:25:55,710] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:25:55,711] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:25:55,723] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:25:55,739] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 36.73ms
[2025-07-10 11:25:55,739] INFO in api_logger: [1752117955676] 請求完成: GET /script/ - 耗時: 37.13ms
[2025-07-10 11:25:56,019] INFO in api_logger: [1752117956019] 請求開始: GET /script/
[2025-07-10 11:25:56,020] DEBUG in api_logger: [1752117956019] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:25:56,026] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:25:56,026] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:25:56,027] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:25:56,034] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:25:56,044] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.57ms
[2025-07-10 11:25:56,045] INFO in api_logger: [1752117956019] 請求完成: GET /script/ - 耗時: 23.98ms
[2025-07-10 11:26:00,634] INFO in api_logger: [1752117960633] 請求開始: GET /script/
[2025-07-10 11:26:00,639] DEBUG in api_logger: [1752117960633] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:26:00,650] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:26:00,651] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:26:00,652] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:26:00,662] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:26:00,673] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.55ms
[2025-07-10 11:26:00,674] INFO in api_logger: [1752117960633] 請求完成: GET /script/ - 耗時: 29.11ms
[2025-07-10 11:26:01,254] INFO in api_logger: [1752117961254] 請求開始: GET /script/
[2025-07-10 11:26:01,254] DEBUG in api_logger: [1752117961254] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:26:01,260] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:26:01,260] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:26:01,261] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:26:01,267] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:26:01,276] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.86ms
[2025-07-10 11:26:01,277] INFO in api_logger: [1752117961254] 請求完成: GET /script/ - 耗時: 21.30ms
[2025-07-10 11:26:57,689] INFO in api_logger: [1752118017689] 請求開始: GET /script/
[2025-07-10 11:26:57,690] DEBUG in api_logger: [1752118017689] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:26:57,697] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:26:57,698] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:26:57,701] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:26:57,708] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:26:57,719] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.57ms
[2025-07-10 11:26:57,720] INFO in api_logger: [1752118017689] 請求完成: GET /script/ - 耗時: 29.21ms
[2025-07-10 11:27:04,183] INFO in api_logger: [1752118024183] 請求開始: GET /script/
[2025-07-10 11:27:04,184] DEBUG in api_logger: [1752118024183] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:27:04,189] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:27:04,190] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:27:04,190] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:27:04,196] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:27:04,206] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 21.66ms
[2025-07-10 11:27:04,207] INFO in api_logger: [1752118024183] 請求完成: GET /script/ - 耗時: 22.42ms
[2025-07-10 11:27:08,136] INFO in api_logger: [1752118028136] 請求開始: GET /script/
[2025-07-10 11:27:08,136] DEBUG in api_logger: [1752118028136] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:27:08,142] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:27:08,142] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:27:08,143] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:27:08,147] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:27:08,161] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.98ms
[2025-07-10 11:27:08,162] INFO in api_logger: [1752118028136] 請求完成: GET /script/ - 耗時: 24.44ms
[2025-07-10 11:27:50,521] INFO in api_logger: [1752118070521] 請求開始: GET /script/
[2025-07-10 11:27:50,523] DEBUG in api_logger: [1752118070521] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:27:50,534] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:27:50,535] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:27:50,537] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:27:50,541] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:27:50,561] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 34.48ms
[2025-07-10 11:27:50,566] INFO in api_logger: [1752118070521] 請求完成: GET /script/ - 耗時: 39.03ms
[2025-07-10 11:27:50,881] INFO in api_logger: [1752118070881] 請求開始: GET /script/
[2025-07-10 11:27:50,882] DEBUG in api_logger: [1752118070881] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:27:50,890] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:27:50,891] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:27:50,891] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:27:50,896] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:27:50,903] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.23ms
[2025-07-10 11:27:50,904] INFO in api_logger: [1752118070881] 請求完成: GET /script/ - 耗時: 20.59ms
[2025-07-10 11:27:53,893] INFO in api_logger: [1752118073893] 請求開始: GET /script/
[2025-07-10 11:27:53,897] DEBUG in api_logger: [1752118073893] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:27:53,907] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:27:53,908] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:27:53,909] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:27:53,913] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:27:53,925] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.38ms
[2025-07-10 11:27:53,928] INFO in api_logger: [1752118073893] 請求完成: GET /script/ - 耗時: 28.14ms
[2025-07-10 11:27:54,237] INFO in api_logger: [1752118074237] 請求開始: GET /script/
[2025-07-10 11:27:54,238] DEBUG in api_logger: [1752118074237] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:27:54,245] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:27:54,246] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:27:54,246] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:27:54,251] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:27:54,262] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.44ms
[2025-07-10 11:27:54,262] INFO in api_logger: [1752118074237] 請求完成: GET /script/ - 耗時: 22.89ms
[2025-07-10 11:27:56,972] INFO in api_logger: [1752118076972] 請求開始: GET /script/
[2025-07-10 11:27:56,978] DEBUG in api_logger: [1752118076972] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:27:56,989] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:27:56,990] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:27:56,991] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:27:56,997] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:27:57,008] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.76ms
[2025-07-10 11:27:57,008] INFO in api_logger: [1752118076972] 請求完成: GET /script/ - 耗時: 28.14ms
[2025-07-10 11:27:57,317] INFO in api_logger: [1752118077316] 請求開始: GET /script/
[2025-07-10 11:27:57,319] DEBUG in api_logger: [1752118077316] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:27:57,328] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:27:57,329] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:27:57,330] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:27:57,337] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:27:57,346] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 24.13ms
[2025-07-10 11:27:57,347] INFO in api_logger: [1752118077316] 請求完成: GET /script/ - 耗時: 24.60ms
[2025-07-10 11:28:17,427] INFO in api_logger: [1752118097427] 請求開始: GET /script/
[2025-07-10 11:28:17,427] DEBUG in api_logger: [1752118097427] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:28:17,450] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:28:17,450] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:28:17,454] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:28:17,462] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:28:17,476] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 47.74ms
[2025-07-10 11:28:17,476] INFO in api_logger: [1752118097427] 請求完成: GET /script/ - 耗時: 48.14ms
[2025-07-10 11:28:17,787] INFO in api_logger: [1752118097786] 請求開始: GET /script/
[2025-07-10 11:28:17,787] DEBUG in api_logger: [1752118097786] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:28:17,794] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:28:17,795] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:28:17,796] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:28:17,803] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:28:17,831] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 42.38ms
[2025-07-10 11:28:17,833] INFO in api_logger: [1752118097786] 請求完成: GET /script/ - 耗時: 44.99ms
[2025-07-10 11:28:28,267] INFO in api_logger: [1752118108267] 請求開始: DELETE /api/v1/schedules/1
[2025-07-10 11:28:28,269] DEBUG in api_logger: [1752118108267] 請求數據: {"method": "DELETE", "path": "/api/v1/schedules/1", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:28:28,270] INFO in schedules: 刪除排程: 1
[2025-07-10 11:28:28,271] ERROR in schedules: 刪除排程API失敗: 'ScheduleManager' object has no attribute 'get_schedule_by_id'
[2025-07-10 11:28:28,271] INFO in api_logger: 功能 [刪除排程] 正常運行 - 耗時: 1.35ms
[2025-07-10 11:28:28,272] INFO in api_logger: [1752118108267] 請求完成: DELETE /api/v1/schedules/1 - 耗時: 1.78ms
[2025-07-10 11:28:34,982] INFO in api_logger: [1752118114982] 請求開始: DELETE /api/v1/schedules/1
[2025-07-10 11:28:34,983] DEBUG in api_logger: [1752118114982] 請求數據: {"method": "DELETE", "path": "/api/v1/schedules/1", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:28:34,984] INFO in schedules: 刪除排程: 1
[2025-07-10 11:28:34,986] ERROR in schedules: 刪除排程API失敗: 'ScheduleManager' object has no attribute 'get_schedule_by_id'
[2025-07-10 11:28:34,987] INFO in api_logger: 功能 [刪除排程] 正常運行 - 耗時: 3.52ms
[2025-07-10 11:28:34,988] INFO in api_logger: [1752118114982] 請求完成: DELETE /api/v1/schedules/1 - 耗時: 4.04ms
[2025-07-10 11:28:54,669] INFO in api_logger: [1752118134669] 請求開始: DELETE /api/v1/schedules/1
[2025-07-10 11:28:54,670] DEBUG in api_logger: [1752118134669] 請求數據: {"method": "DELETE", "path": "/api/v1/schedules/1", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:28:54,672] INFO in schedules: 刪除排程: 1
[2025-07-10 11:28:54,673] ERROR in schedules: 刪除排程API失敗: 'ScheduleManager' object has no attribute 'get_schedule_by_id'
[2025-07-10 11:28:54,674] INFO in api_logger: 功能 [刪除排程] 正常運行 - 耗時: 2.56ms
[2025-07-10 11:28:54,675] INFO in api_logger: [1752118134669] 請求完成: DELETE /api/v1/schedules/1 - 耗時: 3.30ms
[2025-07-10 11:29:00,770] INFO in api_logger: [1752118140770] 請求開始: GET /script/
[2025-07-10 11:29:00,775] DEBUG in api_logger: [1752118140770] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:29:00,789] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:29:00,792] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:29:00,793] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:29:00,799] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:29:00,815] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 34.99ms
[2025-07-10 11:29:00,817] INFO in api_logger: [1752118140770] 請求完成: GET /script/ - 耗時: 36.63ms
[2025-07-10 11:29:01,143] INFO in api_logger: [1752118141142] 請求開始: GET /script/
[2025-07-10 11:29:01,143] DEBUG in api_logger: [1752118141142] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:29:01,149] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:29:01,150] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:29:01,151] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:29:01,157] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:29:01,167] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.29ms
[2025-07-10 11:29:01,169] INFO in api_logger: [1752118141142] 請求完成: GET /script/ - 耗時: 24.51ms
[2025-07-10 11:30:19,204] INFO in api_logger: [1752118219204] 請求開始: GET /script/
[2025-07-10 11:30:19,212] DEBUG in api_logger: [1752118219204] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:30:19,226] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:30:19,228] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:30:19,229] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:30:19,235] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:30:19,252] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.97ms
[2025-07-10 11:30:19,254] INFO in api_logger: [1752118219204] 請求完成: GET /script/ - 耗時: 37.20ms
[2025-07-10 11:30:19,831] INFO in api_logger: [1752118219831] 請求開始: GET /script/
[2025-07-10 11:30:19,832] DEBUG in api_logger: [1752118219831] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:30:19,841] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:30:19,842] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:30:19,843] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:30:19,846] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:30:19,854] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 19.60ms
[2025-07-10 11:30:19,854] INFO in api_logger: [1752118219831] 請求完成: GET /script/ - 耗時: 19.87ms
[2025-07-10 11:42:33,931] INFO in api_logger: [1752118953931] 請求開始: GET /script/
[2025-07-10 11:42:33,942] DEBUG in api_logger: [1752118953931] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:42:33,954] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:42:33,954] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:42:33,955] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:42:33,962] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:42:33,990] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 43.38ms
[2025-07-10 11:42:33,990] INFO in api_logger: [1752118953931] 請求完成: GET /script/ - 耗時: 43.92ms
[2025-07-10 11:42:34,563] INFO in api_logger: [1752118954563] 請求開始: GET /script/
[2025-07-10 11:42:34,564] DEBUG in api_logger: [1752118954563] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:42:34,569] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:42:34,569] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:42:34,570] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:42:34,575] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:42:34,585] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 19.99ms
[2025-07-10 11:42:34,585] INFO in api_logger: [1752118954563] 請求完成: GET /script/ - 耗時: 20.32ms
[2025-07-10 11:43:01,975] INFO in api_logger: [1752118981975] 請求開始: GET /script/
[2025-07-10 11:43:01,984] DEBUG in api_logger: [1752118981975] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:43:01,994] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:43:01,995] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:43:01,996] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:43:02,003] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:43:02,013] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.14ms
[2025-07-10 11:43:02,013] INFO in api_logger: [1752118981975] 請求完成: GET /script/ - 耗時: 25.61ms
[2025-07-10 11:43:02,611] INFO in api_logger: [1752118982611] 請求開始: GET /script/
[2025-07-10 11:43:02,612] DEBUG in api_logger: [1752118982611] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:43:02,615] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:43:02,615] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:43:02,616] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:43:02,619] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:43:02,625] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 12.20ms
[2025-07-10 11:43:02,625] INFO in api_logger: [1752118982611] 請求完成: GET /script/ - 耗時: 12.55ms
[2025-07-10 11:43:13,989] INFO in api_logger: [1752118993989] 請求開始: DELETE /api/v1/schedules/1
[2025-07-10 11:43:13,990] DEBUG in api_logger: [1752118993989] 請求數據: {"method": "DELETE", "path": "/api/v1/schedules/1", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:43:13,990] INFO in schedules: 刪除排程: 1
[2025-07-10 11:43:13,991] ERROR in schedules: 刪除排程API失敗: 'ScheduleManager' object has no attribute 'get_schedule_by_id'
[2025-07-10 11:43:13,991] INFO in api_logger: 功能 [刪除排程] 正常運行 - 耗時: 1.04ms
[2025-07-10 11:43:13,992] INFO in api_logger: [1752118993989] 請求完成: DELETE /api/v1/schedules/1 - 耗時: 1.43ms
[2025-07-10 11:43:57,932] INFO in api_logger: [1752119037932] 請求開始: DELETE /api/v1/schedules/1
[2025-07-10 11:43:57,934] DEBUG in api_logger: [1752119037932] 請求數據: {"method": "DELETE", "path": "/api/v1/schedules/1", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:43:57,935] INFO in schedules: 刪除排程: 1
[2025-07-10 11:43:57,935] ERROR in schedules: 刪除排程API失敗: 'ScheduleManager' object has no attribute 'get_schedule_by_id'
[2025-07-10 11:43:57,936] INFO in api_logger: 功能 [刪除排程] 正常運行 - 耗時: 0.99ms
[2025-07-10 11:43:57,936] INFO in api_logger: [1752119037932] 請求完成: DELETE /api/v1/schedules/1 - 耗時: 1.26ms
[2025-07-10 11:47:19,650] INFO in api_logger: [1752119239646] 請求開始: GET /script/
[2025-07-10 11:47:19,664] DEBUG in api_logger: [1752119239646] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:47:19,684] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:47:19,688] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:47:19,695] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:47:19,715] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:47:19,735] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 64.84ms
[2025-07-10 11:47:19,735] INFO in api_logger: [1752119239646] 請求完成: GET /script/ - 耗時: 65.24ms
[2025-07-10 11:47:19,996] INFO in api_logger: [1752119239996] 請求開始: GET /script/
[2025-07-10 11:47:19,996] DEBUG in api_logger: [1752119239996] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:47:20,003] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:47:20,003] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:47:20,004] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:47:20,010] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:47:20,021] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.49ms
[2025-07-10 11:47:20,021] INFO in api_logger: [1752119239996] 請求完成: GET /script/ - 耗時: 23.85ms
[2025-07-10 11:47:28,014] INFO in api_logger: [1752119248014] 請求開始: GET /script/
[2025-07-10 11:47:28,022] DEBUG in api_logger: [1752119248014] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:47:28,041] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:47:28,045] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:47:28,046] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:47:28,050] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:47:28,061] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 37.23ms
[2025-07-10 11:47:28,061] INFO in api_logger: [1752119248014] 請求完成: GET /script/ - 耗時: 37.77ms
[2025-07-10 11:47:28,638] INFO in api_logger: [1752119248638] 請求開始: GET /script/
[2025-07-10 11:47:28,639] DEBUG in api_logger: [1752119248638] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:47:28,650] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:47:28,652] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:47:28,653] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:47:28,659] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:47:28,669] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.81ms
[2025-07-10 11:47:28,669] INFO in api_logger: [1752119248638] 請求完成: GET /script/ - 耗時: 28.15ms
[2025-07-10 11:47:48,807] INFO in api_logger: [1752119268807] 請求開始: GET /script/
[2025-07-10 11:47:48,808] DEBUG in api_logger: [1752119268807] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:47:48,814] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:47:48,815] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:47:48,815] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:47:48,820] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:47:48,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 26.67ms
[2025-07-10 11:47:48,841] INFO in api_logger: [1752119268807] 請求完成: GET /script/ - 耗時: 32.14ms
[2025-07-10 11:47:49,495] INFO in api_logger: [1752119269494] 請求開始: GET /script/
[2025-07-10 11:47:49,495] DEBUG in api_logger: [1752119269494] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:47:49,501] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:47:49,502] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:47:49,503] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:47:49,507] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:47:49,516] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 19.83ms
[2025-07-10 11:47:49,517] INFO in api_logger: [1752119269494] 請求完成: GET /script/ - 耗時: 20.18ms
[2025-07-10 11:47:55,931] INFO in api_logger: [1752119275930] 請求開始: GET /script/
[2025-07-10 11:47:55,933] DEBUG in api_logger: [1752119275930] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:47:55,973] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:47:55,978] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:47:55,981] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:47:56,015] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 11:47:56,068] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 115.59ms
[2025-07-10 11:47:56,070] INFO in api_logger: [1752119275930] 請求完成: GET /script/ - 耗時: 117.77ms
[2025-07-10 11:48:06,735] INFO in api_logger: [1752119286734] 請求開始: POST /script/validate
[2025-07-10 11:48:06,736] DEBUG in api_logger: [1752119286734] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7073", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryRzNokAFjxjocHxRD", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:48:06,738] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-10 11:48:06,885] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-10 11:48:06,888] INFO in api_logger: [1752119286734] 請求完成: POST /script/validate - 耗時: 150.45ms
[2025-07-10 11:48:26,216] INFO in api_logger: [1752119306215] 請求開始: POST /script/
[2025-07-10 11:48:26,217] DEBUG in api_logger: [1752119306215] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7285", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryZBv3RiWD7CHbS1f4", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}, "form": {"description": "測試腳本", "tag": "網頁爬蟲"}}
[2025-07-10 11:48:26,218] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-10 11:48:26,219] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-10 11:48:26,225] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-10 11:48:26,236] INFO in script_routes: 腳本上傳調試信息:
[2025-07-10 11:48:26,236] INFO in script_routes:   文件名: output_www.py
[2025-07-10 11:48:26,237] INFO in script_routes:   描述: 測試腳本
[2025-07-10 11:48:26,237] INFO in script_routes:   標籤: 未分類
[2025-07-10 11:48:26,238] INFO in script_routes:   表單數據: {'description': '測試腳本', 'tag': '網頁爬蟲'}
[2025-07-10 11:48:26,262] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-10 11:48:26,285] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 66.38ms
[2025-07-10 11:48:26,285] INFO in api_logger: [1752119306215] 請求完成: POST /script/ - 耗時: 66.89ms
[2025-07-10 11:48:42,951] INFO in api_logger: [1752119322951] 請求開始: GET /script/
[2025-07-10 11:48:42,953] DEBUG in api_logger: [1752119322951] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:48:42,968] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:48:42,969] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:48:42,971] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:48:42,982] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:48:42,996] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 41.32ms
[2025-07-10 11:48:42,997] INFO in api_logger: [1752119322951] 請求完成: GET /script/ - 耗時: 41.78ms
[2025-07-10 11:50:54,150] INFO in api_logger: [1752119454150] 請求開始: GET /script/
[2025-07-10 11:50:54,150] DEBUG in api_logger: [1752119454150] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:50:54,156] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:50:54,156] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:50:54,157] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:50:54,162] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:50:54,170] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 18.27ms
[2025-07-10 11:50:54,170] INFO in api_logger: [1752119454150] 請求完成: GET /script/ - 耗時: 18.62ms
[2025-07-10 11:58:30,955] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:58:31,069] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 11:58:45,348] INFO in api_logger: [1752119925348] 請求開始: GET /script/
[2025-07-10 11:58:45,349] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:58:45,350] DEBUG in api_logger: [1752119925348] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:58:45,355] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:58:45,355] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:58:45,357] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:58:45,364] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:58:45,374] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.86ms
[2025-07-10 11:58:45,374] INFO in api_logger: [1752119925348] 請求完成: GET /script/ - 耗時: 23.27ms
[2025-07-10 11:58:45,466] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 11:58:45,659] INFO in api_logger: [1752119925659] 請求開始: GET /script/
[2025-07-10 11:58:45,659] DEBUG in api_logger: [1752119925659] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 11:58:45,666] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 11:58:45,666] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 11:58:45,667] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 11:58:45,671] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 11:58:45,682] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 21.67ms
[2025-07-10 11:58:45,682] INFO in api_logger: [1752119925659] 請求完成: GET /script/ - 耗時: 22.08ms
[2025-07-10 11:58:45,685] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:58:45,803] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 11:58:45,805] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:58:45,930] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 11:58:50,036] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:58:50,151] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 11:58:50,465] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:58:50,579] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 11:58:55,038] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:58:55,153] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 11:58:55,481] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 11:58:55,593] DEBUG in system_routes: 使用系統監控服務獲取資源信息
