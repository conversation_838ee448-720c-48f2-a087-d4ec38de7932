[2025-06-12 13:33:47,094] INFO in api_logger: [1749706427093] 請求開始: GET /script/
[2025-06-12 13:33:47,099] DEBUG in api_logger: [1749706427093] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:47,108] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:47,110] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.13ms
[2025-06-12 13:33:47,110] INFO in api_logger: [1749706427093] 請求完成: GET /script/ - 耗時: 8.70ms
[2025-06-12 13:33:47,140] INFO in api_logger: [1749706427140] 請求開始: GET /script/
[2025-06-12 13:33:47,141] DEBUG in api_logger: [1749706427140] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:47,151] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:47,153] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.91ms
[2025-06-12 13:33:47,153] INFO in api_logger: [1749706427140] 請求完成: GET /script/ - 耗時: 9.46ms
[2025-06-12 13:33:58,585] INFO in api_logger: [1749706438585] 請求開始: GET /script/
[2025-06-12 13:33:58,586] DEBUG in api_logger: [1749706438585] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:58,595] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:58,597] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.67ms
[2025-06-12 13:33:58,598] INFO in api_logger: [1749706438585] 請求完成: GET /script/ - 耗時: 7.81ms
[2025-06-12 13:35:31,994] INFO in api_logger: [1749706531994] 請求開始: GET /script/
[2025-06-12 13:35:31,995] DEBUG in api_logger: [1749706531994] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:35:32,002] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:35:32,003] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.99ms
[2025-06-12 13:35:32,004] INFO in api_logger: [1749706531994] 請求完成: GET /script/ - 耗時: 7.50ms
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,771] INFO in api_logger: [1751957794755] 請求開始: GET /script/
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,772] DEBUG in api_logger: [1751957794755] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,776] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.63ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,777] INFO in api_logger: [1751957794755] 請求完成: GET /script/ - 耗時: 4.81ms
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] INFO in api_logger: [1751957794837] 請求開始: GET /script/
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,837] DEBUG in api_logger: [1751957794837] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,841] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.35ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:34,843] INFO in api_logger: [1751957794837] 請求完成: GET /script/ - 耗時: 5.57ms
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,011] INFO in api_logger: [1751957795011] 請求開始: POST /script/
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in api_logger: [1751957795011] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.010780.41886447284802353\"", "Content-Length": "402"}, "args": {}, "form": {"description": "無效文件測試"}}
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,012] WARNING in script_routes: 不支持的文件類型: test.txt
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.53ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,013] INFO in api_logger: [1751957795011] 請求完成: POST /script/ - 耗時: 0.79ms
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] INFO in api_logger: [1751957795070] 請求開始: POST /script/
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,071] DEBUG in api_logger: [1751957795070] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.07045460.7162329099278886\"", "Content-Length": "76"}, "args": {}}
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] WARNING in script_routes: 未找到上傳文件
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,072] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 0.66ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,073] INFO in api_logger: [1751957795070] 請求完成: POST /script/ - 耗時: 0.98ms
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] INFO in api_logger: [1751957795135] 請求開始: POST /script/
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,136] DEBUG in api_logger: [1751957795135] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost", "Content-Type": "multipart/form-data; boundary=\"---------------WerkzeugFormPart_1751957795.13551020.7675124033390599\"", "Content-Length": "694"}, "args": {}, "form": {"description": "上傳測試腳本"}}
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,137] DEBUG in script_routes: 準備上傳腳本: upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,139] DEBUG in script_routes: 保存文件到: C:\TEMP\tmp5r7apck8\scripts\upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,146] INFO in script_routes: 腳本上傳成功: upload_test.py
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 10.91ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:56:35,148] INFO in api_logger: [1751957795135] 請求完成: POST /script/ - 耗時: 11.22ms
[2025-07-08 14:57:18,774] INFO in api_logger: [1751957838772] 請求開始: GET /script/
[2025-07-08 14:57:18,774] DEBUG in api_logger: [1751957838772] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:57:18,778] INFO in script_routes: 發現未記錄的腳本文件: test_script.py，同步到資料庫
[2025-07-08 14:57:18,780] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:57:18,780] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 5.28ms
[2025-07-08 14:57:18,780] INFO in api_logger: [1751957838772] 請求完成: GET /script/ - 耗時: 5.41ms
[2025-07-08 14:57:29,699] INFO in api_logger: [1751957849697] 請求開始: GET /script/
[2025-07-08 14:57:29,699] DEBUG in api_logger: [1751957849697] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:57:29,702] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:57:29,703] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.31ms
[2025-07-08 14:57:29,703] INFO in api_logger: [1751957849697] 請求完成: GET /script/ - 耗時: 4.42ms
[2025-07-08 14:58:19,439] INFO in api_logger: [1751957899437] 請求開始: GET /script/info_test.py
[2025-07-08 14:58:19,439] INFO in api_logger: [1751957899437] 請求開始: GET /script/info_test.py
[2025-07-08 14:58:19,439] INFO in api_logger: [1751957899437] 請求開始: GET /script/info_test.py
[2025-07-08 14:58:19,440] DEBUG in api_logger: [1751957899437] 請求數據: {"method": "GET", "path": "/script/info_test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,440] DEBUG in api_logger: [1751957899437] 請求數據: {"method": "GET", "path": "/script/info_test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,440] DEBUG in api_logger: [1751957899437] 請求數據: {"method": "GET", "path": "/script/info_test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,441] WARNING in script_routes: 腳本文件不存在: /fake/path/info_test.py
[2025-07-08 14:58:19,441] WARNING in script_routes: 腳本文件不存在: /fake/path/info_test.py
[2025-07-08 14:58:19,441] WARNING in script_routes: 腳本文件不存在: /fake/path/info_test.py
[2025-07-08 14:58:19,442] INFO in api_logger: [1751957899437] 請求完成: GET /script/info_test.py - 耗時: 1.67ms
[2025-07-08 14:58:19,442] INFO in api_logger: [1751957899437] 請求完成: GET /script/info_test.py - 耗時: 1.67ms
[2025-07-08 14:58:19,442] INFO in api_logger: [1751957899437] 請求完成: GET /script/info_test.py - 耗時: 1.67ms
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,494] INFO in api_logger: [1751957899494] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,495] DEBUG in api_logger: [1751957899494] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,498] INFO in api_logger: [1751957899494] 請求完成: GET /script/nonexistent.py - 耗時: 3.23ms
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] INFO in api_logger: [1751957899834] 請求開始: GET /script/
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,834] DEBUG in api_logger: [1751957899834] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 1.89ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,836] INFO in api_logger: [1751957899834] 請求完成: GET /script/ - 耗時: 2.09ms
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,886] INFO in api_logger: [1751957899886] 請求開始: GET /script/
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,887] DEBUG in api_logger: [1751957899886] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,889] WARNING in script_routes: 資料庫中的腳本 test_script.py 文件不存在於磁碟: /fake/path/test_script.py
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,890] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,892] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.86ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:19,893] INFO in api_logger: [1751957899886] 請求完成: GET /script/ - 耗時: 5.18ms
[2025-07-08 14:58:56,331] INFO in api_logger: [1751957936330] 請求開始: GET /script/
[2025-07-08 14:58:56,332] DEBUG in api_logger: [1751957936330] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:58:56,336] INFO in script_routes: 成功獲取腳本列表，共 0 個腳本
[2025-07-08 14:58:56,336] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.59ms
[2025-07-08 14:58:56,337] INFO in api_logger: [1751957936330] 請求完成: GET /script/ - 耗時: 3.97ms
[2025-07-08 14:59:12,860] INFO in api_logger: [1751957952858] 請求開始: GET /script/
[2025-07-08 14:59:12,861] DEBUG in api_logger: [1751957952858] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:59:12,863] WARNING in script_routes: 資料庫中的腳本 test.py 文件不存在於磁碟: /fake/path/test.py
[2025-07-08 14:59:12,864] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 14:59:12,867] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.46ms
[2025-07-08 14:59:12,868] INFO in api_logger: [1751957952858] 請求完成: GET /script/ - 耗時: 6.91ms
[2025-07-08 14:59:12,872] INFO in api_logger: [1751957952871] 請求開始: GET /script/test.py
[2025-07-08 14:59:12,872] DEBUG in api_logger: [1751957952871] 請求數據: {"method": "GET", "path": "/script/test.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:59:12,877] WARNING in script_routes: 腳本文件不存在: /fake/path/test.py
[2025-07-08 14:59:12,877] INFO in api_logger: [1751957952871] 請求完成: GET /script/test.py - 耗時: 4.58ms
[2025-07-08 14:59:26,626] INFO in api_logger: [1751957966624] 請求開始: GET /script/nonexistent.py
[2025-07-08 14:59:26,627] DEBUG in api_logger: [1751957966624] 請求數據: {"method": "GET", "path": "/script/nonexistent.py", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 14:59:26,628] ERROR in script_routes: 獲取腳本 nonexistent.py 詳細信息失敗: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
[2025-07-08 14:59:26,629] INFO in api_logger: [1751957966624] 請求完成: GET /script/nonexistent.py - 耗時: 1.62ms
[2025-07-08 15:02:34,412] INFO in api_logger: [1751958154410] 請求開始: GET /script/
[2025-07-08 15:02:34,413] DEBUG in api_logger: [1751958154410] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:02:34,418] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:02:34,418] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 4.67ms
[2025-07-08 15:02:34,419] INFO in api_logger: [1751958154410] 請求完成: GET /script/ - 耗時: 5.09ms
[2025-07-08 15:02:34,421] INFO in api_logger: [1751958154421] 請求開始: GET /script/
[2025-07-08 15:02:34,421] DEBUG in api_logger: [1751958154421] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:02:34,428] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:02:34,429] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.71ms
[2025-07-08 15:02:34,430] INFO in api_logger: [1751958154421] 請求完成: GET /script/ - 耗時: 7.23ms
[2025-07-08 15:02:56,971] INFO in api_logger: [1751958176971] 請求開始: GET /script/
[2025-07-08 15:02:56,972] DEBUG in api_logger: [1751958176971] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 15:02:56,976] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-07-08 15:02:56,977] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.98ms
[2025-07-08 15:02:56,977] INFO in api_logger: [1751958176971] 請求完成: GET /script/ - 耗時: 4.30ms
[2025-07-08 15:03:47,312] INFO in api_logger: [1751958227310] 請求開始: GET /script/
[2025-07-08 15:03:47,312] DEBUG in api_logger: [1751958227310] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"User-Agent": "Werkzeug/2.3.7", "Host": "localhost"}, "args": {}}
[2025-07-08 15:03:47,313] WARNING in script_routes: 資料庫中的腳本 integration_manual_test.py 文件不存在於磁碟: /fake/path/test.py
[2025-07-08 15:03:47,314] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-08 15:03:47,315] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 3.06ms
[2025-07-08 15:03:47,316] INFO in api_logger: [1751958227310] 請求完成: GET /script/ - 耗時: 3.30ms
