
# 🌿 Python 腳本排程管理系統 - WEB UI 樣式描述文件 (Markdown)

## 🎨 **整體風格定位**
- **風格**：現代、清新、素雅、專業，適合企業內部管理介面。
- **基調顏色**：
  - 背景：#f7f9fc（淺藍灰）
  - 文字：#334155（深灰藍）
  - 邊框：#b5c3ce（淺灰藍）
  - hover 效果：#e2e8f0（淺灰藍 hover）

---

## 🧱 **元件設計細節**

### ✅ **按鈕（Button）**
- **外觀**：
  - 圓角 (border-radius: 8px)
  - 圖示 + 文字 (FontAwesome)
  - 平滑 hover 浮起效果 (transform: translateY(-2px))
- **分類配色**：
  - **新增/啟用**：#e0f2f1 背景，#00695c 字色（綠色系）
  - **暫停/警告**：#fff8e1 背景，#ff8f00 字色（黃色系）
  - **刪除/危險**：#ffebee 背景，#c62828 字色（紅色系）

### ✅ **徽章（Badge）**
- 狀態顯示：
  - **執行中**：#d9f99d 綠色徽章
  - **等待**：#fde68a 黃色徽章
  - **失敗**：#fecaca 紅色徽章
- 圓角、內縮文字，顯示執行狀態。

---

## 🗃 **主要表格模組**

### 📜 **腳本排程總覽**
- 欄位：
  - 腳本名稱、排程類型、條件、下一次執行、當前狀態、排程操作、任務控制
- **互動按鍵**：
  - 排程操作：啟用、修改、刪除
  - 任務控制：暫停、停止、重新執行、查看 Log
- **行 hover 效果**：
  - 行背景變色區分互動

### 🖥 **表格設計**
- **表頭**：#cbd5e1（灰藍底色）區分欄位
- **列間交錯**：淺色分隔 #f8fafc
- **hover 效果**：#e0e7ef

---

## ➕ **新增排程 / 腳本管理**

### 📅 **新增排程**
- 彈窗顯示，圓角柔邊。
- 表單欄位：
  - 選擇腳本（下拉）
  - 排程類型（一次、每日、每週、每月）
  - 排程條件（例如「每日 02:00」）

### 📂 **腳本管理**
- 彈窗新增腳本：
  - 腳本名稱（輸入框）
  - 上傳 .py 檔案

---

## ⚙ **互動與動畫**

- **按鈕** hover：浮起、背景變深。
- **表格列** hover：背景變色提示互動。
- **彈窗**：動態展開/關閉（display: flex 控制）

---

## 💡 **重現專用 Prompt 範例**

```
設計一個現代、清新、素雅的 Python 腳本排程管理介面，包含：
- 表格列出腳本排程（腳本名稱、排程類型、條件、下一次執行、狀態、操作）
- 按鈕包含圖示（FontAwesome），依功能分色（啟用-綠色、暫停-黃色、刪除-紅色）
- 狀態顯示為圓角徽章（執行中-綠、等待-黃、失敗-紅）
- 所有按鈕與表格行具 hover 浮動效果
- 彈窗管理新增排程與腳本
- 整體風格素雅、專業，適合企業內部管理工具
```
