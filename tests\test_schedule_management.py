import unittest
from datetime import datetime, timedelta
from app import create_app, db
from app.models.schedule import Schedule, ScheduleType, ScheduleStatus
from app.services.schedule_manager import ScheduleManager
from app.services.script_executor import ScriptExecutor

class TestScheduleManagement(unittest.TestCase):
    def setUp(self):
        """測試前設置"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # 初始化服務
        self.schedule_manager = ScheduleManager()
        self.schedule_manager.init_app(self.app)
        self.script_executor = ScriptExecutor()
        
    def tearDown(self):
        """測試後清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        
    def test_create_schedule(self):
        """測試建立排程"""
        # 建立測試資料
        test_data = {
            'script_name': 'test_script.py',
            'schedule_type': 'once',
            'execution_time': (datetime.now() + timedelta(hours=1)).isoformat(),
            'description': '測試排程'
        }
        
        # 建立排程
        schedule = self.schedule_manager.create_schedule(**test_data)
        
        # 驗證排程是否建立成功
        self.assertIsNotNone(schedule)
        self.assertEqual(schedule.script_name, test_data['script_name'])
        self.assertEqual(schedule.schedule_type, test_data['schedule_type'])
        self.assertEqual(schedule.description, test_data['description'])
        self.assertEqual(schedule.status, ScheduleStatus.PENDING.value)
        
    def test_get_all_schedules(self):
        """測試獲取所有排程"""
        # 建立測試資料
        test_schedules = [
            {
                'script_name': f'test_script_{i}.py',
                'schedule_type': 'once',
                'execution_time': (datetime.now() + timedelta(hours=i+1)).isoformat(),
                'description': f'測試排程 {i}'
            }
            for i in range(3)
        ]
        
        # 建立多個排程
        for schedule_data in test_schedules:
            self.schedule_manager.create_schedule(**schedule_data)
            
        # 獲取所有排程
        schedules = self.schedule_manager.get_all_schedules()
        
        # 驗證排程數量
        self.assertEqual(len(schedules), 3)
        
    def test_schedule_list_page(self):
        """測試排程列表頁面"""
        # 建立測試排程
        test_data = {
            'script_name': 'test_script.py',
            'schedule_type': 'once',
            'execution_time': (datetime.now() + timedelta(hours=1)).isoformat(),
            'description': '測試排程'
        }
        self.schedule_manager.create_schedule(**test_data)
        
        # 使用測試客戶端發送請求
        with self.app.test_client() as client:
            response = client.get('/schedule/')
            self.assertEqual(response.status_code, 200)
        
    def test_schedule_detail_page(self):
        """測試排程詳情頁面"""
        # 建立測試排程
        test_data = {
            'script_name': 'test_script.py',
            'schedule_type': 'once',
            'execution_time': (datetime.now() + timedelta(hours=1)).isoformat(),
            'description': '測試排程'
        }
        schedule = self.schedule_manager.create_schedule(**test_data)
        
        # 使用測試客戶端發送請求
        with self.app.test_client() as client:
            response = client.get(f'/schedule/{schedule.id}')
            self.assertEqual(response.status_code, 200)
        
if __name__ == '__main__':
    unittest.main() 