import logging
import signal
import sys
from app import create_app, db, socketio
from app.models import Schedule, Script, ExecutionLog
from app.services.schedule_manager import ScheduleManager
from app.services.script_executor import ScriptExecutor
from app.services.schedule_executor import ScheduleExecutor

# 設定日誌
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 建立 Flask 應用程式
app = create_app()

# 註冊信號處理器
def signal_handler(sig, frame):
    logger.info("接收到終止信號，正在關閉應用程式...")
    if 'schedule_executor' in globals():
        schedule_executor.stop()
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def init_db():
    """初始化資料庫"""
    with app.app_context():
        db.create_all()
        logger.info("資料庫初始化完成")

def init_services():
    """初始化服務"""
    global schedule_manager, script_executor, schedule_executor
    
    # 建立服務實例
    schedule_manager = app.schedule_manager
    script_executor = app.script_executor
    schedule_executor = app.schedule_executor
    
    logger.info("服務初始化完成")

if __name__ == '__main__':
    try:
        # 初始化資料庫
        init_db()
        
        # 推送應用程式上下文
        ctx = app.app_context()
        ctx.push()
        
        # 初始化服務
        init_services()
        
        # 啟動應用程式
        logger.info("啟動應用程式...")
        socketio.run(app, debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        logger.error(f"應用程式執行時發生錯誤: {str(e)}")
    finally:
        # 確保在應用程式關閉時停止排程執行器
        if 'schedule_executor' in globals():
            logger.info("正在停止排程執行器...")
            schedule_executor.stop()
        
        # 移除應用程式上下文
        ctx.pop()
        
        logger.info("應用程式已關閉")
