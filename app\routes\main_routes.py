from flask import Blueprint, render_template, current_app, redirect, url_for, jsonify, send_from_directory
from app.utils.logger import setup_logger
from app.models.schedule import Schedule
import psutil
from app.utils.api_logger import api_logger, APILogger

bp = Blueprint('main', __name__)
logger = setup_logger(__name__)

@bp.route('/favicon.ico')
def favicon():
    """返回favicon"""
    return '', 204  # 返回空內容，狀態碼204表示無內容

@bp.route('/')
def index():
    """首頁"""
    try:
        logger.info('訪問首頁')
        # 獲取排程管理器實例
        schedule_manager = current_app.schedule_manager if hasattr(current_app, 'schedule_manager') else None
        
        # 獲取所有排程
        schedules = schedule_manager.get_all_schedules() if schedule_manager else []
        
        # 渲染模板
        logger.debug('開始渲染模板')
        response = render_template('index.html', schedules=schedules)
        logger.debug('模板渲染完成')
        return response
    except Exception as e:
        logger.error(f'載入首頁失敗: {str(e)}')
        return render_template('500.html'), 500

# 系統資源 API - 新路徑
@bp.route('/schedule/api/system-resources')
@APILogger.log_request
@APILogger.monitor_api_health('系統資源監控')
def system_resources():
    """獲取系統資源使用狀況"""
    try:
        api_logger.info('開始獲取系統資源使用狀況')
        resources = None
        error_message = None
        
        # 先嘗試從服務工廠獲取
        try:
            from app.services import get_service, has_service
            if has_service('system_monitor'):
                api_logger.debug('從服務工廠獲取系統監控服務')
                system_monitor = get_service('system_monitor')
                resources = system_monitor.get_system_resources()
                api_logger.debug(f'成功獲取系統資源: {resources}')
            elif has_service('system_service'):
                api_logger.debug('從服務工廠獲取system_service')
                system_monitor = get_service('system_service')
                resources = system_monitor.get_system_resources()
            elif has_service('SystemMonitor'):
                api_logger.debug('從服務工廠獲取SystemMonitor')
                system_monitor = get_service('SystemMonitor')
                resources = system_monitor.get_system_resources()
            else:
                error_message = "系統監控服務未在服務工廠中註冊"
                api_logger.warning(error_message)
        except Exception as service_error:
            error_message = str(service_error)
            api_logger.warning(f'從服務工廠獲取系統監控服務失敗: {error_message}')
        
        # 如果服務工廠方法失敗，嘗試app上下文
        if resources is None:
            api_logger.debug('嘗試從app上下文獲取system_monitor')
            try:
                # 註意：這裡直接使用psutil而非current_app.system_monitor
                # 完全跳過current_app.system_monitor，避免再次出錯
                resources = get_resources_fallback()
                api_logger.debug(f'降級獲取系統資源成功: {resources}')
            except Exception as app_error:
                error_message = f"從app獲取system_monitor失敗: {str(app_error)}"
                api_logger.warning(error_message)
        
        # 最終保障：如果前面方法都失敗，使用psutil
        if resources is None:
            api_logger.warning('所有方法均失敗，使用psutil作為最終降級方案')
            resources = {
                'cpu_percent': psutil.cpu_percent(interval=0.1),
                'memory_percent': psutil.virtual_memory().percent,
                'schedule_count': 0,
                'max_schedules': 10,
                'note': '此為基礎降級數據，僅供參考'
            }
        
        # 無論如何，都返回成功響應
        return jsonify({
            'status': 'success',
            'data': resources
        })
    except Exception as e:
        api_logger.error(f'系統資源API完全失敗: {str(e)}，返回空資源')
        # 即使在最壞情況下也返回一些數據
        return jsonify({
            'status': 'warning',
            'message': f'發生錯誤: {str(e)}',
            'data': {
                'cpu_percent': 0,
                'memory_percent': 0,
                'schedule_count': 0,
                'max_schedules': 10,
                'error': True,
                'note': '發生錯誤，顯示預設值'
            }
        })

def get_resources_fallback():
    """使用psutil直接獲取系統資源的降級方案"""
    api_logger.debug("使用psutil降級方案獲取系統資源")
    try:
        # 獲取CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # 獲取記憶體使用率
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 嘗試獲取排程數量
        schedule_count = 0
        
        # 方式1: 嘗試從數據庫查詢
        try:
            from app.models.schedule import Schedule
            from flask import current_app
            with current_app.app_context():
                schedule_count = Schedule.query.filter_by(is_active=True).count()
                api_logger.debug(f"從數據庫獲取排程數量: {schedule_count}")
        except Exception as db_error:
            api_logger.warning(f"從數據庫獲取排程數量失敗: {str(db_error)}")
            
            # 方式2: 嘗試從schedule_manager獲取
            try:
                if hasattr(current_app, 'schedule_manager') and current_app.schedule_manager:
                    schedules = current_app.schedule_manager.get_all_schedules()
                    schedule_count = len([s for s in schedules if s.get('is_active', True)])
                    api_logger.debug(f"從schedule_manager獲取排程數量: {schedule_count}")
            except Exception as service_error:
                api_logger.warning(f"從服務獲取排程數量失敗: {str(service_error)}")
        
        # 獲取最大排程數量
        max_schedules = 10
        try:
            if hasattr(current_app, 'config'):
                max_schedules = current_app.config.get('MAX_SCHEDULES', 10)
        except Exception:
            pass
            
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory_percent,
            'schedule_count': schedule_count,
            'max_schedules': max_schedules
        }
    except Exception as e:
        api_logger.error(f"降級獲取系統資源失敗: {str(e)}")
        # 返回默認值
        return {
            'cpu_percent': 0,
            'memory_percent': 0,
            'schedule_count': 0,
            'max_schedules': 10
        }

# 系統資源 API - 舊路徑重定向
@bp.route('/api/system-resources')
@APILogger.log_request
def legacy_system_resources():
    """舊的系統資源 API 路徑 (已棄用)"""
    api_logger.warning('使用了棄用的 API 路徑: /api/system-resources，請使用 /api/v1/system/resources')
    return redirect('/api/v1/system/resources')

# API 路徑重定向 - 腳本相關
@bp.route('/api/scripts')
@APILogger.log_request
def legacy_scripts_list():
    """舊的腳本列表 API 路徑 (已棄用)"""
    api_logger.warning('使用了棄用的 API 路徑: /api/scripts，請使用 /script')
    return redirect('/script')

@bp.route('/api/scripts/upload')
@APILogger.log_request
def legacy_scripts_upload():
    """舊的腳本上傳 API 路徑 (已棄用)"""
    api_logger.warning('使用了棄用的 API 路徑: /api/scripts/upload，請使用 /script')
    return redirect('/script')

@bp.route('/api/scripts/<script_name>/info')
@APILogger.log_request
def legacy_script_info(script_name):
    """舊的腳本信息 API 路徑 (已棄用)"""
    api_logger.warning(f'使用了棄用的 API 路徑: /api/scripts/{script_name}/info，請使用 /script/{script_name}/info')
    return redirect(f'/script/{script_name}/info')

@bp.route('/api/scripts/<script_name>')
@APILogger.log_request
def legacy_script_delete(script_name):
    """舊的腳本刪除 API 路徑 (已棄用)"""
    api_logger.warning(f'使用了棄用的 API 路徑: /api/scripts/{script_name}，請使用 /script/{script_name}')
    return redirect(f'/script/{script_name}')

# API 路徑重定向 - 排程相關
@bp.route('/api/schedule')
@APILogger.log_request
def legacy_schedules_list():
    """舊的排程列表 API 路徑 (已棄用)"""
    api_logger.warning('使用了棄用的 API 路徑: /api/schedule，請使用 /schedule/api/schedule')
    return redirect('/schedule/api/schedule')

@bp.route('/api/schedule/<int:schedule_id>')
@APILogger.log_request
def legacy_schedule_detail(schedule_id):
    """舊的排程詳情 API 路徑 (已棄用)"""
    api_logger.warning(f'使用了棄用的 API 路徑: /api/schedule/{schedule_id}，請使用 /api/v1/schedules/{schedule_id}')
    return redirect(f'/api/v1/schedules/{schedule_id}')

@bp.route('/api/schedule/<int:schedule_id>/toggle')
@APILogger.log_request
def legacy_schedule_toggle(schedule_id):
    """舊的排程切換 API 路徑 (已棄用)"""
    api_logger.warning(f'使用了棄用的 API 路徑: /api/schedule/{schedule_id}/toggle，請使用 /api/v1/schedules/{schedule_id}/toggle')
    return redirect(f'/api/v1/schedules/{schedule_id}/toggle')

@bp.route('/api/schedule/<int:schedule_id>/logs')
@APILogger.log_request
def legacy_schedule_logs(schedule_id):
    """舊的排程日誌 API 路徑 (已棄用)"""
    api_logger.warning(f'使用了棄用的 API 路徑: /api/schedule/{schedule_id}/logs，請使用 /api/v1/schedules/{schedule_id}/logs')
    return redirect(f'/api/v1/schedules/{schedule_id}/logs') 