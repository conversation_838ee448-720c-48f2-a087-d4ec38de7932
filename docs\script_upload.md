# 腳本上傳功能說明

## 功能概述
腳本上傳功能提供了一個安全且便捷的方式來上傳和管理Python腳本。該功能包含完整的驗證流程、錯誤處理和用戶反饋機制。

## 系統架構

### 目錄結構
```
app/
├── core/
│   ├── routes.py          # API路由定義
│   └── config.py          # 系統配置
├── scripts/
│   ├── manager.py         # 腳本管理器
│   ├── validator.py       # 腳本驗證器
│   └── metadata.py        # 元數據管理器
├── static/
│   ├── css/
│   │   └── style.css      # 樣式文件
│   └── js/
│       └── main.js        # 前端邏輯
└── templates/
    └── index.html         # 主頁面模板
```

### 數據結構
1. 腳本元數據（JSON格式）
```json
{
    "name": "腳本名稱",
    "description": "腳本描述",
    "author": "作者",
    "tags": ["標籤1", "標籤2"],
    "created_at": "創建時間",
    "modified_at": "修改時間",
    "file_size": 文件大小,
    "file_path": "文件路徑"
}
```

2. 驗證結果（JSON格式）
```json
{
    "is_valid": true/false,
    "errors": ["錯誤信息1", "錯誤信息2"],
    "warnings": ["警告信息1", "警告信息2"]
}
```

## 核心組件

### 1. 腳本管理器（ScriptManager）
- 職責：管理腳本文件的上傳、刪除和更新
- 主要方法：
  - `upload_script()`: 處理腳本上傳
  - `delete_script()`: 刪除腳本
  - `get_script_info()`: 獲取腳本信息
  - `update_script_meta()`: 更新腳本元數據

### 2. 腳本驗證器（ScriptValidator）
- 職責：驗證腳本的安全性和語法
- 驗證項目：
  - Python語法檢查
  - 危險函數檢測
  - 文件大小限制
  - 文件類型檢查

### 3. 元數據管理器（MetadataManager）
- 職責：管理腳本的元數據
- 主要方法：
  - `add_script()`: 添加新腳本元數據
  - `update_script()`: 更新腳本元數據
  - `delete_script()`: 刪除腳本元數據
  - `get_script_meta()`: 獲取腳本元數據

## API接口

### 1. 腳本上傳
```
POST /api/scripts/upload
Content-Type: multipart/form-data
參數：
- file: 腳本文件
返回：
{
    "success": true/false,
    "message": "提示信息",
    "script_info": {
        "name": "腳本名稱",
        "description": "腳本描述"
    }
}
```

### 2. 腳本驗證
```
POST /api/scripts/validate
Content-Type: multipart/form-data
參數：
- file: 腳本文件
返回：
{
    "is_valid": true/false,
    "errors": ["錯誤信息"],
    "warnings": ["警告信息"]
}
```

### 3. 更新腳本元數據
```
PUT /api/scripts/<script_name>/meta
Content-Type: application/json
參數：
{
    "description": "新描述",
    "tags": ["新標籤1", "新標籤2"]
}
返回：
{
    "success": true/false,
    "message": "提示信息"
}
```

## 前端實現

### 1. 上傳界面（Bootstrap Modal）
```html
<div class="modal" id="uploadModal">
    <div class="modal-content">
        <div class="upload-area" id="dropZone">
            <input type="file" id="fileInput" accept=".py">
            <div class="upload-text">拖放文件或點擊選擇</div>
        </div>
        <div class="validation-result" id="validationResult"></div>
        <div class="progress-bar" id="uploadProgress"></div>
    </div>
</div>
```

### 2. 主要JavaScript函數
```javascript
// 文件上傳處理
async function handleFileUpload(file) {
    // 1. 驗證文件
    const validationResult = await validateScript(file);
    
    // 2. 顯示驗證結果
    displayValidationResult(validationResult);
    
    // 3. 如果驗證通過，上傳文件
    if (validationResult.is_valid) {
        await uploadScript(file);
    }
}

// 腳本驗證
async function validateScript(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/scripts/validate', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
}

// 上傳腳本
async function uploadScript(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/scripts/upload', {
        method: 'POST',
        body: formData
    });
    
    const result = await response.json();
    if (result.success) {
        showNotification('上傳成功', 'success');
        loadScriptsList();
    }
}
```

## 安全措施

### 1. 文件驗證
- 文件類型檢查：僅允許.py文件
- 文件大小限制：最大10MB
- 文件名安全檢查：移除特殊字符

### 2. 腳本內容檢查
- 危險函數檢測：
  - `os.system()`
  - `subprocess.call()`
  - `eval()`
  - `exec()`
- 語法檢查：使用`ast`模塊解析

### 3. 文件處理
- 使用臨時文件
- 安全的文件路徑處理
- 文件權限控制

## 錯誤處理

### 1. 前端錯誤
- 文件類型錯誤
- 文件大小超限
- 上傳失敗
- 網絡錯誤

### 2. 後端錯誤
- 文件保存失敗
- 驗證失敗
- 元數據更新失敗
- 系統錯誤

## 部署要求

### 1. 環境要求
- Python 3.8+
- Flask 2.0+
- 足夠的磁盤空間
- 適當的文件權限

### 2. 配置要求
```python
# config.py
UPLOAD_FOLDER = 'data/scripts'
MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {'py'}
```

## 測試用例

### 1. 單元測試
```python
def test_script_validation():
    # 測試腳本驗證
    pass

def test_script_upload():
    # 測試腳本上傳
    pass

def test_metadata_management():
    # 測試元數據管理
    pass
```

### 2. 集成測試
```python
def test_upload_flow():
    # 測試完整上傳流程
    pass

def test_error_handling():
    # 測試錯誤處理
    pass
```

## 維護指南

### 1. 日誌記錄
- 上傳記錄
- 驗證結果
- 錯誤信息
- 系統狀態

### 2. 備份策略
- 定期備份腳本文件
- 備份元數據
- 版本控制

### 3. 監控指標
- 上傳成功率
- 驗證失敗率
- 系統響應時間
- 存儲使用情況

## 主要特點
1. 即時驗證
2. 安全檢查
3. 自動元數據管理
4. 用戶友好的界面
5. 完整的錯誤處理

## 使用流程

### 1. 打開上傳界面
- 點擊"上傳新腳本"按鈕
- 或使用快捷鍵（如果有設置）

### 2. 選擇文件
- 點擊"選擇文件"按鈕
- 或直接拖放文件到上傳區域
- 支持的文件類型：`.py`

### 3. 驗證過程
- 自動進行語法檢查
- 檢查危險函數使用
- 驗證腳本結構
- 即時顯示驗證結果

### 4. 上傳完成
- 顯示成功提示
- 自動更新腳本列表
- 生成腳本元數據

## 安全特性
1. 文件類型限制
2. 危險函數檢查
3. 臨時文件處理
4. 錯誤處理機制

## 錯誤處理
- 文件類型錯誤
- 語法錯誤
- 危險函數警告
- 上傳失敗處理

## 元數據管理
- 自動生成腳本描述
- 記錄上傳時間
- 保存文件信息
- 支持標籤管理

## 注意事項
1. 僅支持Python文件（.py）
2. 文件大小限制
3. 需要有效的Python語法
4. 建議添加腳本描述和標籤

## 未來優化方向
1. 支持更多文件格式
2. 增強驗證規則
3. 優化用戶界面
4. 添加批量上傳功能 