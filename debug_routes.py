#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試路由註冊情況
"""

from app import create_app

def debug_routes():
    """調試路由註冊情況"""
    app = create_app()
    
    print("🔍 已註冊的路由:")
    print("=" * 50)
    
    for rule in app.url_map.iter_rules():
        methods = ','.join(rule.methods - {'HEAD', 'OPTIONS'})
        print(f"{rule.rule:<40} {methods:<15} {rule.endpoint}")
    
    print("\n🔍 查找API v1相關路由:")
    print("=" * 50)
    
    api_v1_routes = [rule for rule in app.url_map.iter_rules() if '/api/v1' in rule.rule]
    
    if api_v1_routes:
        for rule in api_v1_routes:
            methods = ','.join(rule.methods - {'HEAD', 'OPTIONS'})
            print(f"{rule.rule:<40} {methods:<15} {rule.endpoint}")
    else:
        print("❌ 沒有找到 /api/v1 路由!")
    
    print("\n🔍 查找排程相關路由:")
    print("=" * 50)
    
    schedule_routes = [rule for rule in app.url_map.iter_rules() if 'schedule' in rule.rule.lower()]
    
    for rule in schedule_routes:
        methods = ','.join(rule.methods - {'HEAD', 'OPTIONS'})
        print(f"{rule.rule:<40} {methods:<15} {rule.endpoint}")

if __name__ == "__main__":
    debug_routes()
