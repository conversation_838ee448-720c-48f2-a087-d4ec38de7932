import os
import pytest
from datetime import datetime, timedelta
from app import create_app, db
from app.models.script import Script
from app.models.schedule import Schedule, ScheduleType, ScheduleStatus

class TestIntegration:
    def setup_method(self):
        """測試前的設置"""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # 確保測試目錄存在
        os.makedirs(self.app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(os.path.join(os.path.dirname(self.app.config['UPLOAD_FOLDER']), 'test_data'), exist_ok=True)
        os.makedirs(os.path.join(os.path.dirname(self.app.config['UPLOAD_FOLDER']), 'test_outputs'), exist_ok=True)

    def teardown_method(self):
        """測試後的清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        
        # 清理測試檔案
        test_files = [
            os.path.join(self.app.config['UPLOAD_FOLDER'], 'test_flow.py'),
            os.path.join(self.app.config['UPLOAD_FOLDER'], 'test_execution.py')
        ]
        for file_path in test_files:
            if os.path.exists(file_path):
                os.remove(file_path)

    def test_script_to_schedule_flow(self):
        """測試從腳本上傳到排程建立的完整流程"""
        # 1. 上傳腳本
        test_script_content = "print('Hello, World!')"
        test_script_path = os.path.join(self.app.config['UPLOAD_FOLDER'], 'test_flow.py')
        with open(test_script_path, 'w') as f:
            f.write(test_script_content)

        with open(test_script_path, 'rb') as f:
            response = self.client.post(
                '/script',
                data={'file': (f, 'test_flow.py')},
                content_type='multipart/form-data',
                follow_redirects=True
            )

        assert response.status_code == 201
        assert response.json['status'] == 'success'
        script_name = response.json['data']['name']

        # 2. 建立排程
        schedule_data = {
            'script_name': script_name,
            'schedule_type': 'daily',
            'execution_time': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%S'),
            'description': '整合測試排程'
        }
        response = self.client.post('/schedule/api/schedule', json=schedule_data)
        assert response.status_code == 201
        assert response.json['status'] == 'success'
        schedule_id = response.json['data']['id']

        # 3. 確認排程列表
        response = self.client.get('/schedule/api/schedule')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert len(response.json['data']) == 1

        # 4. 切換排程狀態
        response = self.client.post(f'/schedule/api/schedule/{schedule_id}/toggle')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert not response.json['data']['is_active']

        # 5. 刪除排程
        response = self.client.delete(f'/schedule/api/schedule/{schedule_id}')
        assert response.status_code == 200
        assert response.json['status'] == 'success'

    def test_schedule_execution_flow(self):
        """測試排程執行流程"""
        # 1. 建立測試腳本
        test_script_content = "print('Test Execution')"
        test_script_path = os.path.join(self.app.config['UPLOAD_FOLDER'], 'test_execution.py')
        with open(test_script_path, 'w') as f:
            f.write(test_script_content)

        script = Script(
            name='test_execution.py',
            description='執行測試腳本',
            file_path=test_script_path,
            created_at=datetime.now()
        )
        db.session.add(script)
        db.session.commit()

        # 2. 建立排程
        schedule = Schedule(
            script_name='test_execution.py',
            schedule_type=ScheduleType.IMMEDIATE.value,
            status=ScheduleStatus.WAITING.value,
            description='執行測試排程',
            execution_time=datetime.now()
        )
        db.session.add(schedule)
        db.session.commit()

        # 3. 觸發排程執行
        response = self.client.post(f'/schedule/api/schedule/{schedule.id}/execute')
        assert response.status_code == 200
        assert response.json['status'] == 'success'

        # 4. 檢查執行狀態
        response = self.client.get(f'/schedule/api/schedule/{schedule.id}')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert response.json['data']['status'] in ['running', 'completed']