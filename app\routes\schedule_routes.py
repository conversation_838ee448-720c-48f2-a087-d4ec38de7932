from flask import Blueprint, jsonify, request, current_app, render_template
from datetime import datetime
import traceback
from ..services.schedule_manager import ScheduleManager
from ..models.schedule import ScheduleType, Schedule, ScheduleStatus
from app.services.schedule_executor import ScheduleExecutor
from app.services.script_executor import ScriptExecutor
from app.utils.logger import setup_logger
from .. import db
from ..models import ExecutionLog

bp = Blueprint('schedule', __name__, url_prefix='/schedule')

logger = setup_logger(__name__)

def get_schedule_manager():
    """獲取排程管理器實例"""
    if not hasattr(current_app, 'schedule_manager'):
        current_app.schedule_manager = ScheduleManager()
        current_app.schedule_manager.init_app(current_app)
    return current_app.schedule_manager

def get_script_executor():
    """獲取腳本執行器實例"""
    if not hasattr(current_app, 'script_executor'):
        current_app.script_executor = ScriptExecutor()
    return current_app.script_executor

def get_schedule_executor():
    """獲取排程執行器實例"""
    if not hasattr(current_app, 'schedule_executor'):
        current_app.schedule_executor = ScheduleExecutor(
            get_schedule_manager(),
            get_script_executor()
        )
    return current_app.schedule_executor

@bp.before_request
def before_request():
    """每個請求前初始化服務"""
    get_schedule_manager()
    get_script_executor()
    get_schedule_executor()

@bp.route('/')
def index():
    """排程管理主頁面"""
    try:
        schedule_manager = get_schedule_manager()
        schedules = schedule_manager.get_all_schedules()
        return render_template('schedules.html', schedules=schedules)
    except Exception as e:
        logger.error(f'載入排程管理頁面失敗: {str(e)}')
        return render_template('500.html'), 500

@bp.route('/<int:schedule_id>')
def schedule_detail(schedule_id):
    """排程詳情頁面"""
    try:
        schedule_manager = get_schedule_manager()
        schedule = schedule_manager.get_schedule(schedule_id)
        if not schedule:
            return render_template('404.html'), 404
        
        # 獲取執行日誌
        logs = ExecutionLog.query.filter_by(schedule_id=schedule_id).order_by(ExecutionLog.created_at.desc()).limit(50).all()
        
        return render_template('schedule_detail.html', schedule=schedule, logs=logs)
    except Exception as e:
        logger.error(f'載入排程詳情頁面失敗: {str(e)}')
        return render_template('500.html'), 500

@bp.route('/edit/<int:schedule_id>')
def schedule_edit(schedule_id):
    """排程編輯頁面"""
    try:
        schedule_manager = get_schedule_manager()
        script_executor = get_script_executor()
        
        schedule = schedule_manager.get_schedule(schedule_id)
        if not schedule:
            return render_template('404.html'), 404
        
        # 獲取可用的腳本列表
        scripts = script_executor.get_available_scripts()
        
        return render_template('schedule_edit.html', 
                             schedule=schedule, 
                             scripts=scripts,
                             schedule_types=[t.value for t in ScheduleType])
    except Exception as e:
        logger.error(f'載入排程編輯頁面失敗: {str(e)}')
        return render_template('500.html'), 500

@bp.route('/api/schedule/<int:schedule_id>/logs', methods=['GET'])
def get_schedule_logs(schedule_id):
    """獲取排程執行日誌"""
    try:
        logs = ExecutionLog.query.filter_by(schedule_id=schedule_id).order_by(ExecutionLog.created_at.desc()).limit(50).all()
        return jsonify({
            'status': 'success',
            'data': [log.to_dict() for log in logs]
        })
    except Exception as e:
        logger.error(f'獲取排程執行日誌失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/api/schedule', methods=['GET'])
def get_schedules():
    """獲取所有排程"""
    try:
        schedule_manager = get_schedule_manager()
        schedules = schedule_manager.get_all_schedules()
        return jsonify({
            'status': 'success',
            'data': [schedule.to_dict() for schedule in schedules]
        })
    except Exception as e:
        logger.error(f'獲取排程列表失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/api/schedule/<int:schedule_id>', methods=['GET'])
def get_schedule(schedule_id):
    """獲取特定排程"""
    try:
        schedule_manager = get_schedule_manager()
        schedule = schedule_manager.get_schedule(schedule_id)
        if not schedule:
            return jsonify({
                'status': 'error',
                'message': '找不到指定的排程'
            }), 404
        return jsonify({
            'status': 'success',
            'data': schedule.to_dict()
        })
    except Exception as e:
        logger.error(f'獲取排程詳情失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/api/schedule', methods=['POST'])
def create_schedule():
    """建立新排程"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '沒有提供資料'
            }), 400
            
        # 驗證必要欄位
        required_fields = ['script_name', 'schedule_type']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'status': 'error',
                    'message': f'缺少必要欄位: {field}'
                }), 400
        
        # 建立排程
        schedule_manager = get_schedule_manager()
        schedule = schedule_manager.create_schedule(
            script_name=data['script_name'],
            schedule_type=data['schedule_type'],
            execution_time=data.get('execution_time'),
            interval_minutes=data.get('interval_minutes'),
            weekdays=data.get('weekdays'),
            days_of_month=data.get('days_of_month'),
            description=data.get('description', '')
        )
        
        return jsonify({
            'status': 'success',
            'data': schedule.to_dict()
        }), 201
    except Exception as e:
        logger.error(f'建立排程失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/api/schedule/<int:schedule_id>', methods=['PUT'])
def update_schedule(schedule_id):
    """更新排程"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '沒有提供資料'
            }), 400
            
        schedule_manager = get_schedule_manager()
        schedule = schedule_manager.update_schedule(schedule_id, data)
        return jsonify({
            'status': 'success',
            'data': schedule.to_dict()
        })
    except Exception as e:
        logger.error(f'更新排程失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/api/schedule/<int:schedule_id>', methods=['DELETE'])
def delete_schedule(schedule_id):
    """刪除排程"""
    try:
        schedule_manager = get_schedule_manager()
        schedule_manager.delete_schedule(schedule_id)
        return jsonify({
            'status': 'success',
            'message': '排程已刪除'
        })
    except Exception as e:
        logger.error(f'刪除排程失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/api/schedule/<int:schedule_id>/toggle', methods=['POST'])
def toggle_schedule(schedule_id):
    """切換排程狀態"""
    try:
        schedule_manager = get_schedule_manager()
        schedule = schedule_manager.toggle_schedule(schedule_id)
        return jsonify({
            'status': 'success',
            'data': schedule.to_dict()
        })
    except Exception as e:
        logger.error(f'切換排程狀態失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/api/schedule/<int:schedule_id>/execute', methods=['POST'])
def execute_schedule(schedule_id):
    """執行排程"""
    try:
        schedule_executor = get_schedule_executor()
        success = schedule_executor.execute_schedule_now(schedule_id)
        if success:
            return jsonify({
                'status': 'success',
                'message': '排程執行已開始'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '排程執行失敗'
            }), 500
    except Exception as e:
        logger.error(f'執行排程失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/api/schedule/<int:schedule_id>/task/<action>', methods=['POST'])
def handle_task_action(schedule_id, action):
    """處理排程任務操作"""
    try:
        schedule_manager = get_schedule_manager()
        schedule = schedule_manager.get_schedule(schedule_id)
        if not schedule:
            return jsonify({'error': '排程不存在'}), 404

        schedule_executor = get_schedule_executor()
        if action == 'pause':
            schedule_executor.pause_task(schedule_id)
        elif action == 'resume':
            schedule_executor.resume_task(schedule_id)
        elif action == 'stop':
            schedule_executor.stop_task(schedule_id)
        elif action == 'retry':
            schedule_executor.retry_task(schedule_id)
        else:
            return jsonify({'error': '不支援的操作'}), 400

        return jsonify({'message': '操作成功'})
    except Exception as e:
        logger.error(f"處理排程任務操作失敗: {str(e)}")
        return jsonify({'error': '操作失敗'}), 500

@bp.route('/api/schedule/<int:schedule_id>/output-files', methods=['GET'])
def get_output_files(schedule_id):
    """獲取排程輸出檔案列表"""
    try:
        files = get_schedule_manager().get_schedule_output_files(schedule_id)
        return jsonify(files)
    except Exception as e:
        logger.error(f"獲取輸出檔案列表失敗: {str(e)}")
        return jsonify({'error': '獲取輸出檔案列表失敗'}), 500

@bp.route('/api/schedule/<int:schedule_id>/output-files/<filename>', methods=['GET'])
def get_output_file(schedule_id, filename):
    """獲取特定輸出檔案內容"""
    try:
        file_content = get_schedule_manager().get_schedule_output_file(schedule_id, filename)
        return jsonify(file_content)
    except Exception as e:
        logger.error(f"獲取輸出檔案內容失敗: {str(e)}")
        return jsonify({'error': '獲取輸出檔案內容失敗'}), 500

@bp.route('/api/schedule/<int:schedule_id>/output-files/<filename>/download', methods=['GET'])
def download_output_file(schedule_id, filename):
    """下載輸出檔案"""
    try:
        return get_schedule_manager().download_schedule_output_file(schedule_id, filename)
    except Exception as e:
        logger.error(f"下載輸出檔案失敗: {str(e)}")
        return jsonify({'error': '下載輸出檔案失敗'}), 500

@bp.route('/<int:schedule_id>/logs', methods=['GET'])
def get_execution_logs(schedule_id):
    """取得排程執行記錄"""
    logs = get_schedule_manager().get_execution_logs(schedule_id)
    return jsonify([log.to_dict() for log in logs])

@bp.route('/<int:schedule_id>/status', methods=['PUT'])
def update_schedule_status(schedule_id):
    """更新排程狀態"""
    data = request.get_json()
    status = data.get('status')
    error_message = data.get('error_message')
    
    if not status:
        return jsonify({'error': '必須提供狀態'}), 400
        
    schedule = get_schedule_manager().update_schedule_status(schedule_id, status, error_message)
    if not schedule:
        return jsonify({'error': '找不到指定的排程'}), 404
    return jsonify(schedule.to_dict())

@bp.route('/<int:schedule_id>/status', methods=['PUT'])
def update_schedule_status_db(schedule_id):
    """更新排程狀態"""
    schedule = Schedule.query.get_or_404(schedule_id)
    data = request.get_json()
    if not data or 'status' not in data:
        return jsonify({'error': '沒有提供狀態'}), 400
        
    try:
        schedule.status = ScheduleStatus(data['status'])
        db.session.commit()
        return jsonify(schedule.to_dict())
    except ValueError:
        return jsonify({'error': '無效的狀態'}), 400

# 系統資源監控 API (已移至 system_routes.py，此處保留作為備用)
# @bp.route('/api/api/v1/system/resources', methods=['GET'])
# def get_system_resources():
    # """
    # 提供系統資源使用狀況的 API 端點
    #
    # Returns:
    #     JSON: 包含 CPU、記憶體使用率與當前排程數
    # """
    # try:
    #     # 在實際應用中，這些數據應該從系統中獲取
    #     # 這裡使用模擬數據
    #     import random
    #     import psutil
    #
    #     try:
    #         # 嘗試使用 psutil 獲取真實資源使用率
    #         cpu_percent = psutil.cpu_percent(interval=0.5)
    #         memory_percent = psutil.virtual_memory().percent
    #     except:
    #         # 若失敗則使用模擬數據
    #         cpu_percent = random.randint(10, 70)
    #         memory_percent = random.randint(20, 60)
    #
    #     # 獲取當前排程數量
    #     schedule_manager = get_schedule_manager()
    #     active_schedules = len(schedule_manager.get_active_schedules())
    #     max_schedules = schedule_manager.get_max_schedules()
    #
    #     response = {
    #         'status': 'success',
    #         'data': {
    #             'cpu': cpu_percent,
    #             'memory': memory_percent,
    #             'schedules': {
    #                 'current': active_schedules,
    #                 'max': max_schedules
    #             }
    #         }
    #     }
    #
    #     return jsonify(response)
    # except Exception as e:
    #     logger.error(f"獲取系統資源信息時發生錯誤: {str(e)}")
    #     return jsonify({
    #         'status': 'error',
    #         'message': '無法獲取系統資源信息'
    #     }), 500
pass  # 佔位符，因為函數被註釋了

@bp.route('/list', methods=['GET'])
def list_schedules():
    """獲取所有排程列表（已棄用）"""
    return jsonify({
        'status': 'error',
        'message': '此端點已棄用，請使用 /api/schedule'
    }), 410

@bp.route('/remove/<schedule_id>', methods=['DELETE'])
def remove_schedule(schedule_id):
    """移除排程（已棄用）"""
    return jsonify({
        'status': 'error',
        'message': '此端點已棄用，請使用 DELETE /api/schedule/<schedule_id>'
    }), 410

@bp.route('/add')
def add_schedule():
    """添加排程頁面"""
    try:
        # 獲取腳本列表，使用多種方式確保獲取成功
        scripts = []
        
        # 方式1：使用資料庫獲取
        try:
            from ..models.script import Script
            db_scripts = Script.query.all()
            if db_scripts:
                scripts = [script.to_dict() for script in db_scripts]
                logger.debug(f"從資料庫中獲取到 {len(scripts)} 個腳本")
        except Exception as db_error:
            logger.error(f"從資料庫獲取腳本失敗: {str(db_error)}")
        
        # 方式2：若資料庫獲取失敗，嘗試使用腳本服務
        if not scripts:
            try:
                from app.services import get_service, has_service
                
                script_service = None
                if has_service('script_service'):
                    script_service = get_service('script_service')
                elif has_service('script_executor'):
                    script_service = get_service('script_executor')
                    
                if script_service and hasattr(script_service, 'get_all_scripts'):
                    scripts = script_service.get_all_scripts()
                    logger.debug(f"從腳本服務中獲取到 {len(scripts)} 個腳本")
            except Exception as service_error:
                logger.error(f"從服務獲取腳本失敗: {str(service_error)}")
        
        # 方式3：直接從文件系統獲取
        if not scripts:
            try:
                import os
                from werkzeug.utils import secure_filename
                
                upload_folder = current_app.config.get('UPLOAD_FOLDER')
                if os.path.exists(upload_folder):
                    for filename in os.listdir(upload_folder):
                        if filename.endswith('.py'):
                            scripts.append({
                                'id': filename.replace('.py', ''),
                                'name': filename,
                                'description': f'自動掃描發現的腳本: {filename}',
                                'path': os.path.join(upload_folder, filename)
                            })
                    logger.debug(f"從文件系統中掃描到 {len(scripts)} 個腳本")
            except Exception as fs_error:
                logger.error(f"從文件系統掃描腳本失敗: {str(fs_error)}")
        
        logger.debug(f"最終獲取到 {len(scripts)} 個腳本")
        
        # 返回資料
        return render_template('schedule/add.html', scripts=scripts)
    except Exception as e:
        logger.error(f"訪問添加排程頁面失敗: {str(e)}")
        return render_template('500.html'), 500 