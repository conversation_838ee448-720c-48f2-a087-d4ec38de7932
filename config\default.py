# -*- coding: utf-8 -*-
import os

class DefaultConfig:
    """默認配置基類"""
    
    # 基本路徑設定
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    APP_DIR = os.path.join(BASE_DIR, 'app')
    DATA_DIR = os.path.join(APP_DIR, 'data')
    
    # 數據目錄設定
    UPLOAD_FOLDER = os.path.join(DATA_DIR, 'uploads')
    OUTPUT_FOLDER = os.path.join(DATA_DIR, 'outputs')
    LOG_DIR = os.path.join(BASE_DIR, 'logs')
    
    # 資料庫設定
    SQLALCHEMY_DATABASE_URI = 'sqlite:///' + os.path.join(BASE_DIR, 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 安全設定
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'default-dev-key'
    
    # 上傳設定
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 MB
    ALLOWED_EXTENSIONS = {'py'}
    
    # 執行設定
    SCHEDULE_TIMEOUT = 3600  # 1 小時
    MAX_SCHEDULES = 10
    
    @classmethod
    def init_app(cls, app):
        """初始化應用程式配置"""
        pass 