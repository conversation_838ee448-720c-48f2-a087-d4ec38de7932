# -*- coding: utf-8 -*-
"""
腳本API路由模組 v1
"""
from flask import jsonify, request, current_app
from app.api.v1 import api_v1
from app.services import get_service
from app.utils.api_logger import APILogger, api_logger
import os
from werkzeug.utils import secure_filename

# 通用獲取script_service的方法
def _get_script_service():
    """通用方法：獲取腳本服務"""
    script_service = None
    error_message = None
    
    # 方式1: 嘗試從服務工廠獲取
    try:
        from app.services import get_service, has_service
        if has_service('script_service'):
            api_logger.debug("使用script_service獲取腳本服務")
            script_service = get_service('script_service')
        elif has_service('script_executor'):
            api_logger.debug("使用script_executor獲取腳本服務")
            script_service = get_service('script_executor')
        else:
            error_message = "找不到script_service或script_executor"
            api_logger.warning(error_message)
    except Exception as e:
        error_message = f"從服務工廠獲取失敗: {str(e)}"
        api_logger.warning(error_message)
    
    # 方式2: 嘗試從app上下文獲取
    if script_service is None:
        try:
            if hasattr(current_app, 'script_executor') and current_app.script_executor:
                api_logger.debug("從current_app.script_executor獲取腳本服務")
                script_service = current_app.script_executor
            else:
                api_logger.warning("current_app.script_executor不存在")
        except Exception as e:
            error_message = f"從app上下文獲取失敗: {str(e)}"
            api_logger.warning(error_message)
    
    if script_service is None:
        api_logger.error(f"無法獲取腳本服務: {error_message}")
        raise ValueError(f"無法獲取腳本服務: {error_message}")
    
    return script_service

@api_v1.route('/scripts', methods=['GET'])
@APILogger.log_request
@APILogger.monitor_api_health("腳本列表")
def get_scripts():
    """取得所有腳本"""
    try:
        api_logger.info("獲取腳本列表")
        
        # 獲取腳本服務
        script_service = _get_script_service()
        
        # 獲取腳本列表
        scripts = script_service.get_all_scripts()
        api_logger.info(f"成功獲取腳本列表，共 {len(scripts)} 個腳本")
        
        return jsonify({
            'status': 'success',
            'data': scripts
        })
    except Exception as e:
        current_app.logger.error(f"獲取腳本失敗: {str(e)}")
        api_logger.error(f"腳本列表API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"獲取腳本失敗: {str(e)}"
        }), 500

@api_v1.route('/scripts/upload', methods=['POST'])
@APILogger.log_request
@APILogger.monitor_api_health("腳本上傳")
def upload_script():
    """上傳新腳本"""
    try:
        api_logger.info("開始處理腳本上傳請求")
        # 檢查是否有文件
        if 'file' not in request.files:
            api_logger.warning("上傳請求中未找到文件")
            return jsonify({
                'status': 'error',
                'message': '未找到上傳文件'
            }), 400
        
        file = request.files['file']
        
        # 檢查文件名是否為空
        if file.filename == '':
            api_logger.warning("上傳的文件名為空")
            return jsonify({
                'status': 'error',
                'message': '未選擇文件'
            }), 400
        
        # 檢查文件擴展名
        if not file.filename.endswith('.py'):
            api_logger.warning(f"上傳的文件類型不支持: {file.filename}")
            return jsonify({
                'status': 'error',
                'message': '僅支持.py文件'
            }), 400
        
        # 安全地獲取文件名
        filename = secure_filename(file.filename)
        api_logger.debug(f"處理上傳文件: {filename}")
        
        # 獲取表單數據
        name = request.form.get('name', filename)
        description = request.form.get('description', '')
        tags = request.form.get('tags', '')
        
        # 使用服務存儲腳本
        script_service = _get_script_service()
        
        script = script_service.create_script(
            file=file,
            name=name,
            description=description,
            tags=tags.split(',') if tags else []
        )
        
        api_logger.info(f"腳本上傳成功: {name}")
        return jsonify({
            'status': 'success',
            'message': '腳本上傳成功',
            'data': script
        }), 201
    except Exception as e:
        current_app.logger.error(f"上傳腳本失敗: {str(e)}")
        api_logger.error(f"腳本上傳API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"上傳腳本失敗: {str(e)}"
        }), 500

@api_v1.route('/scripts/<script_id>', methods=['GET'])
@APILogger.log_request
@APILogger.monitor_api_health("獲取腳本詳情")
def get_script(script_id):
    """取得特定腳本"""
    try:
        api_logger.info(f"獲取腳本詳情: {script_id}")
        script_service = _get_script_service()
        script = script_service.get_script_by_id(script_id)
        
        if not script:
            api_logger.warning(f"腳本未找到: {script_id}")
            return jsonify({
                'status': 'error',
                'message': f'腳本ID {script_id} 不存在'
            }), 404
        
        return jsonify({
            'status': 'success',
            'data': script
        })
    except Exception as e:
        current_app.logger.error(f"獲取腳本 {script_id} 失敗: {str(e)}")
        api_logger.error(f"獲取腳本詳情API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"獲取腳本失敗: {str(e)}"
        }), 500

@api_v1.route('/scripts/<script_id>', methods=['PUT'])
@APILogger.log_request
@APILogger.monitor_api_health("更新腳本")
def update_script(script_id):
    """更新腳本信息"""
    try:
        api_logger.info(f"更新腳本: {script_id}")
        data = request.get_json()
        if not data:
            api_logger.warning("更新腳本請求缺少數據")
            return jsonify({
                'status': 'error',
                'message': '無效的請求數據'
            }), 400
        
        script_service = _get_script_service()
        script = script_service.get_script_by_id(script_id)
        
        if not script:
            api_logger.warning(f"更新的腳本不存在: {script_id}")
            return jsonify({
                'status': 'error',
                'message': f'腳本ID {script_id} 不存在'
            }), 404
        
        # 更新腳本
        updated_script = script_service.update_script(
            script_id=script_id,
            name=data.get('name'),
            description=data.get('description'),
            tags=data.get('tags')
        )
        
        api_logger.info(f"腳本更新成功: {script_id}")
        return jsonify({
            'status': 'success',
            'message': '腳本信息更新成功',
            'data': updated_script
        })
    except Exception as e:
        current_app.logger.error(f"更新腳本 {script_id} 失敗: {str(e)}")
        api_logger.error(f"更新腳本API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"更新腳本失敗: {str(e)}"
        }), 500

@api_v1.route('/scripts/<script_id>', methods=['DELETE'])
@APILogger.log_request
@APILogger.monitor_api_health("刪除腳本")
def delete_script(script_id):
    """刪除腳本"""
    try:
        api_logger.info(f"刪除腳本: {script_id}")
        script_service = _get_script_service()
        script = script_service.get_script_by_id(script_id)
        
        if not script:
            api_logger.warning(f"要刪除的腳本不存在: {script_id}")
            return jsonify({
                'status': 'error',
                'message': f'腳本ID {script_id} 不存在'
            }), 404
        
        script_service.delete_script(script_id)
        
        api_logger.info(f"腳本刪除成功: {script_id}")
        return jsonify({
            'status': 'success',
            'message': '腳本刪除成功'
        })
    except Exception as e:
        current_app.logger.error(f"刪除腳本 {script_id} 失敗: {str(e)}")
        api_logger.error(f"刪除腳本API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"刪除腳本失敗: {str(e)}"
        }), 500

@api_v1.route('/scripts/<script_id>/execute', methods=['POST'])
@APILogger.log_request
@APILogger.monitor_api_health("執行腳本")
def execute_script(script_id):
    """執行腳本"""
    try:
        api_logger.info(f"執行腳本: {script_id}")
        script_service = _get_script_service()
        script = script_service.get_script_by_id(script_id)
        
        if not script:
            api_logger.warning(f"要執行的腳本不存在: {script_id}")
            return jsonify({
                'status': 'error',
                'message': f'腳本ID {script_id} 不存在'
            }), 404
        
        # 獲取執行參數（可選）
        params = request.get_json() or {}
        
        # 為了兼容性，嘗試直接使用script_service執行腳本，如果失敗則使用execution_service
        try:
            api_logger.debug(f"嘗試使用script_service直接執行腳本: {script_id}")
            execution_result = script_service.execute_script(script_id, params)
        except (AttributeError, NotImplementedError) as e:
            # 如果script_service沒有execute_script方法，嘗試獲取execution_service
            api_logger.debug(f"使用script_service執行失敗: {str(e)}，嘗試execution_service")
            try:
                from app.services import get_service, has_service
                if has_service('execution_service'):
                    execution_service = get_service('execution_service')
                elif has_service('schedule_executor'):
                    execution_service = get_service('schedule_executor')
                else:
                    raise ValueError("找不到執行腳本的服務")
                
                execution_result = execution_service.execute_script(script_id, params)
            except Exception as exec_error:
                api_logger.error(f"使用execution_service執行失敗: {str(exec_error)}")
                raise
        
        api_logger.info(f"腳本執行成功: {script_id}")
        return jsonify({
            'status': 'success',
            'message': '腳本執行成功',
            'data': execution_result
        })
    except Exception as e:
        current_app.logger.error(f"執行腳本 {script_id} 失敗: {str(e)}")
        api_logger.error(f"執行腳本API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"執行腳本失敗: {str(e)}"
        }), 500 