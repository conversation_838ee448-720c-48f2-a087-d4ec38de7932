<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% endblock %} - Python 腳本排程與執行管理</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .status-badge {
            padding: 0.25em 0.6em;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status-pending { background-color: #ffd700; color: #000; }
        .status-running { background-color: #4CAF50; color: white; }
        .status-completed { background-color: #2196F3; color: white; }
        .status-failed { background-color: #f44336; color: white; }
        
        .system-resources {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
        }
        
        .progress {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">Python 腳本排程與執行管理</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/schedule">排程管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/script">腳本管理</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要內容 -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 系統資源監控 -->
    <div class="container system-resources">
        <h5>系統資源使用狀況</h5>
        <div class="row">
            <div class="col-md-4">
                <label>CPU 使用率</label>
                <div class="progress">
                    <div id="cpu-usage" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="col-md-4">
                <label>記憶體使用率</label>
                <div class="progress">
                    <div id="memory-usage" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="col-md-4">
                <label>排程使用量</label>
                <div class="progress">
                    <div id="schedule-count" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Scripts -->
    {% block scripts %}{% endblock %}
</body>
</html> 