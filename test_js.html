<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript 測試頁面</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>JavaScript 測試頁面</h1>
        <div id="test-results"></div>
        <button id="test-btn" class="btn btn-primary">測試 JavaScript 函數</button>
    </div>

    <!-- Bootstrap Bundle with <PERSON><PERSON> -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JavaScript -->
    <script src="app/static/js/main.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('測試頁面載入完成');
            
            const testBtn = document.getElementById('test-btn');
            const testResults = document.getElementById('test-results');
            
            testBtn.addEventListener('click', function() {
                console.log('開始測試 JavaScript 函數');
                
                let results = '<div class="mt-3">';
                
                // 測試 showNotification 函數
                try {
                    if (typeof showNotification === 'function') {
                        showNotification('測試', '這是一個測試通知', 'success');
                        results += '<div class="alert alert-success">✓ showNotification 函數正常</div>';
                    } else {
                        results += '<div class="alert alert-danger">✗ showNotification 函數未定義</div>';
                    }
                } catch (error) {
                    results += `<div class="alert alert-danger">✗ showNotification 錯誤: ${error.message}</div>`;
                }
                
                // 測試 updateSystemResources 函數
                try {
                    if (typeof updateSystemResources === 'function') {
                        results += '<div class="alert alert-success">✓ updateSystemResources 函數已定義</div>';
                    } else {
                        results += '<div class="alert alert-danger">✗ updateSystemResources 函數未定義</div>';
                    }
                } catch (error) {
                    results += `<div class="alert alert-danger">✗ updateSystemResources 錯誤: ${error.message}</div>`;
                }
                
                // 測試 loadScriptsList 函數
                try {
                    if (typeof loadScriptsList === 'function') {
                        results += '<div class="alert alert-success">✓ loadScriptsList 函數已定義</div>';
                    } else {
                        results += '<div class="alert alert-danger">✗ loadScriptsList 函數未定義</div>';
                    }
                } catch (error) {
                    results += `<div class="alert alert-danger">✗ loadScriptsList 錯誤: ${error.message}</div>`;
                }
                
                results += '</div>';
                testResults.innerHTML = results;
            });
        });
    </script>
</body>
</html>
