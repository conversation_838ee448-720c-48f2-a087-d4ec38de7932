#!/usr/bin/env python3
"""
簡單的伺服器測試腳本
"""
import requests
import sys
import time

def test_server():
    """測試伺服器連接"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 測試伺服器連接...", flush=True)
    print(f"📡 目標URL: {base_url}", flush=True)

    try:
        # 測試主頁
        print("\n1. 測試主頁...", flush=True)
        response = requests.get(f"{base_url}/", timeout=10)
        print(f"✅ 狀態碼: {response.status_code}", flush=True)
        print(f"✅ 內容長度: {len(response.text)} 字符", flush=True)
        print(f"✅ 內容類型: {response.headers.get('content-type', 'unknown')}", flush=True)
        print(f"✅ 前100字符: {response.text[:100]}", flush=True)

        # 測試API
        print("\n2. 測試排程API...", flush=True)
        response = requests.get(f"{base_url}/api/v1/schedules", timeout=10)
        print(f"✅ API狀態碼: {response.status_code}", flush=True)
        print(f"✅ API響應: {response.text[:200]}", flush=True)

        # 測試系統資源API
        print("\n3. 測試系統資源API...", flush=True)
        response = requests.get(f"{base_url}/api/v1/system/resources", timeout=10)
        print(f"✅ 系統資源狀態碼: {response.status_code}", flush=True)
        print(f"✅ 系統資源響應: {response.text[:200]}", flush=True)

        print("\n🎉 所有測試通過！伺服器正常運行。", flush=True)
        return True
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 連接錯誤: {e}")
        print("💡 可能原因:")
        print("   - 伺服器未啟動")
        print("   - 防火牆阻擋")
        print("   - 端口被佔用")
        return False
        
    except requests.exceptions.Timeout as e:
        print(f"❌ 超時錯誤: {e}")
        print("💡 伺服器響應太慢")
        return False
        
    except Exception as e:
        print(f"❌ 未知錯誤: {e}")
        return False

if __name__ == "__main__":
    import sys
    sys.stdout.flush()
    print("🚀 開始伺服器測試...", flush=True)
    success = test_server()

    if success:
        print("\n✅ 測試完成 - 伺服器正常", flush=True)
        sys.exit(0)
    else:
        print("\n❌ 測試失敗 - 伺服器有問題", flush=True)
        sys.exit(1)
