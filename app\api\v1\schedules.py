# -*- coding: utf-8 -*-
"""
排程API路由模組 v1
"""
from flask import jsonify, request, current_app
from app.api.v1 import api_v1
from app.services import get_service
from app.utils.api_logger import APILogger, api_logger

@api_v1.route('/schedules', methods=['GET'])
@APILogger.log_request
@APILogger.monitor_api_health("獲取排程列表")
def get_schedules():
    """取得所有排程"""
    try:
        api_logger.info("獲取排程列表")
        schedule_service = get_service('schedule_service')
        schedules = schedule_service.get_all_schedules()

        # 將 Schedule 對象轉換為字典
        schedules_data = [schedule.to_dict() for schedule in schedules]

        return jsonify({
            'status': 'success',
            'data': schedules_data
        })
    except Exception as e:
        current_app.logger.error(f"獲取排程失敗: {str(e)}")
        api_logger.error(f"獲取排程列表API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"獲取排程失敗: {str(e)}"
        }), 500

@api_v1.route('/schedules', methods=['POST'])
@APILogger.log_request
@APILogger.monitor_api_health("創建排程")
def create_schedule():
    """建立新排程"""
    try:
        api_logger.info("開始創建新排程")
        data = request.get_json()
        if not data:
            api_logger.warning("創建排程請求缺少數據")
            return jsonify({
                'status': 'error',
                'message': '無效的請求數據'
            }), 400
        
        # 驗證必要欄位
        required_fields = ['script_id', 'name', 'cron_expression']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            api_logger.warning(f"創建排程請求缺少必要欄位: {missing_fields}")
            return jsonify({
                'status': 'error',
                'message': f'缺少必要欄位: {", ".join(missing_fields)}'
            }), 400
        
        api_logger.debug(f"創建排程請求參數: script_id={data['script_id']}, name={data['name']}")
        schedule_service = get_service('schedule_service')
        schedule = schedule_service.create_schedule(
            script_id=data['script_id'],
            name=data['name'],
            cron_expression=data['cron_expression'],
            description=data.get('description', ''),
            is_active=data.get('is_active', True)
        )
        
        api_logger.info(f"排程創建成功: {schedule.get('id', None)}")
        return jsonify({
            'status': 'success',
            'message': '排程建立成功',
            'data': schedule
        }), 201
    except Exception as e:
        current_app.logger.error(f"建立排程失敗: {str(e)}")
        api_logger.error(f"創建排程API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"建立排程失敗: {str(e)}"
        }), 500

@api_v1.route('/schedules/<schedule_id>', methods=['GET'])
@APILogger.log_request
@APILogger.monitor_api_health("獲取排程詳情")
def get_schedule(schedule_id):
    """取得特定排程"""
    try:
        api_logger.info(f"獲取排程詳情: {schedule_id}")
        schedule_service = get_service('schedule_service')
        schedule = schedule_service.get_schedule_by_id(schedule_id)
        
        if not schedule:
            api_logger.warning(f"排程未找到: {schedule_id}")
            return jsonify({
                'status': 'error',
                'message': f'排程ID {schedule_id} 不存在'
            }), 404
        
        return jsonify({
            'status': 'success',
            'data': schedule
        })
    except Exception as e:
        current_app.logger.error(f"獲取排程 {schedule_id} 失敗: {str(e)}")
        api_logger.error(f"獲取排程詳情API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"獲取排程失敗: {str(e)}"
        }), 500

@api_v1.route('/schedules/<schedule_id>', methods=['PUT'])
@APILogger.log_request
@APILogger.monitor_api_health("更新排程")
def update_schedule(schedule_id):
    """更新排程"""
    try:
        api_logger.info(f"更新排程: {schedule_id}")
        data = request.get_json()
        if not data:
            api_logger.warning("更新排程請求缺少數據")
            return jsonify({
                'status': 'error',
                'message': '無效的請求數據'
            }), 400
        
        schedule_service = get_service('schedule_service')
        schedule = schedule_service.get_schedule_by_id(schedule_id)
        
        if not schedule:
            api_logger.warning(f"更新的排程不存在: {schedule_id}")
            return jsonify({
                'status': 'error',
                'message': f'排程ID {schedule_id} 不存在'
            }), 404
        
        # 更新排程
        api_logger.debug(f"更新排程參數: {data}")
        updated_schedule = schedule_service.update_schedule(
            schedule_id=schedule_id,
            name=data.get('name'),
            cron_expression=data.get('cron_expression'),
            description=data.get('description'),
            is_active=data.get('is_active')
        )
        
        api_logger.info(f"排程更新成功: {schedule_id}")
        return jsonify({
            'status': 'success',
            'message': '排程更新成功',
            'data': updated_schedule
        })
    except Exception as e:
        current_app.logger.error(f"更新排程 {schedule_id} 失敗: {str(e)}")
        api_logger.error(f"更新排程API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"更新排程失敗: {str(e)}"
        }), 500

@api_v1.route('/schedules/<schedule_id>', methods=['DELETE'])
@APILogger.log_request
@APILogger.monitor_api_health("刪除排程")
def delete_schedule(schedule_id):
    """刪除排程"""
    try:
        # 轉換為整數
        schedule_id = int(schedule_id)
        api_logger.info(f"刪除排程: {schedule_id}")

        schedule_service = get_service('schedule_service')
        schedule = schedule_service.get_schedule_by_id(schedule_id)

        if not schedule:
            api_logger.warning(f"要刪除的排程不存在: {schedule_id}")
            return jsonify({
                'status': 'error',
                'message': f'排程ID {schedule_id} 不存在'
            }), 404

        # 執行刪除操作
        success = schedule_service.delete_schedule(schedule_id)

        if success:
            api_logger.info(f"排程刪除成功: {schedule_id}")
            return jsonify({
                'status': 'success',
                'message': '排程刪除成功'
            })
        else:
            api_logger.error(f"排程刪除失敗: {schedule_id}")
            return jsonify({
                'status': 'error',
                'message': '排程刪除失敗'
            }), 500

    except ValueError as e:
        api_logger.error(f"無效的排程ID: {schedule_id}")
        return jsonify({
            'status': 'error',
            'message': f'無效的排程ID: {schedule_id}'
        }), 400
    except Exception as e:
        current_app.logger.error(f"刪除排程 {schedule_id} 失敗: {str(e)}")
        api_logger.error(f"刪除排程API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"刪除排程失敗: {str(e)}"
        }), 500

@api_v1.route('/schedules/<schedule_id>/toggle', methods=['POST'])
@APILogger.log_request
@APILogger.monitor_api_health("切換排程狀態")
def toggle_schedule_status(schedule_id):
    """切換排程狀態"""
    try:
        api_logger.info(f"切換排程狀態: {schedule_id}")
        schedule_service = get_service('schedule_service')
        schedule = schedule_service.get_schedule_by_id(schedule_id)
        
        if not schedule:
            api_logger.warning(f"要切換狀態的排程不存在: {schedule_id}")
            return jsonify({
                'status': 'error',
                'message': f'排程ID {schedule_id} 不存在'
            }), 404
        
        # 切換狀態
        current_status = schedule.get('is_active', False)
        new_status = not current_status
        api_logger.debug(f"將排程 {schedule_id} 狀態從 {current_status} 切換為 {new_status}")
        
        updated_schedule = schedule_service.update_schedule(
            schedule_id=schedule_id,
            is_active=new_status
        )
        
        status_text = "啟用" if updated_schedule.get('is_active', False) else "停用"
        api_logger.info(f"排程 {schedule_id} 已{status_text}")
        
        return jsonify({
            'status': 'success',
            'message': f'排程已{status_text}',
            'data': updated_schedule
        })
    except Exception as e:
        current_app.logger.error(f"切換排程 {schedule_id} 狀態失敗: {str(e)}")
        api_logger.error(f"切換排程狀態API失敗: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"切換排程狀態失敗: {str(e)}"
        }), 500 