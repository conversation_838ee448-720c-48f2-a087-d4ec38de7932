#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腳本服務層單元測試
"""
import os
import unittest
import tempfile
import shutil
from datetime import datetime
from app import create_app, db
from app.models import Script
from app.services.script_executor import ScriptExecutor

class TestScriptServices(unittest.TestCase):
    """腳本服務層測試類"""
    
    def setUp(self):
        """測試前準備"""
        # 建立測試用應用程式
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 建立測試資料庫
        db.create_all()
        
        # 創建臨時目錄
        self.temp_dir = tempfile.mkdtemp()
        self.scripts_dir = os.path.join(self.temp_dir, 'scripts')
        self.outputs_dir = os.path.join(self.temp_dir, 'outputs')
        os.makedirs(self.scripts_dir, exist_ok=True)
        os.makedirs(self.outputs_dir, exist_ok=True)
        
        # 更新應用配置
        self.app.config['UPLOAD_FOLDER'] = self.scripts_dir
        self.app.config['OUTPUT_FOLDER'] = self.outputs_dir
        
        # 創建腳本執行器
        self.script_executor = ScriptExecutor(self.app)
        
        # 創建測試腳本文件
        self.test_script_content = '''#!/usr/bin/env python3
import time
from datetime import datetime

def main():
    print(f"測試腳本開始執行 - {datetime.now()}")
    time.sleep(0.1)  # 短暫延遲
    print("測試腳本執行完成")
    return True

if __name__ == "__main__":
    result = main()
    print(f"執行結果: {result}")
'''
        
        self.test_script_path = os.path.join(self.scripts_dir, 'test_script.py')
        with open(self.test_script_path, 'w', encoding='utf-8') as f:
            f.write(self.test_script_content)
    
    def tearDown(self):
        """測試後清理"""
        # 清理臨時目錄
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        # 刪除測試資料庫
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_script_executor_initialization(self):
        """測試腳本執行器初始化"""
        self.assertIsNotNone(self.script_executor)
        self.assertEqual(self.script_executor.scripts_dir, self.scripts_dir)
        self.assertEqual(self.script_executor.outputs_dir, self.outputs_dir)
        self.assertTrue(os.path.exists(self.script_executor.scripts_dir))
        self.assertTrue(os.path.exists(self.script_executor.outputs_dir))
    
    def test_get_all_scripts(self):
        """測試獲取所有腳本列表"""
        # 獲取所有腳本
        scripts = self.script_executor.get_all_scripts()

        # 驗證結果
        self.assertIsInstance(scripts, list)
        self.assertTrue(len(scripts) > 0)

        # 檢查腳本信息
        script_names = [s['name'] for s in scripts]
        self.assertIn('test_script.py', script_names)
    
    def test_validate_script(self):
        """測試腳本驗證"""
        # 測試有效腳本
        result = self.script_executor._validate_script(self.test_script_path)
        self.assertTrue(result['valid'])

        # 創建無效腳本
        invalid_script_path = os.path.join(self.scripts_dir, 'invalid_script.py')
        with open(invalid_script_path, 'w', encoding='utf-8') as f:
            f.write('if True\n    print("語法錯誤")')  # 故意的語法錯誤

        # 測試無效腳本
        result = self.script_executor._validate_script(invalid_script_path)
        self.assertFalse(result['valid'])
    
    def test_script_file_operations(self):
        """測試腳本文件操作"""
        # 測試文件是否存在
        self.assertTrue(os.path.exists(self.test_script_path))
        
        # 測試文件內容
        with open(self.test_script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        self.assertIn('def main():', content)
        self.assertIn('測試腳本開始執行', content)
    
    def test_script_metadata_operations(self):
        """測試腳本元數據操作"""
        # 創建腳本記錄
        script = Script(
            name='metadata_test.py',
            description='元數據測試腳本',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()
        
        # 驗證腳本記錄
        saved_script = Script.query.filter_by(name='metadata_test.py').first()
        self.assertIsNotNone(saved_script)
        self.assertEqual(saved_script.description, '元數據測試腳本')
        self.assertTrue(saved_script.is_active)
    
    def test_script_path_security(self):
        """測試腳本路徑安全性"""
        # 測試路徑遍歷攻擊防護
        dangerous_paths = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\config\\sam',
        ]

        for dangerous_path in dangerous_paths:
            # 腳本執行器應該拒絕危險路徑
            normalized_path = os.path.normpath(os.path.join(self.scripts_dir, dangerous_path))
            # 路徑遍歷攻擊應該被阻止，所以這些路徑不應該在安全目錄內
            self.assertFalse(normalized_path.startswith(self.scripts_dir))

        # 測試安全路徑
        safe_paths = [
            'safe_script.py',
            'subfolder/script.py'
        ]

        for safe_path in safe_paths:
            normalized_path = os.path.normpath(os.path.join(self.scripts_dir, safe_path))
            # 安全路徑應該在腳本目錄內
            self.assertTrue(normalized_path.startswith(self.scripts_dir))
    
    def test_script_file_size_validation(self):
        """測試腳本文件大小驗證"""
        # 創建大文件（模擬）
        large_script_path = os.path.join(self.scripts_dir, 'large_script.py')
        with open(large_script_path, 'w', encoding='utf-8') as f:
            # 寫入大量內容
            for i in range(1000):
                f.write(f'# 這是第 {i} 行註釋\n')
            f.write(self.test_script_content)
        
        # 檢查文件大小
        file_size = os.path.getsize(large_script_path)
        self.assertGreater(file_size, 1000)  # 確保文件足夠大
        
        # 驗證腳本（應該仍然有效，因為內容正確）
        result = self.script_executor._validate_script(large_script_path)
        self.assertTrue(result['valid'])

if __name__ == '__main__':
    unittest.main()
