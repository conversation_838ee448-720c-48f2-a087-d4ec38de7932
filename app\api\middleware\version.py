# -*- coding: utf-8 -*-
from flask import request, redirect, current_app
import re

def init_app(app):
    """初始化 API 版本控制中間件"""
    
    @app.before_request
    def redirect_old_api_paths():
        """重定向舊的 API 路徑到新的版本化 API 路徑"""
        # API路徑映射
        api_path_mapping = {
            # 腳本API
            r'^/script/api/scripts$': '/api/v1/scripts',
            r'^/script/api/script/([^/]+)$': r'/api/v1/scripts/\1',
            r'^/script/api/upload$': '/api/v1/scripts/upload',
            r'^/script/api/execute/([^/]+)$': r'/api/v1/scripts/\1/execute',
            
            # 排程API
            r'^/schedule/api/schedules$': '/api/v1/schedules',
            r'^/schedule/api/schedule/([^/]+)$': r'/api/v1/schedules/\1',
            r'^/schedule/api/schedule/([^/]+)/toggle$': r'/api/v1/schedules/\1/toggle',
            
            # 系統資源API
            r'^/system-resources$': '/api/v1/system/resources',
            r'^/api/system-resources$': '/api/v1/system/resources',
            r'^/schedule/api/system-resources$': '/api/v1/system/resources'
        }
        
        path = request.path
        
        # 檢查路徑是否匹配舊API路徑模式
        for pattern, new_path in api_path_mapping.items():
            match = re.match(pattern, path)
            if match:
                # 替換捕獲的組
                if '\\' in new_path:
                    for i, group in enumerate(match.groups(), 1):
                        new_path = new_path.replace(f'\\{i}', group)
                
                # 記錄警告
                current_app.logger.warning(f"使用了棄用的API路徑: {path}，重定向到: {new_path}")
                
                # 重定向到新路徑
                return redirect(new_path)
        
        return None 