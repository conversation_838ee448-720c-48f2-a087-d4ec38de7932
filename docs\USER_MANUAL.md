# 腳本管理系統用戶手冊

## 🎯 系統概述

腳本管理系統是一個功能強大的Python腳本管理和自動化執行平台，提供直觀的Web界面來管理、執行和監控Python腳本。

### 主要功能
- 📁 **腳本管理**: 上傳、編輯、刪除Python腳本
- ⏰ **排程管理**: 設置定時執行任務
- 📊 **系統監控**: 實時監控系統資源和執行狀態
- 🔒 **安全驗證**: 腳本安全檢查和驗證
- 🏷️ **標籤分類**: 腳本分類和標籤管理

## 🚀 快速開始

### 1. 訪問系統
在瀏覽器中輸入系統地址：`http://your-domain.com`

### 2. 主界面導覽
- **首頁**: 系統概覽和快速操作
- **腳本管理**: 腳本上傳、查看、編輯
- **排程管理**: 定時任務設置和監控
- **系統監控**: 資源使用情況和日誌

## 📁 腳本管理

### 上傳腳本

1. **進入腳本管理**
   - 點擊導航欄的"腳本管理"
   - 點擊"上傳新腳本"按鈕

2. **選擇文件**
   - 拖放Python文件到上傳區域，或
   - 點擊"選擇文件"按鈕瀏覽文件

3. **腳本驗證**
   - 系統自動驗證腳本語法
   - 檢查安全性問題
   - 識別依賴包需求

4. **填寫信息**
   - **描述**: 腳本功能說明
   - **標籤**: 選擇適當的分類標籤
     - 數據處理
     - 自動化
     - 測試腳本
     - 備份還原
     - 系統維護

5. **確認上傳**
   - 檢查驗證結果
   - 確認腳本信息
   - 點擊"上傳"完成

### 管理腳本

#### 查看腳本列表
- **搜索**: 使用搜索框快速查找腳本
- **篩選**: 按標籤篩選腳本
- **排序**: 按名稱、創建時間排序

#### 腳本操作
- **查看詳情**: 點擊腳本名稱查看詳細信息
- **編輯**: 修改腳本描述和標籤
- **執行**: 立即執行腳本
- **刪除**: 移除不需要的腳本

#### 腳本詳情頁面
- **基本信息**: 名稱、描述、標籤、創建時間
- **文件信息**: 文件大小、路徑
- **執行歷史**: 最近執行記錄
- **操作按鈕**: 執行、編輯、刪除

## ⏰ 排程管理

### 創建排程

1. **進入排程管理**
   - 點擊導航欄的"排程管理"
   - 點擊"新增排程"按鈕

2. **選擇腳本**
   - 從下拉列表選擇要執行的腳本
   - 查看腳本描述確認選擇

3. **設置執行時間**

   #### 一次性執行
   - 選擇"一次性"類型
   - 設置執行日期和時間

   #### 重複執行
   - 選擇"重複"類型
   - 設置間隔時間（分鐘）

   #### 每日執行
   - 選擇"每日"類型
   - 設置執行時間

   #### 每週執行
   - 選擇"每週"類型
   - 選擇星期幾
   - 設置執行時間

   #### 每月執行
   - 選擇"每月"類型
   - 選擇日期
   - 設置執行時間

4. **填寫描述**
   - 輸入排程說明
   - 便於後續管理和識別

5. **保存排程**
   - 檢查設置信息
   - 點擊"保存"創建排程

### 管理排程

#### 排程列表
- **狀態指示**: 
  - 🟢 運行中
  - 🟡 等待中
  - 🔴 已停止
  - ⚪ 已完成

- **操作按鈕**:
  - **啟用/禁用**: 控制排程狀態
  - **編輯**: 修改排程設置
  - **刪除**: 移除排程
  - **查看日誌**: 查看執行記錄

#### 排程監控
- **下次執行時間**: 顯示預計執行時間
- **最後執行時間**: 上次執行的時間
- **執行狀態**: 成功/失敗狀態
- **錯誤信息**: 失敗時的錯誤詳情

## 📊 系統監控

### 資源監控

#### CPU使用率
- 實時顯示CPU使用百分比
- 歷史使用趨勢圖表
- 高負載警告提示

#### 記憶體使用
- 已用/總計記憶體顯示
- 記憶體使用率百分比
- 記憶體不足警告

#### 磁碟空間
- 各分區使用情況
- 可用空間提示
- 空間不足警告

### 執行日誌

#### 日誌查看
- **實時日誌**: 查看當前執行的腳本輸出
- **歷史日誌**: 瀏覽過往執行記錄
- **錯誤日誌**: 專門查看錯誤信息

#### 日誌篩選
- **時間範圍**: 選擇特定時間段
- **腳本名稱**: 篩選特定腳本的日誌
- **執行狀態**: 只看成功或失敗的記錄
- **關鍵字搜索**: 在日誌內容中搜索

## 🔧 高級功能

### 腳本編輯器
- **語法高亮**: Python語法著色
- **自動完成**: 代碼自動補全
- **錯誤檢查**: 實時語法檢查
- **保存**: 直接在線編輯和保存

### 批量操作
- **批量執行**: 同時執行多個腳本
- **批量刪除**: 一次刪除多個腳本
- **批量標籤**: 批量修改腳本標籤

### 導入導出
- **腳本導出**: 下載腳本文件
- **配置導出**: 導出排程配置
- **批量導入**: 批量上傳腳本文件

## ⚠️ 注意事項

### 腳本安全
- 只上傳可信任的Python腳本
- 注意腳本中的系統調用
- 避免包含敏感信息的腳本

### 系統資源
- 監控腳本執行對系統資源的影響
- 避免同時執行過多資源密集型腳本
- 定期清理不需要的腳本和日誌

### 數據備份
- 定期備份重要腳本
- 保存排程配置
- 注意日誌文件大小

## 🐛 常見問題

### Q: 腳本上傳失敗怎麼辦？
**A**: 檢查以下項目：
- 文件是否為.py格式
- 文件大小是否超過限制（16MB）
- 腳本語法是否正確
- 網絡連接是否正常

### Q: 排程沒有按時執行？
**A**: 可能原因：
- 排程被禁用
- 系統時間不正確
- 腳本執行時間過長
- 系統資源不足

### Q: 如何查看腳本執行錯誤？
**A**: 查看方法：
- 進入"系統監控" → "執行日誌"
- 篩選失敗的執行記錄
- 查看錯誤信息詳情
- 檢查腳本代碼問題

### Q: 系統運行緩慢怎麼辦？
**A**: 優化建議：
- 檢查系統資源使用情況
- 停止不必要的排程任務
- 清理舊的日誌文件
- 優化腳本代碼效率

## 📞 技術支持

### 獲取幫助
- **在線文檔**: 查看詳細技術文檔
- **問題反饋**: 報告系統問題或建議
- **技術支持**: 聯繫系統管理員

### 聯繫方式
- **郵箱**: <EMAIL>
- **電話**: +1-234-567-8900
- **工作時間**: 週一至週五 9:00-18:00

---

**用戶手冊版本**: v1.0  
**最後更新**: 2025-07-08  
**適用系統版本**: v1.0+
