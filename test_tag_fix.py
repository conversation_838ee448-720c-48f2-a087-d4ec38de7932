#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試標籤修復的完整流程
"""
import requests
import io
import json

def test_script_upload_with_tag():
    """測試帶標籤的腳本上傳"""
    print("🧪 測試腳本上傳（帶標籤）...")
    
    # 創建測試腳本
    test_script = '''#!/usr/bin/env python3
# 檢核通知測試腳本
print("這是一個檢核通知腳本")
print("執行檢核任務...")
'''
    
    # 準備上傳數據
    files = {
        'file': ('notification_test.py', io.BytesIO(test_script.encode('utf-8')), 'text/plain')
    }
    data = {
        'description': '檢核通知測試腳本',
        'tags': '檢核通知'
    }
    
    try:
        response = requests.post('http://localhost:5000/script/', files=files, data=data, timeout=10)
        print(f"上傳狀態: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ 上傳成功！")
            print(f"腳本名稱: {result['data']['name']}")
            print(f"描述: {result['data']['description']}")
            print(f"標籤: {result['data']['tags']}")
            return True
        else:
            print(f"❌ 上傳失敗: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 上傳錯誤: {e}")
        return False

def test_script_list_api():
    """測試腳本列表API"""
    print("\n🧪 測試腳本列表API...")
    
    try:
        response = requests.get('http://localhost:5000/script/', timeout=10)
        print(f"API狀態: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API調用成功！")
            print(f"腳本數量: {len(result['data'])}")
            
            for script in result['data']:
                print(f"- {script['name']}: 標籤='{script['tags']}'")
            
            return result['data']
        else:
            print(f"❌ API調用失敗: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ API錯誤: {e}")
        return []

def test_database_direct():
    """直接測試數據庫"""
    print("\n🧪 直接測試數據庫...")
    
    try:
        from app import create_app, db
        from app.models.script import Script
        
        app = create_app()
        with app.app_context():
            scripts = Script.query.all()
            print(f"數據庫中有 {len(scripts)} 個腳本:")
            
            for script in scripts:
                print(f"- ID: {script.id}")
                print(f"  名稱: {script.name}")
                print(f"  描述: {script.description}")
                print(f"  標籤: '{script.tags}' (類型: {type(script.tags)})")
                print(f"  to_dict(): {script.to_dict()}")
                print("---")
                
            return scripts
            
    except Exception as e:
        print(f"❌ 數據庫測試錯誤: {e}")
        return []

def test_create_script_with_tag():
    """直接在數據庫中創建帶標籤的腳本"""
    print("\n🧪 直接創建帶標籤的腳本...")
    
    try:
        from app import create_app, db
        from app.models.script import Script
        
        app = create_app()
        with app.app_context():
            # 創建測試腳本
            test_script = Script(
                name='direct_tag_test.py',
                description='直接創建的測試腳本',
                file_path='/fake/path/direct_tag_test.py',
                tags='檢核通知'
            )
            
            db.session.add(test_script)
            db.session.commit()
            
            # 查詢剛創建的腳本
            created_script = Script.query.filter_by(name='direct_tag_test.py').first()
            if created_script:
                print("✅ 腳本創建成功:")
                print(f"名稱: {created_script.name}")
                print(f"標籤: '{created_script.tags}'")
                print(f"to_dict(): {created_script.to_dict()}")
                return True
            else:
                print("❌ 腳本創建失敗")
                return False
                
    except Exception as e:
        print(f"❌ 創建腳本錯誤: {e}")
        return False

def main():
    """主測試函數"""
    print("🔍 開始標籤修復測試...")
    print("=" * 50)
    
    # 測試1: 直接數據庫測試
    test_database_direct()
    
    # 測試2: 直接創建帶標籤的腳本
    test_create_script_with_tag()
    
    # 測試3: 再次檢查數據庫
    test_database_direct()
    
    # 測試4: API測試
    scripts = test_script_list_api()
    
    # 測試5: 上傳測試
    test_script_upload_with_tag()
    
    # 測試6: 最終API測試
    final_scripts = test_script_list_api()
    
    print("\n" + "=" * 50)
    print("🎉 標籤修復測試完成！")
    
    # 分析結果
    if final_scripts:
        tagged_scripts = [s for s in final_scripts if s['tags'] != '未分類']
        print(f"總腳本數: {len(final_scripts)}")
        print(f"有標籤的腳本: {len(tagged_scripts)}")
        
        if tagged_scripts:
            print("✅ 標籤功能正常工作！")
        else:
            print("❌ 標籤功能仍有問題")
    else:
        print("❌ 無法獲取腳本列表")

if __name__ == '__main__':
    main()
