#!/usr/bin/env python3
from app import create_app, db
from app.models.script import Script

print("🔍 檢查數據庫中的腳本標籤...")

app = create_app()
with app.app_context():
    # 檢查現有腳本
    scripts = Script.query.all()
    print(f"數據庫中有 {len(scripts)} 個腳本:")
    
    for script in scripts:
        print(f"- {script.name}: tags='{script.tags}' (type: {type(script.tags)})")
    
    # 創建測試腳本
    print("\n創建測試腳本...")
    test_script = Script(
        name='tag_test_script.py',
        description='標籤測試腳本',
        file_path='/fake/path/tag_test_script.py',
        tags='檢核通知'
    )
    
    db.session.add(test_script)
    db.session.commit()
    
    # 查詢剛創建的腳本
    created = Script.query.filter_by(name='tag_test_script.py').first()
    if created:
        print(f"✅ 創建成功: {created.name}, tags='{created.tags}'")
        print(f"to_dict(): {created.to_dict()}")
    else:
        print("❌ 創建失敗")
    
    print("\n所有腳本的to_dict()結果:")
    all_scripts = Script.query.all()
    for script in all_scripts:
        data = script.to_dict()
        print(f"- {script.name}: {data}")
