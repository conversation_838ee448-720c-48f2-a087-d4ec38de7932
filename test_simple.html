<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡單測試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>簡單測試頁面</h1>
        <div class="success">✓ 頁面載入成功</div>
        
        <p>這是一個簡單的測試頁面，用來確認基本的HTML和CSS是否正常工作。</p>
        
        <button onclick="testAlert()">測試 JavaScript Alert</button>
        <button onclick="testConsole()">測試 Console Log</button>
        
        <div id="test-output"></div>
    </div>

    <script>
        function testAlert() {
            alert('JavaScript Alert 正常工作！');
        }
        
        function testConsole() {
            console.log('Console log 測試');
            const output = document.getElementById('test-output');
            output.innerHTML = '<div class="success">✓ Console log 已執行，請檢查瀏覽器開發者工具</div>';
        }
        
        // 頁面載入完成後自動執行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('頁面載入完成');
            const output = document.getElementById('test-output');
            output.innerHTML = '<div class="success">✓ DOMContentLoaded 事件已觸發</div>';
        });
    </script>
</body>
</html>
