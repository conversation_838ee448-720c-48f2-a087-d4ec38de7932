import os
import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from ..models.schedule import Schedule, ScheduleType, ScheduleStatus
from ..models.execution_log import ExecutionLog
from ..utils.logger import setup_logger
from .. import db
import logging
from flask import current_app

logger = logging.getLogger(__name__)

class ScheduleManager:
    """排程管理器"""
    
    def __init__(self, app=None):
        """初始化排程管理器"""
        self._max_schedules = 10  # 最大排程數量限制
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化應用"""
        self.app = app
    
    def create_schedule(self, script_name: str, schedule_type: str,
                       execution_time: Optional[str] = None,
                       interval_minutes: Optional[int] = None,
                       weekdays: Optional[List[int]] = None,
                       days_of_month: Optional[List[int]] = None,
                       description: str = "") -> Schedule:
        """建立新排程"""
        try:
            # 檢查排程數量是否超過限制
            if len(self.get_all_schedules()) >= self._max_schedules:
                raise ValueError("排程數量已達上限")
                
            # 建立排程
            schedule = Schedule(
                script_name=script_name,
                schedule_type=schedule_type,  # 已在 Schedule 模型中處理轉換
                execution_time=datetime.fromisoformat(execution_time) if execution_time else None,
                interval_minutes=interval_minutes,
                weekdays=','.join(map(str, weekdays)) if weekdays else None,
                days_of_month=','.join(map(str, days_of_month)) if days_of_month else None,
                description=description,
                status=ScheduleStatus.PENDING.value,  # 使用 .value
                is_active=True
            )
            
            db.session.add(schedule)
            db.session.commit()
            
            logger.info(f"建立排程成功: {schedule.id}")
            return schedule
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"建立排程失敗: {str(e)}")
            raise
    
    def get_schedule(self, schedule_id: int) -> Optional[Schedule]:
        """獲取特定排程"""
        try:
            return Schedule.query.get(schedule_id)
        except Exception as e:
            logger.error(f"獲取排程失敗: {str(e)}")
            return None

    def get_schedule_by_id(self, schedule_id: int) -> Optional[Schedule]:
        """獲取特定排程（別名方法）"""
        return self.get_schedule(schedule_id)
    
    def get_all_schedules(self) -> List[Schedule]:
        """獲取所有排程"""
        try:
            return Schedule.query.all()
        except Exception as e:
            logger.error(f"獲取排程列表失敗: {str(e)}")
            return []
    
    def get_active_schedules(self) -> List[Schedule]:
        """獲取所有啟用的排程"""
        try:
            return Schedule.query.filter_by(is_active=True).all()
        except Exception as e:
            logger.error(f"獲取啟用排程列表失敗: {str(e)}")
            return []
    
    def update_schedule(self, schedule_id: int, **kwargs) -> Optional[Schedule]:
        """更新排程"""
        try:
            schedule = self.get_schedule(schedule_id)
            if not schedule:
                return None
            
            # 處理特殊欄位
            if 'execution_time' in kwargs and kwargs['execution_time']:
                kwargs['execution_time'] = datetime.fromisoformat(kwargs['execution_time'])
            
            if 'weekdays' in kwargs and kwargs['weekdays'] is not None:
                kwargs['weekdays'] = ','.join(map(str, kwargs['weekdays']))
            
            if 'days_of_month' in kwargs and kwargs['days_of_month'] is not None:
                kwargs['days_of_month'] = ','.join(map(str, kwargs['days_of_month']))
            
            if 'schedule_type' in kwargs:
                if kwargs['schedule_type'] not in [t.value for t in ScheduleType]:
                    raise ValueError(f"無效的排程類型: {kwargs['schedule_type']}")
            
            if 'status' in kwargs:
                if kwargs['status'] not in [s.value for s in ScheduleStatus]:
                    raise ValueError(f"無效的狀態值: {kwargs['status']}")
            
            # 更新欄位
            for key, value in kwargs.items():
                if hasattr(schedule, key):
                    setattr(schedule, key, value)
            
            db.session.commit()
            logger.info(f"更新排程成功: {schedule_id}")
            return schedule
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新排程失敗: {str(e)}")
            raise  # 讓呼叫者處理錯誤
    
    def delete_schedule(self, schedule_id: int) -> bool:
        """刪除排程"""
        try:
            schedule = self.get_schedule(schedule_id)
            if schedule:
                db.session.delete(schedule)
                db.session.commit()
                logger.info(f"刪除排程成功: {schedule_id}")
                return True
            return False
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"刪除排程失敗: {str(e)}")
            return False
    
    def get_max_schedules(self) -> int:
        """獲取最大排程數量限制"""
        return self._max_schedules
    
    def update_schedule_status(self, schedule_id: int, status: str) -> Optional[Schedule]:
        """更新排程狀態"""
        try:
            schedule = self.get_schedule(schedule_id)
            if not schedule:
                return None
                
            # 驗證狀態值
            if status not in [s.value for s in ScheduleStatus]:
                raise ValueError(f"無效的狀態值: {status}")
                
            schedule.status = status  # 直接使用字串值
            db.session.commit()
            return schedule
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新排程狀態失敗: {str(e)}")
            return None
    
    def get_pending_schedules(self) -> List[Schedule]:
        """獲取待執行的排程"""
        try:
            return Schedule.query.filter_by(
                status=ScheduleStatus.PENDING.value,
                is_active=True
            ).all()
        except Exception as e:
            logger.error(f"獲取待執行排程列表失敗: {str(e)}")
            return []
    
    def add_execution_log(self, schedule_id: int, status: str, output: str = None, error: str = None) -> Optional[ExecutionLog]:
        """添加執行日誌"""
        schedule = self.get_schedule(schedule_id)
        if not schedule:
            return None
            
        log = ExecutionLog(
            schedule_id=schedule_id,
            status=status,
            output=output,
            error=error
        )
        
        db.session.add(log)
        db.session.commit()
        return log
    
    def get_execution_logs(self, schedule_id: int) -> List[ExecutionLog]:
        """獲取排程的執行日誌"""
        return ExecutionLog.query.filter_by(schedule_id=schedule_id).all()

    def create_execution_log(self, schedule_id: int, status: str, output: str = None, error_message: str = None) -> ExecutionLog:
        """建立執行記錄"""
        log = ExecutionLog(
            schedule_id=schedule_id,
            status=status,
            output=output,
            error_message=error_message
        )
        db.session.add(log)
        db.session.commit()
        return log

    def update_execution_log(self, log_id: int, **kwargs) -> Optional[ExecutionLog]:
        """更新執行記錄"""
        try:
            log = ExecutionLog.query.get(log_id)
            if not log:
                return None
            
            # 更新欄位
            for key, value in kwargs.items():
                if hasattr(log, key):
                    setattr(log, key, value)
            
            # 如果狀態更新為完成或失敗，設置結束時間
            if kwargs.get('status') in ['completed', 'failed']:
                log.end_time = datetime.now()
            
            # 儲存變更
            db.session.commit()
            
            logger.info(f'更新執行記錄成功: {log_id}')
            return log
            
        except Exception as e:
            logger.error(f'更新執行記錄失敗: {str(e)}')
            db.session.rollback()
            raise

    def get_schedule_logs(self, schedule_id: int) -> List[ExecutionLog]:
        """獲取排程的執行記錄"""
        try:
            return ExecutionLog.query.filter_by(schedule_id=schedule_id).all()
        except Exception as e:
            logger.error(f'獲取執行記錄失敗: {str(e)}')
            raise

    def toggle_schedule(self, schedule_id: int) -> Optional[Schedule]:
        """切換排程的啟用狀態"""
        try:
            schedule = self.get_schedule(schedule_id)
            if not schedule:
                return None
            
            schedule.is_active = not schedule.is_active
            db.session.commit()
            
            logger.info(f"切換排程 {schedule_id} 狀態為 {'啟用' if schedule.is_active else '停用'}")
            return schedule
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"切換排程狀態失敗: {str(e)}")
            raise 