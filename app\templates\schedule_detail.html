{% extends "base.html" %}
{% from "components/schedule_detail.html" import schedule_detail %}

{% block title %}排程詳情{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/schedules">排程管理</a></li>
                    <li class="breadcrumb-item active">排程詳情</li>
                </ol>
            </nav>
            {{ schedule_detail(schedule, logs) }}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 更新系統資源使用狀況
function updateSystemResources() {
    fetch('/api/api/v1/system/resources')
        .then(response => response.json())
        .then(data => {
            document.getElementById('cpu-usage').style.width = `${data.cpu}%`;
            document.getElementById('memory-usage').style.width = `${data.memory}%`;
            document.getElementById('schedule-count').style.width = `${(data.active_schedules / data.max_schedules) * 100}%`;
        })
        .catch(error => console.error('Error updating system resources:', error));
}

// 定期更新系統資源
setInterval(updateSystemResources, 5000);
</script>
{% endblock %} 