from flask import jsonify, request, current_app
from . import bp
from app import scheduler, socketio
from datetime import datetime
import json
import os

SCHEDULE_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'schedules.json')

def load_schedules():
    """從文件加載排程設置"""
    if os.path.exists(SCHEDULE_FILE):
        with open(SCHEDULE_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def save_schedules(schedules):
    """保存排程設置到文件"""
    with open(SCHEDULE_FILE, 'w', encoding='utf-8') as f:
        json.dump(schedules, f, ensure_ascii=False, indent=2)

def emit_schedule_update():
    """發送排程更新通知"""
    schedules = load_schedules()
    socketio.emit('schedule_update', schedules)

@bp.route('/schedules')
def get_schedules():
    """獲取所有排程"""
    return jsonify(load_schedules())

@bp.route('/schedule', methods=['POST'])
def add_schedule():
    """添加新排程"""
    data = request.json
    script_name = data.get('script_name')
    schedule_type = data.get('schedule_type')
    schedule_config = data.get('schedule_config', {})

    if not all([script_name, schedule_type]):
        return jsonify({'error': '缺少必要參數'}), 400

    schedules = load_schedules()
    
    # 生成排程ID
    schedule_id = f"{script_name}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    # 根據不同排程類型設置觸發器
    try:
        if schedule_type == 'once':
            trigger = 'date'
            run_date = schedule_config.get('run_date')
            if not run_date:
                return jsonify({'error': '缺少執行時間'}), 400
            scheduler.add_job(
                'app.core.routes:run_script',
                trigger=trigger,
                run_date=run_date,
                args=[script_name],
                id=schedule_id
            )
        
        elif schedule_type == 'interval':
            trigger = 'interval'
            minutes = schedule_config.get('minutes', 1)
            scheduler.add_job(
                'app.core.routes:run_script',
                trigger=trigger,
                minutes=minutes,
                args=[script_name],
                id=schedule_id
            )
        
        elif schedule_type in ['daily', 'weekly', 'monthly']:
            trigger = 'cron'
            hour = schedule_config.get('hour', 0)
            minute = schedule_config.get('minute', 0)
            
            kwargs = {'hour': hour, 'minute': minute}
            
            if schedule_type == 'weekly':
                kwargs['day_of_week'] = schedule_config.get('day_of_week', 0)
            elif schedule_type == 'monthly':
                kwargs['day'] = schedule_config.get('day', 1)
            
            scheduler.add_job(
                'app.core.routes:run_script',
                trigger=trigger,
                args=[script_name],
                id=schedule_id,
                **kwargs
            )
        
        else:
            return jsonify({'error': '不支持的排程類型'}), 400
        
        # 保存排程信息
        schedules[schedule_id] = {
            'script_name': script_name,
            'schedule_type': schedule_type,
            'schedule_config': schedule_config,
            'status': 'active'
        }
        save_schedules(schedules)
        emit_schedule_update()
        
        return jsonify({
            'schedule_id': schedule_id,
            'message': '排程創建成功'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/schedule/<schedule_id>', methods=['DELETE'])
def delete_schedule(schedule_id):
    """刪除排程"""
    try:
        scheduler.remove_job(schedule_id)
        schedules = load_schedules()
        if schedule_id in schedules:
            del schedules[schedule_id]
            save_schedules(schedules)
            emit_schedule_update()
        return jsonify({'message': '排程已刪除'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/schedule/<schedule_id>/toggle', methods=['POST'])
def toggle_schedule(schedule_id):
    """啟用/禁用排程"""
    try:
        schedules = load_schedules()
        if schedule_id not in schedules:
            return jsonify({'error': '排程不存在'}), 404
        
        current_status = schedules[schedule_id]['status']
        new_status = 'inactive' if current_status == 'active' else 'active'
        
        if new_status == 'active':
            scheduler.resume_job(schedule_id)
        else:
            scheduler.pause_job(schedule_id)
        
        schedules[schedule_id]['status'] = new_status
        save_schedules(schedules)
        emit_schedule_update()
        
        return jsonify({
            'schedule_id': schedule_id,
            'status': new_status
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500