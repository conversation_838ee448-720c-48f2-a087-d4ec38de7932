{% macro schedule_detail(schedule, logs) %}
<div class="schedule-detail">
    <div class="schedule-header">
        <h3>排程詳情</h3>
        <div class="schedule-actions">
            <button class="btn btn-primary" onclick="executeSchedule({{ schedule.id }})">
                <i class="fas fa-play"></i> 立即執行
            </button>
            <button class="btn btn-warning" onclick="editSchedule({{ schedule.id }})">
                <i class="fas fa-edit"></i> 編輯
            </button>
            <button class="btn btn-danger" onclick="deleteSchedule({{ schedule.id }})">
                <i class="fas fa-trash"></i> 刪除
            </button>
        </div>
    </div>

    <div class="schedule-info">
        <div class="info-card">
            <h4>基本資訊</h4>
            <table class="table">
                <tr>
                    <th>腳本名稱</th>
                    <td>{{ schedule.script_name }}</td>
                </tr>
                <tr>
                    <th>排程類型</th>
                    <td>{{ schedule.schedule_type }}</td>
                </tr>
                <tr>
                    <th>執行時間</th>
                    <td>{{ schedule.execution_time }}</td>
                </tr>
                <tr>
                    <th>狀態</th>
                    <td>
                        <span class="status-badge status-{{ schedule.status.lower() }}">
                            {{ schedule.status }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <th>上次執行</th>
                    <td>{{ schedule.last_run or '從未執行' }}</td>
                </tr>
                <tr>
                    <th>下次執行</th>
                    <td>{{ schedule.next_run or '無' }}</td>
                </tr>
            </table>
        </div>

        <div class="info-card">
            <h4>執行日誌</h4>
            <div class="log-container">
                {% for log in logs %}
                <div class="log-entry">
                    <span class="log-time">{{ log.timestamp }}</span>
                    <span class="log-status status-{{ log.status.lower() }}">{{ log.status }}</span>
                    <span class="log-message">{{ log.message }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<style>
.schedule-detail {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.schedule-actions {
    display: flex;
    gap: 10px;
}

.schedule-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.info-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.info-card h4 {
    margin-bottom: 15px;
    color: #333;
}

.log-container {
    max-height: 400px;
    overflow-y: auto;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

.log-entry {
    padding: 8px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 10px;
}

.log-time {
    color: #666;
    font-size: 0.9em;
}

.log-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    font-weight: bold;
}

.log-message {
    flex: 1;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.status-pending {
    background-color: #ffd700;
    color: #000;
}

.status-running {
    background-color: #4CAF50;
    color: white;
}

.status-completed {
    background-color: #2196F3;
    color: white;
}

.status-failed {
    background-color: #f44336;
    color: white;
}
</style>

<script>
function executeSchedule(scheduleId) {
    if (confirm('確定要立即執行此排程嗎？')) {
        fetch(`/schedule/api/schedule/${scheduleId}/execute`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            } else {
                alert('執行失敗：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('執行失敗，請稍後再試');
        });
    }
}

function editSchedule(scheduleId) {
    window.location.href = `/schedule/${scheduleId}/edit`;
}

function deleteSchedule(scheduleId) {
    if (confirm('確定要刪除此排程嗎？')) {
        fetch(`/schedule/api/schedule/${scheduleId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                window.location.href = '/schedules';
            } else {
                alert('刪除失敗：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('刪除失敗，請稍後再試');
        });
    }
}

// 定期更新排程狀態和日誌
setInterval(() => {
    fetch(`/schedule/api/schedule/${scheduleId}`)
        .then(response => response.json())
        .then(data => {
            // 更新基本資訊
            document.querySelector('.status-badge').textContent = data.status;
            document.querySelector('td:nth-child(5)').textContent = data.last_run || '從未執行';
            document.querySelector('td:nth-child(6)').textContent = data.next_run || '無';
            
            // 更新日誌
            fetch(`/schedule/api/schedule/${scheduleId}/logs`)
                .then(response => response.json())
                .then(logs => {
                    const logContainer = document.querySelector('.log-container');
                    logContainer.innerHTML = logs.map(log => `
                        <div class="log-entry">
                            <span class="log-time">${log.timestamp}</span>
                            <span class="log-status status-${log.status.toLowerCase()}">${log.status}</span>
                            <span class="log-message">${log.message}</span>
                        </div>
                    `).join('');
                });
        });
}, 5000); // 每5秒更新一次
</script>
{% endmacro %} 