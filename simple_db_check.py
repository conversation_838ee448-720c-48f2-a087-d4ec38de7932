import sqlite3
import os

# 檢查數據庫文件
db_path = 'app.db'
if os.path.exists(db_path):
    print(f"數據庫文件存在: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 檢查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='schedules';")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("schedules 表存在")
            
            # 檢查排程數量
            cursor.execute("SELECT COUNT(*) FROM schedules")
            count = cursor.fetchone()[0]
            print(f"排程總數: {count}")
            
            if count > 0:
                # 顯示所有排程
                cursor.execute("SELECT id, description, script_name, status, is_active FROM schedules")
                rows = cursor.fetchall()
                print("\n排程列表:")
                for row in rows:
                    print(f"ID:{row[0]}, 描述:{row[1]}, 腳本:{row[2]}, 狀態:{row[3]}, 啟用:{row[4]}")
            else:
                print("數據庫中沒有排程記錄")
        else:
            print("schedules 表不存在")
            
    except Exception as e:
        print(f"查詢錯誤: {e}")
    finally:
        conn.close()
else:
    print(f"數據庫文件不存在: {db_path}")
