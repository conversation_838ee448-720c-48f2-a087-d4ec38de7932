import os
import shutil
import unittest
import time
import logging
from datetime import datetime
from app import create_app, db
from app.services.script_executor import ScriptExecutor
from app.models.script import Script

# 設定測試日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestScriptExecutor(unittest.TestCase):
    """腳本執行器測試類別"""
    
    @classmethod
    def setUpClass(cls):
        """測試類別設定"""
        logger.info("開始設定測試環境...")
        # 建立測試目錄
        cls.test_dirs = ['uploads', 'outputs']
        for dir_name in cls.test_dirs:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
            os.makedirs(dir_name)
        logger.info("測試環境設定完成")
            
    @classmethod
    def tearDownClass(cls):
        """測試類別清理"""
        logger.info("開始清理測試環境...")
        # 清理測試目錄
        for dir_name in cls.test_dirs:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
        logger.info("測試環境清理完成")
                
    def setUp(self):
        """測試環境設定"""
        # 建立測試應用程式
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 設定測試資料庫
        db.create_all()
        
        # 建立測試腳本
        self.test_script = Script(
            name='test_script.py',
            description='測試腳本',
            file_path=os.path.join('uploads', 'test_script.py'),
            is_active=True
        )
        db.session.add(self.test_script)
        db.session.commit()
        
        # 建立測試腳本檔案
        with open(self.test_script.file_path, 'w', encoding='utf-8') as f:
            f.write('print("測試輸出")\n')
            
        # 初始化執行器
        self.executor = ScriptExecutor()
        
    def tearDown(self):
        """測試環境清理"""
        try:
            # 清理資料庫
            db.session.remove()
            db.drop_all()
            
            # 移除應用程式上下文
            self.app_context.pop()
            
            # 清理測試檔案
            if os.path.exists(self.test_script.file_path):
                os.remove(self.test_script.file_path)
                
            # 清理輸出目錄
            output_dir = os.path.join('outputs', self.test_script.name)
            if os.path.exists(output_dir):
                shutil.rmtree(output_dir)
                
        except Exception as e:
            logger.error(f"清理測試環境時發生錯誤: {str(e)}")
            
    def test_script_execution(self):
        """測試腳本執行功能"""
        logger.info("執行測試：腳本執行功能")
        start_time = time.time()
        
        # 執行腳本
        success, output, error = self.executor.execute_script('test_script.py')
        
        # 驗證執行結果
        self.assertTrue(success, f"腳本執行失敗: {error}")
        self.assertIn('測試輸出', output)
        self.assertIsNone(error)
        
        # 驗證輸出檔案
        outputs = self.executor.get_script_outputs(self.test_script.id)
        self.assertEqual(len(outputs), 1)
        self.assertTrue(outputs[0]['filename'].startswith('output_'))
        self.assertTrue(outputs[0]['filename'].endswith('.log'))
        
        logger.info(f"測試完成，耗時：{time.time() - start_time:.2f}秒")
        
    def test_script_execution_error(self):
        """測試腳本執行錯誤處理"""
        logger.info("執行測試：腳本執行錯誤處理")
        start_time = time.time()
        
        # 修改腳本內容為錯誤程式碼
        with open(self.test_script.file_path, 'w', encoding='utf-8') as f:
            f.write('print("測試輸出")\n')
            f.write('raise Exception("測試錯誤")\n')
            
        # 執行腳本
        success, output, error = self.executor.execute_script('test_script.py')
        
        # 驗證執行結果
        self.assertFalse(success)
        self.assertIsNone(output)
        self.assertIn('測試錯誤', error)
        
        logger.info(f"測試完成，耗時：{time.time() - start_time:.2f}秒")
        
    def test_script_output_management(self):
        """測試輸出檔案管理"""
        logger.info("執行測試：輸出檔案管理")
        start_time = time.time()
        
        # 執行腳本兩次（減少執行次數）
        for i in range(2):
            logger.info(f"執行第 {i+1} 次腳本...")
            success, _, _ = self.executor.execute_script('test_script.py')
            self.assertTrue(success)
            time.sleep(0.05)  # 減少等待時間
            
        # 驗證輸出檔案列表
        outputs = self.executor.get_script_outputs(self.test_script.id)
        self.assertEqual(len(outputs), 2)
        
        # 驗證輸出檔案內容
        for output in outputs:
            content = self.executor.get_script_output(self.test_script.id, output['filename'])
            self.assertIsNotNone(content)
            self.assertIn('測試輸出', content)
            
        # 刪除輸出檔案
        filename = outputs[0]['filename']
        self.assertTrue(self.executor.delete_script_output(self.test_script.id, filename))
        
        # 驗證檔案已刪除
        outputs = self.executor.get_script_outputs(self.test_script.id)
        self.assertEqual(len(outputs), 1)
        filenames = [output['filename'] for output in outputs]
        self.assertNotIn(filename, filenames)
        
        logger.info(f"測試完成，耗時：{time.time() - start_time:.2f}秒")
        
    def test_script_not_found(self):
        """測試找不到腳本的情況"""
        logger.info("執行測試：找不到腳本的情況")
        start_time = time.time()
        
        # 執行不存在的腳本
        success, output, error = self.executor.execute_script('non_existent.py')
        
        # 驗證執行結果
        self.assertFalse(success)
        self.assertIsNone(output)
        self.assertIn('找不到腳本', error)
        
        logger.info(f"測試完成，耗時：{time.time() - start_time:.2f}秒")
        
    def test_script_timeout(self):
        """測試腳本執行超時的情況"""
        print("\n" + "="*50)
        print("開始執行超時測試...")
        print("="*50 + "\n")
        
        start_time = time.time()
        test_timeout = 3  # 整體測試超時時間為 3 秒
        
        try:
            # 準備測試腳本
            print("1. 準備測試腳本...")
            script_content = '''
import time
print("開始執行計時器...")
while True:  # 無限迴圈
    print("執行中...")
    time.sleep(0.1)  # 每次迭代休眠 0.1 秒
print("完成！")  # 這一行永遠不會執行到
'''
            with open(self.test_script.file_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # 設定較短的超時時間
            print("2. 設定超時時間...")
            self.executor.timeout = 0.2  # 200ms 超時
            
            print("3. 開始執行腳本...")
            success, output, error = self.executor.execute_script(self.test_script.name)
            
            # 驗證執行結果
            print("4. 驗證執行結果...")
            self.assertFalse(success, "腳本應該因為超時而失敗")
            self.assertIsNotNone(output, "應該有部分輸出")
            self.assertIn('開始執行計時器', output, "輸出應包含開始訊息")
            self.assertIn('超時', error, "錯誤訊息應包含'超時'")
            
            # 驗證輸出檔案
            print("5. 驗證輸出檔案...")
            outputs = self.executor.get_script_outputs(self.test_script.id)
            self.assertEqual(len(outputs), 1, "應該只有一個輸出檔案")
            
            content = self.executor.get_script_output(self.test_script.id, outputs[0]['filename'])
            self.assertIsNotNone(content, "應該能夠讀取輸出檔案")
            self.assertIn('開始執行計時器', content, "輸出應包含開始訊息")
            self.assertIn('超時', content, "輸出應包含超時訊息")
            
            duration = time.time() - start_time
            print("\n" + "="*50)
            print(f"測試完成！總耗時：{duration:.2f}秒")
            print("="*50 + "\n")
            
            logger.info(f"測試完成，耗時：{duration:.2f}秒")
            
        except Exception as e:
            duration = time.time() - start_time
            print("\n" + "="*50)
            print(f"測試發生錯誤！總耗時：{duration:.2f}秒")
            print(f"錯誤訊息：{str(e)}")
            print("="*50 + "\n")
            raise
        finally:
            # 檢查是否超過總體超時時間
            if time.time() - start_time > test_timeout:
                print("\n" + "="*50)
                print("警告：測試執行時間超過 3 秒！")
                print("正在清理進程...")
                try:
                    import psutil
                    for proc in psutil.process_iter(['pid', 'name']):
                        if 'python' in proc.info['name'].lower():
                            try:
                                proc.kill()
                            except psutil.NoSuchProcess:
                                pass
                except Exception as e:
                    print(f"清理進程時發生錯誤：{str(e)}")
                print("="*50 + "\n") 