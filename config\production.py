# -*- coding: utf-8 -*-
from config.default import DefaultConfig
import os
import logging

class ProductionConfig(DefaultConfig):
    """
    生產環境配置
    """
    
    # 調試模式關閉
    DEBUG = False
    
    # SQL回顯關閉
    SQLALCHEMY_ECHO = False
    
    # 數據庫URI
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///app.db')
    
    # 日誌級別
    LOG_LEVEL = logging.INFO
    
    # 最大排程數
    MAX_SCHEDULES = 50
    
    @classmethod
    def init_app(cls, app):
        """初始化應用配置"""
        super().init_app(app)
        
        # 生產環境特定初始化
        # 配置生產環境日誌
        if not app.debug and not app.testing:
            # 使用更強大的日誌處理
            from logging.handlers import RotatingFileHandler
            file_handler = RotatingFileHandler(
                os.path.join(app.config['LOG_FOLDER'], 'application.log'),
                maxBytes=10485760, # 10MB
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s '
                '[in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('應用啟動 - 生產環境')