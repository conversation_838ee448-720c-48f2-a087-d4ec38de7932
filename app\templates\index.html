<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python 腳本排程與執行管理</title>
    <!-- FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid py-4">
        <h1 class="text-center mb-4">Python 腳本排程與執行管理</h1>

        <!-- 主要內容區域 -->
        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>排程項目管理</h4>
                        <div>
                            <button class="btn btn-primary me-2" onclick="toggleModal('scheduleModal')">
                                <i class="fas fa-plus"></i> 新增排程
                            </button>
                            <button class="btn btn-outline-primary" onclick="toggleModal('scriptModal')">
                                <i class="fas fa-file-code"></i> 腳本管理
                            </button>
                        </div>
                    </div>

                    <!-- 排程列表 -->
                    <div class="card">
                        <div class="card-body">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>排程名稱</th>
                                        <th>腳本</th>
                                        <th>執行時間</th>
                                        <th>狀態</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="scheduleList">
                                    <!-- 排程列表將由 JavaScript 動態填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 腳本管理模態框 -->
        <div class="modal fade" id="scriptModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="scriptModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="scriptModalLabel">腳本管理</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- 腳本列表區域 -->
                        <div id="scriptListView">
                            <div class="d-flex justify-content-end mb-3">
                                <button class="btn btn-primary" onclick="showUploadView()">
                                    <i class="fas fa-plus"></i> 上傳新腳本
                                </button>
                            </div>
                            <div class="list-group" id="scriptList">
                                <!-- 腳本列表將由 JavaScript 動態填充 -->
                            </div>
                        </div>

                        <!-- 上傳腳本區域 -->
                        <div id="uploadView" style="display: none;">
                            <!-- 步驟指示器 -->
                            <div class="mb-4">
                                <h5 class="current-step-title text-center mb-4">選擇檔案</h5>
                                <div class="d-flex justify-content-between align-items-center position-relative stepper">
                                    <div class="step-indicator active" data-step="1">1</div>
                                    <div class="step-indicator" data-step="2">2</div>
                                    <div class="step-indicator" data-step="3">3</div>
                                    <div class="step-indicator" data-step="4">4</div>
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <div class="step-title active">選擇檔案</div>
                                    <div class="step-title">驗證檢查</div>
                                    <div class="step-title">依賴安裝</div>
                                    <div class="step-title">確認上傳</div>
                                </div>
                            </div>

                            <!-- 步驟內容 -->
                            <div class="step-contents">
                                <!-- 步驟 1: 選擇檔案 -->
                                <div class="step-content active" data-step="1">
                                    <input type="file" id="fileInput" accept=".py" class="d-none">
                                    <div id="dropZone" class="drop-zone">
                                        <div class="upload-icon bounce-in">
                                            <i class="fas fa-cloud-upload-alt fa-3x"></i>
                                        </div>
                                        <div class="upload-text fade-in">
                                            <p class="mb-2">拖放 Python 腳本至此處</p>
                                            <p class="text-muted">或</p>
                                            <button type="button" class="btn btn-outline-primary mt-2" onclick="document.getElementById('fileInput').click()">
                                                <i class="fas fa-folder-open"></i> 選擇文件
                                            </button>
                                        </div>
                                        <div class="upload-hint mt-3">
                                            <p class="text-muted small">僅支援 .py 文件，最大 5MB</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步驟 2: 驗證檢查 -->
                                <div class="step-content" data-step="2">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title mb-4">
                                                <i class="fas fa-check-circle"></i> 驗證檢查
                                            </h5>
                                            <div id="validationDetails"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步驟 3: 依賴安裝 -->
                                <div class="step-content" data-step="3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title mb-4">
                                                <i class="fas fa-box"></i> 依賴套件管理
                                            </h5>
                                            <div id="dependencyInfo"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步驟 4: 確認上傳 -->
                                <div class="step-content" data-step="4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title mb-4">
                                                <i class="fas fa-upload"></i> 確認上傳
                                            </h5>
                                            <div id="uploadConfirm">
                                                <div class="mb-3">
                                                    <label for="scriptDescription" class="form-label">腳本描述</label>
                                                    <input type="text" class="form-control" id="scriptDescription" 
                                                           maxlength="20" placeholder="請輸入腳本描述（最多20字）">
                                                    <div class="form-text">還可以輸入<span id="charCount">20</span>個字</div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">選擇標籤</label>
                                                    <div class="tag-options">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="scriptTag" value="網頁爬蟲" id="tag1">
                                                            <label class="form-check-label" for="tag1">網頁爬蟲</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="scriptTag" value="數據處理" id="tag2">
                                                            <label class="form-check-label" for="tag2">數據處理</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="scriptTag" value="文件處理" id="tag3">
                                                            <label class="form-check-label" for="tag3">文件處理</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="scriptTag" value="自動化測試" id="tag4">
                                                            <label class="form-check-label" for="tag4">自動化測試</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="scriptTag" value="其他" id="tag5">
                                                            <label class="form-check-label" for="tag5">其他</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 步驟控制按鈕 -->
                            <div class="d-flex justify-content-between mt-4">
                                <button type="button" class="btn btn-secondary" id="prevStepBtn" onclick="prevStep()">
                                    <i class="fas fa-arrow-left"></i> 上一步
                                </button>
                                <button type="button" class="btn btn-primary" id="nextStepBtn" onclick="nextStep()">
                                    下一步 <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增排程模態框 -->
        <div class="modal" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="scheduleModalLabel">新增排程</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="closeModal('scheduleModal')"></button>
                    </div>
                    <div class="modal-body">
                        <form id="scheduleForm">
                            <!-- 系統資源使用資訊 - 移到最頂部 -->
                            <div class="mb-4 system-resources">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-server"></i> 系統資源使用狀況
                                        </h6>
                                        <div class="row mt-2">
                                            <div class="col-md-4">
                                                <div class="resource-meter">
                                                    <span>CPU: </span>
                                                    <div class="progress">
                                                        <div class="progress-bar" id="cpu-usage" role="progressbar" style="width: 25%;" data-content="25%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="resource-meter">
                                                    <span>記憶體: </span>
                                                    <div class="progress">
                                                        <div class="progress-bar" id="memory-usage" role="progressbar" style="width: 40%;" data-content="40%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="resource-meter">
                                                    <span>同時排程: </span>
                                                    <div class="progress">
                                                        <div class="progress-bar" id="schedule-count" role="progressbar" style="width: 30%;" data-content="3/10"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="script-select" class="form-label required">選擇腳本</label>
                                <select class="form-select" id="script-select" required>
                                    <option value="" selected disabled>請選擇腳本</option>
                                    <!-- 動態填充腳本選項 -->
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="schedule-type" class="form-label required">排程類型</label>
                                <select class="form-select" id="schedule-type" required>
                                    <option value="immediate">立即執行</option>
                                    <option value="once">一次排程</option>
                                    <option value="minute">每分鐘</option>
                                    <option value="daily">每日執行</option>
                                    <option value="weekly">每週執行</option>
                                    <option value="monthly">每月執行</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="schedule-description" class="form-label">排程描述</label>
                                <input type="text" class="form-control" id="schedule-description" placeholder="例如：資料備份任務">
                            </div>
                            
                            <!-- 一次排程選項 -->
                            <div class="schedule-option once-options card shadow-sm mb-3" style="display:none;">
                                <div class="card-body">
                                    <h6 class="card-title mb-3">一次排程設定</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="execution-date" class="form-label required">執行日期</label>
                                            <input type="date" class="form-control" id="execution-date">
                                            <div class="invalid-feedback" id="date-feedback">日期不能是過去時間</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="execution-time" class="form-label required">執行時間</label>
                                            <input type="time" class="form-control" id="execution-time">
                                            <div class="invalid-feedback" id="time-feedback">時間不能是過去時間</div>
                                        </div>
                                    </div>
                                    <div id="datetime-warning" class="alert alert-warning d-none">
                                        <i class="fas fa-exclamation-triangle"></i> 警告：此時段已有 <span id="conflict-count">0</span> 個排程任務
                                    </div>
                                    <div id="time-auto-adjusted" class="alert alert-info d-none">
                                        <i class="fas fa-info-circle"></i> 您選擇的時間已過，系統已自動調整為下一個可執行時間
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 每分鐘選項 -->
                            <div class="schedule-option minute-options card shadow-sm mb-3" style="display:none;">
                                <div class="card-body">
                                    <h6 class="card-title mb-3">每分鐘排程設定</h6>
                                    <div class="mb-3">
                                        <label for="interval-minutes" class="form-label required">間隔分鐘數</label>
                                        <div class="d-flex align-items-center">
                                            <input type="range" class="form-range flex-grow-1 me-2" id="interval-minutes-range" min="5" max="60" step="5" value="5">
                                            <input type="number" class="form-control" id="interval-minutes" min="5" value="5" style="width: 80px;">
                                        </div>
                                        <small class="form-text text-muted">最小間隔為5分鐘</small>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">預計執行時間</label>
                                        <div class="next-execution-times">
                                            <ul class="list-group list-group-flush" id="minute-preview">
                                                <li class="list-group-item">載入中...</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 每日執行選項 -->
                            <div class="schedule-option daily-options card shadow-sm mb-3" style="display:none;">
                                <div class="card-body">
                                    <h6 class="card-title mb-3">每日排程設定</h6>
                                    <div class="mb-3">
                                        <label for="daily-time" class="form-label required">執行時間</label>
                                        <input type="time" class="form-control" id="daily-time">
                                        <div id="daily-auto-adjusted" class="alert alert-info mt-2 d-none">
                                            <i class="fas fa-info-circle"></i> 今日此時間已過，將從明日開始執行
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="daily-weekday-only">
                                            <label class="form-check-label" for="daily-weekday-only">
                                                僅工作日執行（週一至週五）
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 每週執行選項 -->
                            <div class="schedule-option weekly-options card shadow-sm mb-3" style="display:none;">
                                <div class="card-body">
                                    <h6 class="card-title mb-3">每週排程設定</h6>
                                    <div class="mb-3">
                                        <label class="form-label required">執行星期</label>
                                        <div class="weekday-selector">
                                            <div class="btn-group" role="group" aria-label="週間選擇">
                                                <input type="checkbox" class="btn-check" name="weekdays" value="0" id="week-sun" autocomplete="off">
                                                <label class="btn btn-outline-primary" for="week-sun">日</label>
                                                
                                                <input type="checkbox" class="btn-check" name="weekdays" value="1" id="week-mon" autocomplete="off">
                                                <label class="btn btn-outline-primary" for="week-mon">一</label>
                                                
                                                <input type="checkbox" class="btn-check" name="weekdays" value="2" id="week-tue" autocomplete="off">
                                                <label class="btn btn-outline-primary" for="week-tue">二</label>
                                                
                                                <input type="checkbox" class="btn-check" name="weekdays" value="3" id="week-wed" autocomplete="off">
                                                <label class="btn btn-outline-primary" for="week-wed">三</label>
                                                
                                                <input type="checkbox" class="btn-check" name="weekdays" value="4" id="week-thu" autocomplete="off">
                                                <label class="btn btn-outline-primary" for="week-thu">四</label>
                                                
                                                <input type="checkbox" class="btn-check" name="weekdays" value="5" id="week-fri" autocomplete="off">
                                                <label class="btn btn-outline-primary" for="week-fri">五</label>
                                                
                                                <input type="checkbox" class="btn-check" name="weekdays" value="6" id="week-sat" autocomplete="off">
                                                <label class="btn btn-outline-primary" for="week-sat">六</label>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-link p-0" id="select-weekdays">選擇工作日</button>
                                            <button type="button" class="btn btn-sm btn-link p-0 ms-3" id="select-weekend">選擇週末</button>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="weekly-time" class="form-label required">執行時間</label>
                                        <input type="time" class="form-control" id="weekly-time">
                                        <div id="weekly-auto-adjusted" class="alert alert-info mt-2 d-none">
                                            <i class="fas fa-info-circle"></i> 本週此時間已過，將從下週開始執行
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 每月執行選項 -->
                            <div class="schedule-option monthly-options card shadow-sm mb-3" style="display:none;">
                                <div class="card-body">
                                    <h6 class="card-title mb-3">每月排程設定</h6>
                                    <div class="mb-3">
                                        <label class="form-label required">執行日期</label>
                                        <div class="monthly-calendar">
                                            <div class="d-flex flex-wrap">
                                                {% for day in range(1, 32) %}
                                                <div class="day-selector">
                                                    <input type="checkbox" class="btn-check" name="days_of_month" value="{{ day }}" id="day-{{ day }}" autocomplete="off">
                                                    <label class="btn btn-outline-primary" for="day-{{ day }}">{{ day }}</label>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            <div class="mt-2">
                                                <button type="button" class="btn btn-sm btn-link p-0" id="select-first-day">選擇月初 (1日)</button>
                                                <button type="button" class="btn btn-sm btn-link p-0 ms-3" id="select-mid-day">選擇月中 (15日)</button>
                                                <button type="button" class="btn btn-sm btn-link p-0 ms-3" id="select-last-day">選擇月底 (28-31日)</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="monthly-time" class="form-label required">執行時間</label>
                                        <input type="time" class="form-control" id="monthly-time">
                                        <div id="monthly-auto-adjusted" class="alert alert-info mt-2 d-none">
                                            <i class="fas fa-info-circle"></i> 本月選擇的日期或時間已過，將從下個月開始執行
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal" onclick="closeModal('scheduleModal')">取消</button>
                        <button type="button" class="btn btn-primary" id="save-schedule">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 編輯描述模態框 -->
        <div class="modal" id="editDescriptionModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">編輯腳本資訊</h5>
                        <button type="button" class="btn-close" onclick="toggleModal('editDescriptionModal')"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editDescriptionForm">
                            <input type="hidden" id="editScriptName">
                            <div class="mb-3">
                                <label class="form-label">腳本描述</label>
                                <textarea class="form-control" id="editDescription" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">作者</label>
                                <input type="text" class="form-control" id="editAuthor">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">標籤</label>
                                <input type="text" class="form-control" id="editTags" placeholder="使用逗號分隔多個標籤">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="toggleModal('editDescriptionModal')">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveDescription()">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading遮罩 -->
        <div class="loading">
            <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/schedule.js') }}"></script>
    
    <script>
        // 顯示或隱藏模態窗口
        function toggleModal(modalId) {
            const modalElement = document.getElementById(modalId);
            if (modalElement) {
                // 使用 Bootstrap 的 Modal 方法
                const modal = bootstrap.Modal.getOrCreateInstance(modalElement);
                modal.toggle();
            }
        }
        
        // 關閉模態視窗函數
        function closeModal(modalId) {
            const modalElement = document.getElementById(modalId);
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }
        }
        
        // 載入腳本列表
        function loadScripts() {
            fetch('/script')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const scriptSelect = document.getElementById('script-select');
                        if (scriptSelect) {
                            // 清空下拉選單
                            while (scriptSelect.options.length > 1) {
                                scriptSelect.remove(1);
                            }
                            
                            // 填充腳本選項
                            data.data.forEach(script => {
                                const option = document.createElement('option');
                                option.value = script.name;
                                option.textContent = `${script.name} - ${script.description || ''}`;
                                scriptSelect.appendChild(option);
                            });

                            // 同時更新腳本列表
                            const scriptList = document.getElementById('scriptList');
                            if (scriptList) {
                                scriptList.innerHTML = '';
                                if (data.data.length === 0) {
                                    scriptList.innerHTML = '<div class="text-center my-4">沒有找到腳本</div>';
                                } else {
                                    data.data.forEach(script => {
                                        const item = document.createElement('div');
                                        item.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';
                                        item.innerHTML = `
                                            <div>
                                                <h6 class="mb-1">${script.name}</h6>
                                                <p class="mb-1 small text-muted">${script.description || '無描述'}</p>
                                            </div>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteScript('${script.name}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        `;
                                        scriptList.appendChild(item);
                                    });
                                }
                            }
                        }
                    }
                })
                .catch(error => console.error('載入腳本清單時發生錯誤:', error));
        }
        
        // 頁面載入時執行
        document.addEventListener('DOMContentLoaded', function() {
            // 載入腳本列表
            loadScripts();
            // 更新系統資源
            updateSystemResources();
            
            // 初始化所有模態框
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                new bootstrap.Modal(modal, {
                    backdrop: true,  // 允許點擊背景關閉
                    keyboard: true   // 允許使用 ESC 鍵關閉
                });
            });
            
            // 為其他關閉按鈕添加事件監聽
            document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(button => {
                button.addEventListener('click', function() {
                    const modalId = this.closest('.modal').id;
                    closeModal(modalId);
                });
            });
            
            // 全局事件處理，確保模態框能正確關閉
            document.addEventListener('keydown', function(event) {
                // ESC 鍵關閉當前模態框
                if (event.key === 'Escape') {
                    const openModal = document.querySelector('.modal.show');
                    if (openModal) {
                        closeModal(openModal.id);
                    }
                }
            });
        });

        // 更新系統資源使用狀況
        function updateSystemResources() {
            fetch('/api/v1/system/resources')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const resources = data.data;
                        const cpuPercent = resources.cpu_percent || 0;
                        const memoryPercent = resources.memory_percent || 0;

                        document.getElementById('cpu-usage').style.width = `${cpuPercent}%`;
                        document.getElementById('cpu-usage').textContent = `${cpuPercent.toFixed(1)}%`;

                        document.getElementById('memory-usage').style.width = `${memoryPercent}%`;
                        document.getElementById('memory-usage').textContent = `${memoryPercent.toFixed(1)}%`;
                        
                        const current = resources.schedule_count || 0;
                        const max = resources.max_schedules || 10;
                        document.getElementById('schedule-count').style.width =
                            `${(current / max) * 100}%`;
                        document.getElementById('schedule-count').textContent =
                            `${current}/${max}`;
                    }
                })
                .catch(error => console.error('Error updating system resources:', error));
        }

        // 定期更新系統資源
        setInterval(updateSystemResources, 5000);

        // 刪除腳本
        function deleteScript(scriptName) {
            if (confirm(`確定要刪除腳本 ${scriptName} 嗎？此操作不可逆！`)) {
                fetch(`/script/${scriptName}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert('刪除成功');
                        loadScripts();
                    } else {
                        alert(`刪除失敗: ${data.message}`);
                    }
                })
                .catch(error => {
                    console.error('刪除腳本時發生錯誤:', error);
                    alert('刪除腳本時發生錯誤');
                });
            }
        }
        
        // 保存排程功能已移至 schedule.js 中處理，避免重複事件監聽器
    </script>
</body>
</html>
