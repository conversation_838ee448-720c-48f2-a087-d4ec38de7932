# -*- coding: utf-8 -*-
import psutil
import logging
import os
from typing import Dict, Any
from datetime import datetime
from flask import current_app

logger = logging.getLogger(__name__)

class SystemMonitor:
    """系統資源監控服務"""
    
    def __init__(self, app=None):
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)
        self._initialized = False
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化系統監控服務"""
        self.app = app
        # 確保日誌已設置
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(asctime)s] %(levelname)s in %(module)s: %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        self.logger.debug("系統監控服務初始化完成")
        self._initialized = True
        
        # 將自身添加到 app 上下文
        app.system_monitor = self
    
    def get_system_resources(self):
        """獲取系統資源使用狀況"""
        try:
            # 獲取CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # 獲取記憶體使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 嘗試獲取排程數量
            schedule_count = 0
            try:
                # 嘗試從服務獲取
                from app.services import get_service, has_service
                if has_service('schedule_manager'):
                    schedule_manager = get_service('schedule_manager')
                    schedules = schedule_manager.get_all_schedules()
                    schedule_count = sum(1 for s in schedules if s.get('is_active', False))
                elif hasattr(current_app, 'schedule_manager') and current_app.schedule_manager:
                    schedules = current_app.schedule_manager.get_all_schedules()
                    schedule_count = sum(1 for s in schedules if s.get('is_active', False))
            except Exception as e:
                self.logger.warning(f"獲取排程數量失敗: {str(e)}")
                # 嘗試從數據庫直接查詢
                try:
                    from app.models.schedule import Schedule
                    from flask_sqlalchemy import SQLAlchemy
                    
                    db = SQLAlchemy()
                    schedule_count = Schedule.query.filter_by(is_active=True).count()
                except Exception as db_error:
                    self.logger.warning(f"從數據庫獲取排程數量失敗: {str(db_error)}")
            
            max_schedules = current_app.config.get('MAX_SCHEDULES', 10) if current_app else 10
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'schedule_count': schedule_count,
                'max_schedules': max_schedules
            }
        except Exception as e:
            self.logger.error(f"獲取系統資源失敗: {str(e)}")
            return {
                'cpu_percent': 0,
                'memory_percent': 0,
                'schedule_count': 0,
                'max_schedules': 10,
                'error': str(e)
            }
    
    def get_process_resources(self, pid):
        """獲取特定進程的資源使用狀況"""
        try:
            process = psutil.Process(pid)
            
            return {
                "pid": pid,
                "name": process.name(),
                "cpu_percent": process.cpu_percent(interval=0.1),
                "memory_percent": process.memory_percent(),
                "status": process.status(),
                "create_time": process.create_time()
            }
        except psutil.NoSuchProcess:
            self.logger.warning(f"進程不存在: {pid}")
            return {
                "error": "進程不存在",
                "message": f"PID {pid} 不存在"
            }
        except Exception as e:
            self.logger.error(f"獲取進程資源失敗: {str(e)}")
            return {
                "error": "獲取進程資源失敗",
                "message": str(e)
            }
    
    def get_all_processes(self, limit=50):
        """獲取所有進程列表"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'create_time', 'status']):
                try:
                    pinfo = proc.as_dict(attrs=['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'create_time', 'status'])
                    processes.append(pinfo)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
            
            # 按CPU使用率排序並限制數量
            processes = sorted(processes, key=lambda p: p.get('cpu_percent', 0), reverse=True)
            return processes[:limit]
        except Exception as e:
            self.logger.error(f"獲取進程列表失敗: {str(e)}")
            return [] 