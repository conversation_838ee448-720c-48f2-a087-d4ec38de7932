from flask import jsonify, request, current_app, render_template, abort, send_from_directory
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge
from..scripts.manager import ScriptManager
import os
import traceback
import jinja2
import subprocess
import sys
import json

script_manager = None

def init_script_manager(app):
    """初始化腳本管理器"""
    global script_manager
    if script_manager is None:
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        app.logger.debug(f'Project root: {project_root}')
        script_manager = ScriptManager(project_root)
        app.logger.debug('Script manager initialized')

def allowed_file(filename):
    """檢查文件是否允許上傳"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def init_routes(bp):
    """初始化所有路由"""
    
    @bp.route('/')
    def index():
        """首頁"""
        current_app.logger.info('訪問首頁')
        try:
            template_path = os.path.join(current_app.root_path, 'templates', 'index.html')
            current_app.logger.debug(f'模板路徑: {template_path}')
            current_app.logger.debug(f'模板是否存在: {os.path.exists(template_path)}')
            
            if not os.path.exists(template_path):
                current_app.logger.error(f'首頁模板不存在: {template_path}')
                abort(500, description="Template not found")
            
            current_app.logger.debug('開始渲染模板')
            response = render_template('index.html')
            current_app.logger.debug('模板渲染完成')
            return response
            
        except Exception as e:
            current_app.logger.error(f'渲染首頁時出錯: {str(e)}')
            current_app.logger.error(f'錯誤詳情: {traceback.format_exc()}')
            abort(500)

    @bp.route('/api/scripts', methods=['GET'])
    def list_scripts():
        """列出所有腳本"""
        global script_manager
        if script_manager is None:
            init_script_manager(current_app)
        try:
            scripts = script_manager.list_scripts()
            current_app.logger.debug(f'找到 {len(scripts)} 個腳本')
            return jsonify({
                'status': 'success',
                'data': scripts
            })
        except Exception as e:
            current_app.logger.error(f'列出腳本時發生錯誤: {str(e)}')
            return jsonify({
                'status': 'error',
                'message': '獲取腳本列表失敗'
            }), 500

    @bp.route('/api/scripts/upload', methods=['POST'])
    def upload_script():
        """上傳腳本"""
        global script_manager
        if script_manager is None:
            init_script_manager(current_app)
            
        current_app.logger.debug('處理腳本上傳請求')
        current_app.logger.debug(f'請求方法: {request.method}')
        current_app.logger.debug(f'請求頭: {dict(request.headers)}')
        current_app.logger.debug(f'表單數據: {request.form}')
        current_app.logger.debug(f'文件數據: {request.files}')
        
        try:
            # 檢查是否有文件
            if 'file' not in request.files:
                current_app.logger.warning('未收到文件')
                return jsonify({
                    'status': 'error',
                    'message': '未收到文件'
                }), 400
                
            file = request.files['file']
            if file.filename == '':
                current_app.logger.warning('未選擇文件')
                return jsonify({
                    'status': 'error',
                    'message': '未選擇文件'
                }), 400
                
            # 檢查文件類型
            if not file or not allowed_file(file.filename):
                current_app.logger.warning(f'不允許的文件類型: {file.filename}')
                return jsonify({
                    'status': 'error',
                    'message': '只允許上傳Python腳本文件'
                }), 400

            # 獲取描述等元數據
            description = request.form.get('description', '')
            tag = request.form.get('tag', '')
            
            # 處理標籤
            tags = [tag] if tag else []
            
            current_app.logger.debug(f'處理上傳文件: {file.filename}')
            current_app.logger.debug(f'描述: {description}')
            current_app.logger.debug(f'標籤: {tags}')

            # 保存到臨時文件
            filename = secure_filename(file.filename)
            temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], f'temp_{filename}')
            current_app.logger.debug(f'保存臨時文件到: {temp_path}')
            file.save(temp_path)
            current_app.logger.debug(f'臨時文件保存成功，大小: {os.path.getsize(temp_path)} 字節')
            
            try:
                # 先驗證腳本
                current_app.logger.debug(f'開始驗證腳本: {temp_path}')
                is_valid, validation_result = script_manager.validator.validate_script(temp_path)
                current_app.logger.debug(f'驗證結果: {validation_result}')
                
                if not is_valid:
                    current_app.logger.warning('腳本驗證失敗')
                    return jsonify({
                        'status': 'error',
                        'message': '腳本驗證失敗',
                        'validation_result': validation_result
                    }), 400

                # 上傳腳本
                current_app.logger.debug(f'開始上傳腳本...')
                with open(temp_path, 'rb') as f:
                    result = script_manager.upload_script(
                        f,
                        description=description,
                        tags=tags
                    )
                    
                current_app.logger.debug(f'上傳結果: {result}')
                    
                if result['status'] == 'success':
                    current_app.logger.info(f'成功上傳腳本: {file.filename}')
                    return jsonify({
                        'status': 'success',
                        'message': '上傳成功',
                        'script_info': {
                            'name': result.get("name", ""),
                            'description': result.get("description", ""),
                            'tag': tags[0] if tags else ""
                        }
                    })
                else:
                    current_app.logger.warning(f'上傳腳本失敗: {result["message"]}')
                    return jsonify(result), 400
            finally:
                # 清理臨時文件
                if os.path.exists(temp_path):
                    current_app.logger.debug(f'清理臨時文件: {temp_path}')
                    os.remove(temp_path)
                
        except RequestEntityTooLarge:
            current_app.logger.error('文件大小超過限制')
            return jsonify({
                'status': 'error',
                'message': '文件大小超過限制'
            }), 413
        except Exception as e:
            current_app.logger.error(f'上傳腳本時發生錯誤: {str(e)}')
            current_app.logger.error(f'錯誤詳情: {traceback.format_exc()}')
            return jsonify({
                'status': 'error',
                'message': '上傳失敗',
                'error': str(e)
            }), 500

    @bp.route('/api/scripts/<script_name>', methods=['DELETE'])
    def delete_script(script_name):
        """刪除腳本"""
        global script_manager
        if script_manager is None:
            init_script_manager(current_app)
            
        current_app.logger.debug(f'處理刪除腳本請求: {script_name}')
        
        try:
            success, message = script_manager.delete_script(script_name)
            if success:
                current_app.logger.info(f'成功刪除腳本: {script_name}')
                return jsonify({
                    'status': 'success',
                    'message': message
                })
            else:
                current_app.logger.warning(f'刪除腳本失敗: {message}')
                return jsonify({
                    'status': 'error',
                    'message': message
                }), 404
        except Exception as e:
            current_app.logger.error(f'刪除腳本時發生錯誤: {str(e)}')
            return jsonify({
                'status': 'error',
                'message': '刪除失敗'
            }), 500

    @bp.route('/api/scripts/<script_name>/info', methods=['GET'])
    def get_script_info(script_name):
        """獲取腳本信息"""
        global script_manager
        if script_manager is None:
            init_script_manager(current_app)
            
        current_app.logger.debug(f'獲取腳本信息: {script_name}')
        
        try:
            info = script_manager.get_script_info(script_name)
            if info:
                return jsonify({
                    'status': 'success',
                    'data': info
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': '腳本不存在'
                }), 404
        except Exception as e:
            current_app.logger.error(f'獲取腳本信息時發生錯誤: {str(e)}')
            return jsonify({
                'status': 'error',
                'message': '獲取腳本信息失敗'
            }), 500

    @bp.route('/api/scripts/<script_name>/meta', methods=['PUT'])
    def update_script_meta(script_name):
        """更新腳本元數據"""
        global script_manager
        if script_manager is None:
            init_script_manager(current_app)
            
        current_app.logger.debug(f'更新腳本元數據: {script_name}')
        current_app.logger.debug(f'請求數據: {request.get_json()}')
        
        try:
            data = request.get_json()
            
            # 確保標籤參數的正確處理
            tag = data.get('tag', '')
            if tag is None:
                tag = ""
            
            # 獲取描述，如果未提供則使用空字串
            description = data.get('description')
            if description is None:
                description = ""
                
            # 處理標籤 - 改為單一標籤
            tags = [tag] if tag else []
            
            current_app.logger.debug(f'處理的參數: description={description}, tag={tag}')
                
            success, message = script_manager.update_script_meta(
                script_name,
                description=description,
                tags=tags
            )
            
            if success:
                current_app.logger.debug(f'更新成功')
                return jsonify({
                    'status': 'success',
                    'message': message
                })
            else:
                current_app.logger.error(f'更新失敗: {message}')
                return jsonify({
                    'status': 'error',
                    'message': message
                }), 400
        except Exception as e:
            current_app.logger.error(f'更新腳本元數據時發生錯誤: {str(e)}')
            current_app.logger.error(f'錯誤詳情: {traceback.format_exc()}')
            return jsonify({
                'status': 'error',
                'message': '更新失敗'
            }), 500

    @bp.route('/api/scripts/search', methods=['GET'])
    def search_scripts():
        """搜索腳本"""
        global script_manager
        if script_manager is None:
            init_script_manager(current_app)
            
        query = request.args.get('q', '').strip()
        current_app.logger.debug(f'搜索腳本: {query}')
        
        try:
            results = script_manager.search_scripts(query)
            current_app.logger.debug(f'搜索結果數量: {len(results)}')
            return jsonify({
                'status': 'success',
                'data': results
            })
        except Exception as e:
            current_app.logger.error(f'搜索腳本時發生錯誤: {str(e)}')
            return jsonify({
                'status': 'error',
                'message': '搜索失敗'
            }), 500

    @bp.route('/api/scripts/validate', methods=['POST'])
    def validate_script():
        """驗證腳本"""
        global script_manager
        if script_manager is None:
            init_script_manager(current_app)
        
        current_app.logger.debug('開始處理腳本驗證請求')
        
        try:
            # 檢查是否有文件
            if 'file' not in request.files:
                current_app.logger.warning('未收到文件')
                return jsonify({
                    'status': 'error',
                    'message': '未收到文件'
                }), 400
            
            file = request.files['file']
            if file.filename == '':
                current_app.logger.warning('未選擇文件')
                return jsonify({
                    'status': 'error',
                    'message': '未選擇文件'
                }), 400
            
            # 檢查文件類型
            if not file or not allowed_file(file.filename):
                current_app.logger.warning(f'不允許的文件類型: {file.filename}')
                return jsonify({
                    'status': 'error',
                    'message': '只允許上傳Python腳本文件'
                }), 400

            # 確保臨時目錄存在
            temp_dir = current_app.config['UPLOAD_FOLDER']
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
                current_app.logger.debug(f'創建臨時目錄: {temp_dir}')

            # 保存到臨時文件進行驗證
            filename = secure_filename(file.filename)
            temp_path = os.path.join(temp_dir, f'temp_{filename}')
            
            current_app.logger.debug(f'保存臨時文件: {temp_path}')
            file.save(temp_path)
            
            try:
                current_app.logger.debug('開始驗證腳本')
                is_valid, validation_result = script_manager.validator.validate_script(temp_path)
                current_app.logger.debug(f'驗證結果: {validation_result}')
                
                # 為前端返回格式化的結果
                return jsonify({
                    'status': 'success',
                    'errors': validation_result.get('errors', []),
                    'warnings': validation_result.get('warnings', []),
                    'missing_packages': validation_result.get('missing_packages', []),
                    'required_packages': validation_result.get('required_packages', []),
                    'has_severe_errors': validation_result.get('has_severe_errors', False),
                    'has_missing_packages': validation_result.get('has_missing_packages', False)
                })
                
            finally:
                # 清理臨時文件
                try:
                    if os.path.exists(temp_path):
                        current_app.logger.debug(f'清理臨時文件: {temp_path}')
                        os.remove(temp_path)
                except Exception as e:
                    current_app.logger.error(f'清理臨時文件失敗: {str(e)}')
        
        except Exception as e:
            current_app.logger.error(f'驗證腳本時發生錯誤: {str(e)}')
            current_app.logger.error(f'錯誤詳情: {traceback.format_exc()}')
            return jsonify({
                'status': 'error',
                'message': '驗證失敗: ' + str(e),
                'errors': ['驗證過程中發生錯誤: ' + str(e)],
                'warnings': [],
                'required_packages': []
            }), 500

    @bp.route('/api/scripts/install-dependencies', methods=['POST'])
    def install_dependencies():
        """安裝腳本依賴"""
        try:
            data = request.get_json()
            dependencies = data.get('dependencies', [])
            
            if not dependencies:
                return jsonify({
                    'status': 'error',
                    'message': '未指定依賴包'
                }), 400
                
            # 使用 pip 安裝依賴
            for pkg in dependencies:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', pkg])
                
            return jsonify({
                'status': 'success',
                'message': '依賴安裝完成'
            })
        except Exception as e:
            current_app.logger.error(f'安裝依賴時發生錯誤: {str(e)}')
            return jsonify({
                'status': 'error',
                'message': f'安裝依賴失敗: {str(e)}'
            }), 500

    @bp.errorhandler(404)
    def not_found_error(error):
        current_app.logger.error(f'404錯誤: {str(error)}')
        return jsonify({
            'status': 'error',
            'message': '請求的資源不存在'
        }), 404

    @bp.errorhandler(500)
    def server_error(error):
        current_app.logger.error(f'服務器錯誤: {str(error)}')
        return jsonify({
            'status': 'error',
            'message': '服務器錯誤'
        }), 500
