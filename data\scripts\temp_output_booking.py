import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
import time
import re
import os
import warnings
import sys

# 確保輸出為 UTF-8，防止多語言環境下亂碼
sys.stdout.reconfigure(encoding='utf-8')

# 忽略 SSL 憑證警告
warnings.filterwarnings("ignore", message="Unverified HTTPS request")

# 儲存已經爬取過的網址，避免重複
visited_urls = set()

# 基本的網站 URL 和域名
base_url = 'https://booking.farglory-hotel.com.tw/Farglory/FG-H/'
base_domain = urlparse(base_url).netloc

# 設定儲存文件的目錄
output_dir = 'output\output_booking'
os.makedirs(output_dir, exist_ok=True)

# 定義 URL 轉合法文件名
def url_to_filename(url, prefix="booking_"):
    # 加上前置字串 "output_"，並處理不合法字符
    return prefix + (re.sub(r'[\/:*?"<>|]', '_', urlparse(url).path.strip('/')) or 'index')

# 定義一個函數，負責擷取單個網頁的內容並剔除不必要的部分
def scrape_page(url):
    if url in visited_urls:
        print(f"跳過已訪問頁面：{url}")
        return

    print(f"正在爬取: {url}")
    visited_urls.add(url)

    # 發送 GET 請求來抓取網頁內容
    try:
        response = requests.get(url, verify=False)  # 忽略 SSL 憑證
        response.raise_for_status()  # 若請求失敗則引發異常
    except requests.RequestException as e:
        print(f"無法抓取 {url}，錯誤: {e}")
        return

    # 解析內容
    soup = BeautifulSoup(response.text, 'html.parser')

    # 移除頁首、頁腳、選單等不必要的區塊
    for tag in ['header', 'footer', 'nav', 'aside','head']:
        for element in soup.find_all(tag):
            element.decompose()

    # 剔除特定的 class 區塊
    for class_name in ["nav-link", "room-bg", "modal-header", "modal-body", "modal-footer", "modal-tenant-content-copy", "child-age-copy col-auto", "alert-text", "col-12 col-lg-3 my-lg-auto"]:
        for element in soup.find_all(class_=class_name):
            element.decompose()

    # 擷取主要內容的文字，保留正常段落間的單一換行
    main_content = soup.get_text(separator="")

    # 將段落之間超過 5 行的空白縮減為 3 行
    main_content = re.sub(r'\n{6,}', '\n\n\n', main_content).strip()

    # 生成文件名，基於網頁名稱並加上前置字串
    filename = os.path.join(output_dir, url_to_filename(url) + '.txt')
    with open(filename, 'w', encoding='utf-8') as file:
        file.write(f"--- 擷取自 {url} ---\n\n")
        file.write(main_content)

    print(f"內容已保存到: {filename}")

    # 尋找所有子頁面的鏈接
    for link in soup.find_all('a', href=True):
        sub_url = link['href']
        sub_url = urljoin(base_url, sub_url)  # 處理相對路徑

        # 解析 URL，確認網域是否與目標網站相同
        sub_url_domain = urlparse(sub_url).netloc
        if sub_url_domain != base_domain:
            continue

        # 遞迴爬取每一個子網頁
        print(f"將遞迴爬取子頁面: {sub_url}")
        scrape_page(sub_url)
        time.sleep(1)  # 避免請求過快，減少伺服器壓力

# 從首頁開始爬取
if __name__ == "__main__":
    scrape_page(base_url)
    print("所有頁面已被爬取並分別保存到指定目錄。")
