/* Upload Zone Styles */
.upload-zone {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: var(--bg-main);
    cursor: pointer;
}

.upload-zone:hover {
    border-color: var(--btn-primary-text);
    background: var(--btn-primary-bg);
}

.upload-zone.drag-over {
    border-color: var(--btn-primary-text);
    background: var(--btn-primary-hover);
    transform: scale(1.02);
}

.upload-prompt {
    color: var(--text-main);
}

.upload-prompt i {
    color: var(--btn-primary-text);
    margin-bottom: 1rem;
}

/* Progress Bar */
.progress {
    height: 0.5rem;
    border-radius: 1rem;
    background: var(--border-color);
    overflow: hidden;
}

.progress-bar {
    background: var(--btn-primary-text);
    transition: width 0.3s ease-in-out;
}

/* File Info Card */
.card {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: var(--text-main);
    font-size: var(--font-size-header);
    margin-bottom: 1rem;
}

/* Search Input */
.input-group {
    display: none;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
}

.modal.show {
    display: block !important;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 1.75rem auto;
    max-width: 800px;
    pointer-events: auto;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    pointer-events: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.btn-close {
    box-sizing: content-box;
    width: 1em;
    height: 1em;
    padding: 0.25em;
    color: #000;
    background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
    border: 0;
    border-radius: 0.25rem;
    opacity: .5;
    cursor: pointer;
    margin: -0.5rem -0.5rem -0.5rem auto;
}

.btn-close:hover {
    opacity: .75;
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    gap: 0.5rem;
}

/* 確保模態框內容可以點擊 */
.modal-dialog {
    pointer-events: auto;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
}

.modal-backdrop.show {
    opacity: 0.5;
}

/* 上傳視圖樣式 */
#uploadView {
    display: none;
}

#uploadView.show {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

/* 確保列表視圖可以點擊 */
#scriptListView {
    pointer-events: auto;
}

/* Form Controls */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--btn-primary-text);
    box-shadow: 0 0 0 3px rgba(15, 118, 110, 0.1);
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

/* Buttons */
.btn {
    padding: 0.5rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 120px;
    justify-content: center;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: var(--btn-primary-bg);
    color: var(--btn-primary-text);
    border: none;
}

.btn-primary:hover {
    background: var(--btn-primary-hover);
}

.btn-outline-primary {
    border: 1px solid var(--btn-primary-text);
    color: var(--btn-primary-text);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--btn-primary-bg);
}

/* 確保按鈕在禁用狀態下保持相同大小 */
.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 確保按鈕內的圖標和文字對齊 */
.btn i {
    font-size: 1rem;
    line-height: 1;
}

/* 確保按鈕文字大小一致 */
.btn span {
    font-size: 0.95rem;
}

/* List Group */
.list-group-item {
    border: 1px solid var(--border-color);
    margin-bottom: -1px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.list-group-item:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.list-group-item:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    margin-bottom: 0;
}

.list-group-item:hover {
    background: var(--hover-bg);
}

.script-name {
    font-weight: 500;
    color: var(--text-main);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out forwards;
}

/* Loading Overlay */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading.show {
    display: flex;
}

/* Custom Variables */
:root {
    --font-size-normal: 14px;
    --font-size-small: 13px;
    --font-size-title: 18px;
    --font-size-header: 16px;
    
    --bg-main: #f7f9fc;
    --text-main: #334155;
    --border-color: #e2e8f0;
    --hover-bg: #f1f5f9;
    
    --btn-primary-bg: #e0f2f1;
    --btn-primary-text: #0f766e;
    --btn-primary-hover: #ccfbf1;
}

/* Base Styles */
body {
    background-color: var(--bg-main);
    color: var(--text-main);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-size: var(--font-size-normal);
    line-height: 1.5;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .upload-zone {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.4rem 0.8rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

/* File Preview Styles */
.file-preview {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    background: white;
    transition: all 0.3s ease;
}

.file-preview:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-preview .card {
    margin-bottom: 0;
}

.file-preview .btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
}

.file-preview .btn-outline-danger:hover {
    background-color: #dc3545;
    color: white;
}

/* Upload Zone Active State */
.upload-zone.drag-over {
    border-color: var(--btn-primary-text);
    background-color: var(--btn-primary-bg);
    transform: scale(1.02);
}

/* Progress Bar Animation */
.progress-bar {
    transition: width 0.3s ease-in-out;
}

/* Stepper Styles */
.stepper {
    position: relative;
    padding: 0 20px;
}

.stepper::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--border-color);
    z-index: 1;
}

.step {
    position: relative;
    z-index: 2;
    background: var(--bg-main);
    padding: 0 10px;
    text-align: center;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--border-color);
    color: var(--text-main);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.step.active .step-icon {
    background: var(--btn-primary-text);
    color: white;
}

.step.completed .step-icon {
    background: var(--btn-primary-text);
    color: white;
}

.step.completed .step-icon::after {
    content: '✓';
    font-size: 20px;
}

.step-label {
    font-size: var(--font-size-small);
    color: var(--text-main);
    transition: all 0.3s ease;
}

.step.active .step-label {
    color: var(--btn-primary-text);
    font-weight: bold;
}

/* Step Content */
.step-content {
    position: relative;
    min-height: 300px;
}

.step-pane {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.step-pane.active {
    display: block;
    opacity: 1;
}

/* Validation Results */
.validation-errors-list,
.validation-warnings-list {
    padding-left: 20px;
    margin-bottom: 0;
    color: inherit;
}

.validation-errors-list li {
    color: var(--danger);
    margin-bottom: 5px;
}

.validation-warnings-list li {
    color: var(--warning);
    margin-bottom: 5px;
}

/* Dependencies List */
.dependencies-list {
    max-height: 200px;
    overflow-y: auto;
}

.dependency-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-color);
}

.dependency-item:last-child {
    border-bottom: none;
}

.dependency-status {
    font-size: var(--font-size-small);
    padding: 2px 8px;
    border-radius: 12px;
}

.dependency-status.installed {
    background: #d1fae5;
    color: #065f46;
}

.dependency-status.missing {
    background: #fee2e2;
    color: #991b1b;
}

/* 步驟切換動畫 */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-out {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.fade-in {
    animation: fade-in 0.3s ease-out forwards;
}

.fade-out {
    animation: fade-out 0.3s ease-out forwards;
}

/* 步驟指示器樣式 */
.step-indicator {
    transition: all 0.3s ease;
}

.step-indicator.active {
    transform: scale(1.1);
}

.step-indicator.completed {
    background-color: #10B981;
    border-color: #10B981;
}

.step-title {
    transition: all 0.3s ease;
}

.step-title.active {
    color: #10B981;
    font-weight: bold;
}

.step-title.completed {
    color: #10B981;
}

/* 步驟內容樣式 */
.step-content {
    display: none;
}

.step-content.active {
    display: block;
}

/* 按鈕過渡效果 */
.btn {
    transition: all 0.3s ease;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.drop-zone {
    min-height: 300px;
    border: 2px dashed #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f8fafc;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.drop-zone:hover:not(.file-selected):not(.error) {
    border-color: #3b82f6;
    background-color: #eff6ff;
    transform: translateY(-2px);
}

.drop-zone.dragover:not(.file-selected):not(.error) {
    border-color: #3b82f6;
    background-color: #eff6ff;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.drop-zone .upload-icon {
    margin-bottom: 1.5rem;
    color: #64748b;
    transition: all 0.3s ease;
}

.drop-zone:hover:not(.file-selected):not(.error) .upload-icon {
    color: #3b82f6;
    transform: translateY(-5px);
}

.drop-zone .upload-text {
    text-align: center;
    color: #475569;
}

.drop-zone .upload-hint {
    margin-top: 1rem;
    color: #94a3b8;
    font-size: 0.875rem;
}

/* 文件選擇成功狀態 */
.drop-zone.file-selected {
    border-color: #10b981;
    background-color: #f0fdf4;
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

.file-success-icon {
    color: #10b981;
    margin-bottom: 1.5rem;
}

.file-success-message {
    text-align: center;
}

.file-details {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.file-name {
    font-weight: 500;
    color: #1e293b;
}

.file-size {
    color: #64748b;
    font-size: 0.875rem;
}

/* 錯誤狀態 */
.drop-zone.error {
    border-color: #ef4444;
    background-color: #fef2f2;
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.error-icon {
    color: #ef4444;
    margin-bottom: 1.5rem;
}

.error-message {
    text-align: center;
}

/* 動畫效果 */
.bounce-in {
    animation: bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 按鈕樣式 */
.btn-outline-primary {
    color: #3b82f6;
    border-color: #3b82f6;
    background-color: transparent;
    transition: all 0.2s ease;
}

.btn-outline-primary:hover {
    color: white;
    background-color: #3b82f6;
    transform: translateY(-1px);
}

.btn-outline-secondary {
    color: #64748b;
    border-color: #e2e8f0;
    background-color: white;
    transition: all 0.2s ease;
}

.btn-outline-secondary:hover {
    color: #1e293b;
    border-color: #94a3b8;
    transform: translateY(-1px);
}

/* 步驟指示器動畫 */
.step-indicator {
    transition: all 0.3s ease;
}

.step-indicator.completed {
    animation: completedStep 0.5s ease-out;
}

@keyframes completedStep {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.pulse-animation {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

.bg-success-light {
    background-color: #d1fae5 !important;
}

.border-success {
    border-color: #10B981 !important;
}

.pulse-animation {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

.drop-zone {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
}

.drop-zone.border-success {
    border-width: 2px;
}

.drop-zone i {
    transition: all 0.3s ease;
}

.drop-zone .fa-check-circle {
    color: #10B981;
}

.text-success {
    color: #10B981 !important;
}

.text-muted {
    color: #6B7280 !important;
}

.small {
    font-size: 0.875rem;
}

.file-selected-animation {
    animation: fileSelectedBounce 0.5s ease-in-out;
}

@keyframes fileSelectedBounce {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.file-success-message {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-details {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.file-name {
    font-weight: 500;
    color: #1a73e8;
}

.file-size {
    color: #5f6368;
}

.selected-file {
    transform-origin: top;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#resetFileBtn {
    transition: all 0.2s ease;
}

#resetFileBtn:hover {
    background-color: #dc3545;
    color: white;
    transform: translateY(-1px);
}

.drop-zone.border-success {
    border-color: #10B981;
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

.bg-success-light {
    background-color: rgba(16, 185, 129, 0.1);
}

.text-success {
    color: #10B981 !important;
}

.validation-results {
    animation: fadeIn 0.5s ease-out;
}

.validation-results .alert {
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left-width: 4px;
}

.validation-results .alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
    color: #721c24;
}

.validation-results .alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-left-color: #ffc107;
    color: #856404;
}

.validation-results .alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-left-color: #17a2b8;
    color: #0c5460;
}

.validation-results .alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left-color: #28a745;
    color: #155724;
}

.validation-results li {
    display: flex;
    align-items: flex-start;
    padding: 0.5rem 0;
}

.validation-results li i {
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.validation-results li span {
    margin-left: 0.5rem;
}

/* 驗證中的動畫 */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.75;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.75;
    }
}

.spinner-border {
    animation: spinner-border 1s linear infinite, pulse 2s ease-in-out infinite;
}

/* 按鈕動畫 */
.btn .spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.15em;
}

.btn-pulse {
    animation: button-pulse 1.5s infinite;
}

@keyframes button-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

/* 上傳視圖樣式 */
#uploadView {
    display: none;
}

.step-contents {
    min-height: 300px;
}

.step-content {
    display: none;
}

.step-content.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 拖放區域樣式 */
.drop-zone {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #f8fafc;
    cursor: pointer;
}

.drop-zone.dragover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.drop-zone.file-selected {
    border-style: solid;
    border-color: #10b981;
    background-color: #f0fdf4;
}

.drop-zone.error {
    border-color: #ef4444;
    background-color: #fef2f2;
}

/* 動畫效果 */
.bounce-in {
    animation: bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 步驟指示器樣式 */
.stepper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.stepper::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e5e7eb;
    z-index: 1;
}

.step-indicator {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #6b7280;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.step-indicator.active {
    border-color: #3b82f6;
    color: #3b82f6;
    background-color: #eff6ff;
}

.step-indicator.completed {
    border-color: #10b981;
    background-color: #10b981;
    color: #fff;
}

.step-indicator.error {
    border-color: #ef4444;
    background-color: #ef4444;
    color: #fff;
}

.step-title {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
    transition: all 0.3s ease;
}

.step-title.active {
    color: #3b82f6;
    font-weight: 600;
}

.step-title.completed {
    color: #10b981;
}

.step-title.error {
    color: #ef4444;
}

.script-item {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    background-color: white;
}

.script-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.script-description {
    color: #666;
    margin-bottom: 10px;
}

.script-tags {
    margin-bottom: 10px;
}

.tag {
    display: inline-block;
    padding: 2px 8px;
    margin-right: 5px;
    border-radius: 3px;
    background-color: #e9ecef;
    color: #495057;
    font-size: 0.9em;
}

.script-buttons {
    display: flex;
    gap: 5px;
    justify-content: flex-end;
}

.script-buttons button {
    padding: 4px 8px;
}

/* 標籤按鈕樣式 */
.tag-btn {
    display: inline-block;
    padding: 5px 10px;
    margin: 5px;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    background-color: #f8f9fa;
    transition: all 0.2s;
}

.tag-btn.active {
    background-color: #007bff;
    color: white;
    border-color: #0056b3;
}

/* 腳本列表項目樣式 */
.list-group-item {
    transition: all 0.2s ease;
}

.list-group-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* 腳本標籤樣式 */
.script-tag .badge {
    font-size: 0.9rem !important;
    font-weight: 500;
    letter-spacing: 0.3px;
    text-transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.script-tag .badge i {
    font-size: 0.85rem;
}

/* 腳本信息樣式 */
.script-info {
    flex-grow: 1;
    min-width: 0; /* 防止文字溢出 */
}

.script-info h6 {
    margin-bottom: 0.25rem;
    color: #1a202c;
}

.script-info p {
    color: #4a5568 !important;
    margin-bottom: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 刪除按鈕樣式 */
.btn-outline-danger {
    border-width: 1.5px;
    font-weight: 500;
    padding: 0.375rem 1rem;
}

.btn-outline-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

/* 新增排程彈窗樣式 */
.schedule-option {
    transition: all 0.3s ease;
}

.required:after {
    content: " *";
    color: #dc3545;
}

.system-resources .progress {
    height: 8px;
    margin-top: 4px;
}

/* 週間選擇按鈕組 */
.weekday-selector .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.weekday-selector .btn {
    min-width: auto;
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 月份日期選擇器 */
.monthly-calendar {
    display: flex;
    flex-direction: column;
}

.monthly-calendar .d-flex {
    gap: 4px;
    flex-wrap: wrap;
}

.day-selector {
    margin-bottom: 4px;
}

.day-selector .btn {
    min-width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 下次執行時間預覽列表 */
.next-execution-times {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.next-execution-times .list-group-item {
    padding: 0.5rem 1rem;
    border: none;
    border-bottom: 1px solid #f0f0f0;
}

.next-execution-times .list-group-item:last-child {
    border-bottom: none;
}

/* 無效輸入反饋 */
.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #dc3545;
}

.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

/* 時間範圍選擇器 */
input[type="range"].form-range {
    -webkit-appearance: none;
    width: 100%;
    height: 10px;
    border-radius: 5px;
    background: #dee2e6;
    outline: none;
    padding: 0;
    margin: 0;
}

input[type="range"].form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--btn-primary-text);
    cursor: pointer;
    transition: all .2s ease-in-out;
}

input[type="range"].form-range::-webkit-slider-thumb:hover {
    background: var(--btn-primary-hover);
    transform: scale(1.1);
}

/* 衝突警告提示 */
#datetime-warning {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

#datetime-warning i {
    font-size: 1.1rem;
}

/* 設定卡片 */
.schedule-option.card {
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.schedule-option.card:hover {
    border-color: var(--btn-primary-text);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 資源監控樣式 */
.resource-meter {
    margin-bottom: 8px;
}

.resource-meter span {
    display: block;
    margin-bottom: 4px;
    font-size: 0.85rem;
}

.resource-meter .progress {
    height: 20px;
    position: relative;
}

.resource-meter .progress-bar {
    background-color: var(--btn-primary-text);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: visible;
    z-index: 1;
}

.resource-meter .progress-bar::after {
    content: attr(data-content);
    position: absolute;
    right: 5px;
    color: #fff;
    font-weight: 500;
    z-index: 2;
}

/* 資源警告閾值樣式 */
.resource-meter .progress-bar.warning {
    background-color: #ffc107;
}

.resource-meter .progress-bar.danger {
    background-color: #dc3545;
}
