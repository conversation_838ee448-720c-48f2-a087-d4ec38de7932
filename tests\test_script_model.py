#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script模型單元測試
"""
import os
import unittest
import tempfile
from datetime import datetime
from app import create_app, db
from app.models import Script

class TestScriptModel(unittest.TestCase):
    """Script模型測試類"""
    
    def setUp(self):
        """測試前準備"""
        # 建立測試用應用程式
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 建立測試資料庫
        db.create_all()
        
        # 創建臨時測試文件
        self.temp_dir = tempfile.mkdtemp()
        self.test_script_path = os.path.join(self.temp_dir, 'test_script.py')
        with open(self.test_script_path, 'w', encoding='utf-8') as f:
            f.write('print("Hello, World!")')
    
    def tearDown(self):
        """測試後清理"""
        # 清理臨時文件
        if os.path.exists(self.test_script_path):
            os.remove(self.test_script_path)
        if os.path.exists(self.temp_dir):
            os.rmdir(self.temp_dir)
        
        # 刪除測試資料庫
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_script_creation(self):
        """測試腳本創建"""
        script = Script(
            name='test_script.py',
            description='測試腳本',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()
        
        # 驗證基本屬性
        self.assertIsNotNone(script.id)
        self.assertEqual(script.name, 'test_script.py')
        self.assertEqual(script.description, '測試腳本')
        self.assertEqual(script.file_path, self.test_script_path)
        self.assertTrue(script.is_active)
        self.assertIsNotNone(script.created_at)
        self.assertIsNotNone(script.updated_at)
    
    def test_script_unique_name_constraint(self):
        """測試腳本名稱唯一性約束"""
        # 創建第一個腳本
        script1 = Script(
            name='duplicate_name.py',
            description='第一個腳本',
            file_path=self.test_script_path
        )
        db.session.add(script1)
        db.session.commit()
        
        # 嘗試創建同名腳本
        script2 = Script(
            name='duplicate_name.py',
            description='第二個腳本',
            file_path=self.test_script_path
        )
        db.session.add(script2)
        
        # 應該拋出完整性錯誤
        with self.assertRaises(Exception):
            db.session.commit()
    
    def test_script_to_dict(self):
        """測試to_dict方法"""
        script = Script(
            name='test_script.py',
            description='測試腳本',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()
        
        script_dict = script.to_dict()
        
        # 驗證字典內容
        self.assertIsInstance(script_dict, dict)
        self.assertEqual(script_dict['name'], 'test_script.py')
        self.assertEqual(script_dict['description'], '測試腳本')
        self.assertEqual(script_dict['file_path'], self.test_script_path)
        self.assertTrue(script_dict['is_active'])
        self.assertIn('created_at', script_dict)
        self.assertIn('updated_at', script_dict)
        
        # 驗證時間格式
        self.assertIsInstance(script_dict['created_at'], str)
        self.assertIsInstance(script_dict['updated_at'], str)
    
    def test_script_from_dict(self):
        """測試from_dict方法"""
        data = {
            'name': 'from_dict_script.py',
            'description': '從字典創建的腳本',
            'file_path': self.test_script_path,
            'is_active': False
        }
        
        script = Script.from_dict(data)
        
        # 驗證屬性
        self.assertEqual(script.name, 'from_dict_script.py')
        self.assertEqual(script.description, '從字典創建的腳本')
        self.assertEqual(script.file_path, self.test_script_path)
        self.assertFalse(script.is_active)
    
    def test_script_update_file_info(self):
        """測試update_file_info方法"""
        script = Script(
            name='test_script.py',
            description='測試腳本',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()
        
        original_updated_at = script.updated_at
        
        # 等待一小段時間確保時間戳不同
        import time
        time.sleep(0.1)
        
        # 調用update_file_info
        script.update_file_info()
        
        # 驗證updated_at已更新
        self.assertGreater(script.updated_at, original_updated_at)
    
    def test_script_default_values(self):
        """測試默認值"""
        script = Script(
            name='default_test.py',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()
        
        # 驗證默認值
        self.assertIsNone(script.description)
        self.assertTrue(script.is_active)
        self.assertIsNotNone(script.created_at)
        self.assertIsNotNone(script.updated_at)
    
    def test_script_update_timestamp(self):
        """測試更新時間戳自動更新"""
        script = Script(
            name='timestamp_test.py',
            description='時間戳測試',
            file_path=self.test_script_path
        )
        db.session.add(script)
        db.session.commit()
        
        original_updated_at = script.updated_at
        
        # 等待一小段時間
        import time
        time.sleep(0.1)
        
        # 更新描述
        script.description = '更新後的描述'
        db.session.commit()
        
        # 驗證updated_at已自動更新
        self.assertGreater(script.updated_at, original_updated_at)

if __name__ == '__main__':
    unittest.main()
