from flask_socketio import emit, join_room, leave_room
from .. import socketio
import logging

logger = logging.getLogger(__name__)

@socketio.on('connect')
def handle_connect():
    """處理客戶端連接"""
    logger.info('客戶端已連接')

@socketio.on('disconnect')
def handle_disconnect():
    """處理客戶端斷開連接"""
    logger.info('客戶端已斷開連接')

@socketio.on('join_schedule')
def handle_join_schedule(data):
    """加入特定排程的房間"""
    schedule_id = data.get('schedule_id')
    if schedule_id:
        join_room(f'schedule_{schedule_id}')
        logger.info(f'客戶端加入排程房間: {schedule_id}')

@socketio.on('leave_schedule')
def handle_leave_schedule(data):
    """離開特定排程的房間"""
    schedule_id = data.get('schedule_id')
    if schedule_id:
        leave_room(f'schedule_{schedule_id}')
        logger.info(f'客戶端離開排程房間: {schedule_id}')

@socketio.on('schedule_status_update')
def handle_status_update(data):
    """處理排程狀態更新"""
    try:
        schedule_id = data.get('schedule_id')
        status = data.get('status')
        timestamp = data.get('timestamp')
        
        if schedule_id and status:
            # 發送到特定排程房間
            emit('status_update', {
                'schedule_id': schedule_id,
                'status': status,
                'timestamp': timestamp
            }, room=f'schedule_{schedule_id}')
            
            # 同時發送到所有客戶端
            emit('status_update', {
                'schedule_id': schedule_id,
                'status': status,
                'timestamp': timestamp
            }, broadcast=True)
            
            logger.debug(f'排程 {schedule_id} 狀態更新: {status}')
            
    except Exception as e:
        logger.error(f'處理狀態更新時發生錯誤: {str(e)}')

@socketio.on('system_resource_update')
def handle_resource_update(data):
    """處理系統資源更新"""
    try:
        emit('resource_update', data, broadcast=True)
        logger.debug('系統資源更新已發送')
    except Exception as e:
        logger.error(f'處理資源更新時發生錯誤: {str(e)}')

@socketio.on('schedule_log_update')
def handle_log_update(data):
    """處理排程日誌更新"""
    try:
        schedule_id = data.get('schedule_id')
        log_data = data.get('log')
        
        if schedule_id and log_data:
            # 發送到特定排程房間
            emit('log_update', {
                'schedule_id': schedule_id,
                'log': log_data
            }, room=f'schedule_{schedule_id}')
            
            logger.debug(f'排程 {schedule_id} 日誌更新')
            
    except Exception as e:
        logger.error(f'處理日誌更新時發生錯誤: {str(e)}') 