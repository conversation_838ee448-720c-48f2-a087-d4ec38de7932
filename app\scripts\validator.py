"""腳本驗證器"""
import ast
import sys
import importlib
import pkg_resources
from typing import List, Dict, <PERSON>ple
import os

class ScriptValidator:
    """腳本驗證器類"""
    
    def __init__(self, python_version: str = None):
        self.python_version = python_version or f"{sys.version_info.major}.{sys.version_info.minor}"
        self.installed_packages = {pkg.key: pkg.version for pkg in pkg_resources.working_set}
    
    def validate_script(self, script_path: str) -> Tuple[bool, Dict]:
        """驗證腳本"""
        result = {
            'syntax_valid': False,
            'imports_valid': False,
            'environment_compatible': False,
            'errors': [],        # 嚴重錯誤
            'warnings': [],      # 警告信息
            'missing_packages': [],  # 缺失的依賴包
            'required_packages': []  # 需要安裝的包名稱
        }
        
        try:
            # 檢查文件是否存在
            if not os.path.exists(script_path):
                result['errors'].append('文件不存在')
                return False, result
            
            # 讀取腳本內容
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 語法檢查
            syntax_valid, syntax_errors = self._check_syntax(content)
            result['syntax_valid'] = syntax_valid
            if syntax_errors:
                result['errors'].extend(syntax_errors)
            
            if syntax_valid:
                # 解析 AST
                tree = ast.parse(content)
                
                # 檢查導入
                imports = self._get_imports(tree)
                imports_valid, import_errors, required_pkgs = self._check_imports(imports)
                result['imports_valid'] = imports_valid
                
                # 提取依賴包信息
                missing_pkgs = [pkg['name'] for pkg in required_pkgs if pkg['status'] == 'missing']
                result['required_packages'] = missing_pkgs
                
                # 生成缺失依賴包提示信息
                if missing_pkgs:
                    for pkg in missing_pkgs:
                        result['missing_packages'].append(f"缺少依賴包: {pkg}")
                
                # 添加導入相關的錯誤
                if import_errors:
                    result['errors'].extend(import_errors)
                
                # 檢查環境兼容性
                env_compatible, env_errors = self._check_environment_compatibility(tree)
                result['environment_compatible'] = env_compatible
                if env_errors:
                    result['warnings'].extend(env_errors)
                
                # 如果只有警告，不影響驗證結果
                result['environment_compatible'] = True
            
        except Exception as e:
            result['errors'].append(f"驗證過程發生錯誤: {str(e)}")
        
        # 定義不同級別的驗證結果
        has_errors = len(result['errors']) > 0  # 嚴重錯誤
        has_missing_packages = len(result['missing_packages']) > 0  # 缺失依賴包
        
        # 判斷驗證是否通過（有嚴重錯誤則不通過）
        is_valid = (result['syntax_valid'] and 
                    result['imports_valid'] and 
                    result['environment_compatible'] and 
                    not has_errors)
        
        # 簡化結果格式
        simplified_result = {
            'errors': result['errors'],
            'warnings': result['warnings'],
            'missing_packages': result['missing_packages'],
            'required_packages': result['required_packages'],
            'has_severe_errors': has_errors,  # 是否有嚴重錯誤
            'has_missing_packages': has_missing_packages  # 是否缺少依賴包
        }
        
        return is_valid, simplified_result
    
    def _check_syntax(self, content: str) -> Tuple[bool, List[str]]:
        """檢查語法"""
        try:
            ast.parse(content)
            return True, []
        except SyntaxError as e:
            return False, [f"語法錯誤: 第 {e.lineno} 行, {str(e)}"]
        except Exception as e:
            return False, [f"語法檢查時發生錯誤: {str(e)}"]
    
    def _get_imports(self, tree: ast.AST) -> List[str]:
        """獲取所有導入的模組"""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for name in node.names:
                    imports.append(name.name.split('.')[0])
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module.split('.')[0])
        
        return list(set(imports))
    
    def _check_imports(self, imports: List[str]) -> Tuple[bool, List[str], List[Dict]]:
        """檢查導入的模組是否可用"""
        errors = []
        missing_packages = []  # 缺失的依賴包，不作為錯誤而是單獨處理
        required_packages = []
        
        for module_name in imports:
            try:
                # 排除標準庫
                if self._is_stdlib_module(module_name):
                    continue
                
                # 檢查第三方包
                spec = importlib.util.find_spec(module_name)
                if spec is None:
                    missing_packages.append(f"缺少依賴包: {module_name}")
                    required_packages.append({
                        'name': module_name,
                        'status': 'missing'
                    })
                else:
                    version = self.installed_packages.get(module_name)
                    required_packages.append({
                        'name': module_name,
                        'status': 'installed',
                        'version': version
                    })
            except Exception as e:
                errors.append(f"檢查模組 {module_name} 時發生錯誤: {str(e)}")
        
        # 將缺少依賴不視為錯誤，讓驗證能通過並進入依賴安裝步驟
        return len(errors) == 0, errors, required_packages
    
    def _is_stdlib_module(self, module_name: str) -> bool:
        """檢查是否為標準庫模組"""
        try:
            module_info = sys.modules.get(module_name) or importlib.import_module(module_name)
            return hasattr(module_info, '__file__') and 'site-packages' not in str(module_info.__file__)
        except:
            return False
    
    def _check_environment_compatibility(self, tree: ast.AST) -> Tuple[bool, List[str]]:
        """檢查環境兼容性"""
        errors = []
        
        # 檢查 Python 版本要求
        for node in ast.walk(tree):
            if isinstance(node, ast.Name) and node.id == 'print' and isinstance(node.ctx, ast.Load):
                # Python 2 風格的 print 語句
                errors.append("發現 Python 2 風格的 print 語句，建議更新為 print() 函數")
            
            if isinstance(node, ast.comprehension):
                # Python 3 特有的特性
                if hasattr(node, 'is_async'):
                    errors.append("使用了異步推導式，需要 Python 3.6 或更高版本")
        
        return len(errors) == 0, errors
    
    def get_installation_commands(self, required_packages: List[Dict]) -> List[str]:
        """獲取安裝命令"""
        commands = []
        for pkg in required_packages:
            if pkg['status'] == 'missing':
                commands.append(f"pip install {pkg['name']}")
        return commands
