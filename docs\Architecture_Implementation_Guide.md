# Python 腳本管理與排程系統 - 架構實現指南

*文檔版本：1.0.0*  
*最後更新日期：2025-03-27*

## 目錄

1. [實現概述](#實現概述)
2. [API 路徑統一化實現](#api-路徑統一化實現)
3. [目錄結構重構實現](#目錄結構重構實現)
4. [服務層重構實現](#服務層重構實現)
5. [配置管理重構實現](#配置管理重構實現)
6. [測試架構優化實現](#測試架構優化實現)
7. [部署與遷移考量](#部署與遷移考量)

## 實現概述

本文檔提供了 Python 腳本管理與排程系統架構改進的具體實現指南。這些指南基於《架構結構說明》文檔中提出的改進建議，提供了更詳細的實施步驟和代碼示例。

## API 路徑統一化實現

### 1. 創建 API 版本控制中間件

首先，實現一個 API 版本控制中間件，用於處理 API 路徑重定向和版本管理：

```python
# app/api/middleware/version.py
from flask import request, redirect, current_app
import functools

def api_version_middleware(app):
    """API 版本控制中間件"""
    
    @app.before_request
    def redirect_old_api_paths():
        """重定向舊的 API 路徑到新的版本化 API 路徑"""
        path = request.path
        
        # 舊路徑映射到新路徑
        api_path_mapping = {
            # 腳本相關 API
            r'^/script/api/scripts$': '/api/v1/scripts',
            r'^/script/([^/]+)/info$': r'/api/v1/scripts/\1/info',
            r'^/script/([^/]+)$': r'/api/v1/scripts/\1',
            
            # 排程相關 API
            r'^/schedule/api/schedule$': '/api/v1/schedules',
            r'^/schedule/api/schedule/([^/]+)$': r'/api/v1/schedules/\1',
            r'^/schedule/api/schedule/([^/]+)/toggle$': r'/api/v1/schedules/\1/toggle',
            r'^/schedule/api/schedule/([^/]+)/logs$': r'/api/v1/schedules/\1/logs',
            
            # 系統資源 API
            r'^/api/system-resources$': '/api/v1/system/resources',
            r'^/schedule/api/system-resources$': '/api/v1/system/resources',
        }
        
        import re
        for old_pattern, new_path in api_path_mapping.items():
            if re.match(old_pattern, path):
                # 替換路徑中的捕獲組
                if '\\' in new_path:
                    for i, group in enumerate(re.match(old_pattern, path).groups(), 1):
                        new_path = new_path.replace(f'\\{i}', group)
                
                current_app.logger.warning(f'使用了棄用的 API 路徑: {path}，請使用 {new_path}')
                return redirect(new_path)
```

### 2. 創建統一的 API 藍圖結構

接下來，創建新的 API 藍圖結構：

```python
# app/api/v1/__init__.py
from flask import Blueprint

api_v1 = Blueprint('api_v1', __name__, url_prefix='/api/v1')

# 導入並註冊各個 API 模塊
from . import scripts, schedules, system

# 在 app/__init__.py 中註冊此藍圖
```

### 3. 實現新版本 API 路由

為每個資源創建獨立的 API 模塊：

```python
# app/api/v1/scripts.py
from . import api_v1
from flask import jsonify, request, current_app

@api_v1.route('/scripts', methods=['GET'])
def get_scripts():
    """獲取所有可用的腳本"""
    script_executor = current_app.script_executor
    scripts = script_executor.get_available_scripts()
    return jsonify({
        'status': 'success',
        'data': scripts
    })

@api_v1.route('/scripts/<script_name>', methods=['GET'])
def get_script(script_name):
    """獲取特定腳本的詳細信息"""
    # 實現代碼...

@api_v1.route('/scripts', methods=['POST'])
def upload_script():
    """上傳新腳本"""
    # 實現代碼...

@api_v1.route('/scripts/<script_name>', methods=['DELETE'])
def delete_script(script_name):
    """刪除腳本"""
    # 實現代碼...
```

類似地，實現排程和系統資源相關的 API 模塊。

## 目錄結構重構實現

### 1. 創建新的數據目錄結構

首先，創建新的數據目錄結構：

```bash
mkdir -p app/data/uploads
mkdir -p app/data/outputs
```

### 2. 遷移現有數據

遷移現有的上傳文件和輸出文件到新位置：

```bash
# 在 Windows 上
xcopy /E /I uploads app\data\uploads
xcopy /E /I outputs app\data\outputs

# 在 Linux/macOS 上
cp -r uploads/ app/data/uploads/
cp -r outputs/ app/data/outputs/
```

### 3. 更新配置中的路徑

修改配置文件以使用新的路徑：

```python
# config/default.py
import os

class DefaultConfig:
    # 基本路徑設定
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    APP_DIR = os.path.join(BASE_DIR, 'app')
    DATA_DIR = os.path.join(APP_DIR, 'data')
    
    # 數據目錄設定
    UPLOAD_FOLDER = os.path.join(DATA_DIR, 'uploads')
    OUTPUT_FOLDER = os.path.join(DATA_DIR, 'outputs')
    LOG_DIR = os.path.join(BASE_DIR, 'logs')
```

## 服務層重構實現

### 1. 實現服務工廠

首先，實現服務工廠模式來管理服務實例和依賴：

```python
# app/services/__init__.py
from flask import current_app
import inspect

_services = {}

def register_service(service_class, *args, **kwargs):
    """註冊服務"""
    service_name = service_class.__name__
    if service_name not in _services:
        # 檢查是否已提供所有需要的參數
        sig = inspect.signature(service_class.__init__)
        required_params = [
            param.name for param in sig.parameters.values()
            if param.default == inspect.Parameter.empty and param.name != 'self'
        ]
        
        # 檢查是否有未提供的必要參數
        missing_params = [param for param in required_params if param not in kwargs]
        if missing_params:
            raise ValueError(f"缺少服務 {service_name} 初始化所需參數: {', '.join(missing_params)}")
        
        # 創建服務實例
        service = service_class(*args, **kwargs)
        _services[service_name] = service
        current_app.logger.debug(f"服務 {service_name} 已註冊")
    
    return _services[service_name]

def get_service(service_name):
    """獲取服務實例"""
    if service_name not in _services:
        raise ValueError(f"服務 {service_name} 未註冊")
    return _services[service_name]

def init_services(app):
    """初始化所有服務"""
    # 清空服務註冊表
    _services.clear()
    
    # 註冊腳本執行器
    from .executors.script import ScriptExecutor
    script_executor = register_service(
        ScriptExecutor,
        upload_folder=app.config['UPLOAD_FOLDER'],
        output_folder=app.config['OUTPUT_FOLDER']
    )
    app.script_executor = script_executor
    
    # 註冊排程管理器
    from .managers.schedule import ScheduleManager
    schedule_manager = register_service(
        ScheduleManager,
        script_executor=script_executor
    )
    app.schedule_manager = schedule_manager
    
    # 註冊排程執行器
    from .executors.schedule import ScheduleExecutor
    schedule_executor = register_service(
        ScheduleExecutor,
        schedule_manager=schedule_manager,
        script_executor=script_executor
    )
    app.schedule_executor = schedule_executor
    
    # 啟動排程執行器
    schedule_executor.start()
    
    return app
```

### 2. 重構服務實現

將服務分離到獨立的模塊：

```python
# app/services/executors/script.py
import os
import subprocess
from ...utils.logger import setup_logger

class ScriptExecutor:
    """腳本執行器"""
    
    def __init__(self, upload_folder, output_folder):
        self.upload_folder = upload_folder
        self.output_folder = output_folder
        self.logger = setup_logger(__name__)
        
        # 確保目錄存在
        os.makedirs(self.upload_folder, exist_ok=True)
        os.makedirs(self.output_folder, exist_ok=True)
    
    def execute_script(self, script_name, timeout=60):
        """執行腳本並返回結果"""
        # 實現代碼...
    
    def get_available_scripts(self):
        """獲取可用的腳本列表"""
        # 實現代碼...
```

類似地，重構 `ScheduleManager` 和 `ScheduleExecutor` 服務。

## 配置管理重構實現

### 1. 創建配置目錄結構

首先，創建配置目錄結構：

```bash
mkdir -p config
```

### 2. 實現配置基類和環境特定配置

創建配置基類和各環境配置文件：

```python
# config/default.py
import os

class DefaultConfig:
    """默認配置基類"""
    
    # 基本路徑設定
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    APP_DIR = os.path.join(BASE_DIR, 'app')
    DATA_DIR = os.path.join(APP_DIR, 'data')
    
    # 數據目錄設定
    UPLOAD_FOLDER = os.path.join(DATA_DIR, 'uploads')
    OUTPUT_FOLDER = os.path.join(DATA_DIR, 'outputs')
    LOG_DIR = os.path.join(BASE_DIR, 'logs')
    
    # 資料庫設定
    SQLALCHEMY_DATABASE_URI = 'sqlite:///' + os.path.join(BASE_DIR, 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 安全設定
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'default-dev-key'
    
    # 上傳設定
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 MB
    ALLOWED_EXTENSIONS = {'py'}
    
    # 執行設定
    SCHEDULE_TIMEOUT = 3600  # 1 小時
    MAX_SCHEDULES = 10
    
    @classmethod
    def init_app(cls, app):
        """初始化應用程式配置"""
        pass
```

```python
# config/development.py
from .default import DefaultConfig

class DevelopmentConfig(DefaultConfig):
    """開發環境配置"""
    
    DEBUG = True
    SQLALCHEMY_ECHO = True
    
    @classmethod
    def init_app(cls, app):
        super().init_app(app)
        # 開發環境特定初始化
```

```python
# config/production.py
from .default import DefaultConfig

class ProductionConfig(DefaultConfig):
    """生產環境配置"""
    
    DEBUG = False
    
    # 使用環境變量獲取敏感配置
    SECRET_KEY = os.environ.get('SECRET_KEY')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or DefaultConfig.SQLALCHEMY_DATABASE_URI
    
    @classmethod
    def init_app(cls, app):
        super().init_app(app)
        # 生產環境特定初始化
```

```python
# config/testing.py
from .default import DefaultConfig
import tempfile

class TestingConfig(DefaultConfig):
    """測試環境配置"""
    
    TESTING = True
    WTF_CSRF_ENABLED = False
    
    # 使用臨時資料庫
    DB_FD, DB_PATH = tempfile.mkstemp()
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{DB_PATH}'
    
    # 使用測試專用目錄
    TEST_DIR = tempfile.mkdtemp()
    UPLOAD_FOLDER = os.path.join(TEST_DIR, 'uploads')
    OUTPUT_FOLDER = os.path.join(TEST_DIR, 'outputs')
    
    @classmethod
    def init_app(cls, app):
        super().init_app(app)
        # 確保測試目錄存在
        os.makedirs(cls.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(cls.OUTPUT_FOLDER, exist_ok=True)
```

### 3. 實現配置加載機制

創建配置初始化文件：

```python
# config/__init__.py
import os
from .default import DefaultConfig
from .development import DevelopmentConfig
from .production import ProductionConfig
from .testing import TestingConfig

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """獲取當前環境配置"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])

def validate_config(config_obj):
    """驗證配置是否完整有效"""
    required_attrs = [
        'SQLALCHEMY_DATABASE_URI',
        'UPLOAD_FOLDER',
        'OUTPUT_FOLDER',
        'SECRET_KEY',
        'MAX_CONTENT_LENGTH',
        'ALLOWED_EXTENSIONS'
    ]
    
    missing_attrs = [attr for attr in required_attrs if not hasattr(config_obj, attr)]
    if missing_attrs:
        raise ValueError(f"配置缺少必要項: {', '.join(missing_attrs)}")
    
    # 檢查目錄是否可寫
    for dir_attr in ['UPLOAD_FOLDER', 'OUTPUT_FOLDER']:
        dir_path = getattr(config_obj, dir_attr)
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path)
            except Exception as e:
                raise ValueError(f"無法創建目錄 {dir_path}: {str(e)}")
        elif not os.access(dir_path, os.W_OK):
            raise ValueError(f"目錄 {dir_path} 不可寫")
    
    return True
```

## 測試架構優化實現

### 1. 重組測試目錄結構

創建新的測試目錄結構：

```bash
mkdir -p tests/unit/models
mkdir -p tests/unit/services
mkdir -p tests/integration/api
mkdir -p tests/integration/services
mkdir -p tests/e2e
```

### 2. 實現測試固件和共享資源

創建測試固件和共享資源：

```python
# tests/conftest.py
import pytest
import os
import tempfile
from app import create_app, db
from app.models import Schedule, ExecutionLog

@pytest.fixture
def app():
    """創建測試應用實例"""
    # 使用測試配置
    app = create_app('testing')
    
    # 創建應用上下文
    with app.app_context():
        # 創建數據庫
        db.create_all()
        yield app
        
        # 清理數據庫
        db.session.remove()
        db.drop_all()

@pytest.fixture
def client(app):
    """創建測試客戶端"""
    return app.test_client()

@pytest.fixture
def runner(app):
    """創建 CLI 測試運行器"""
    return app.test_cli_runner()

@pytest.fixture
def sample_script(app):
    """創建樣本腳本"""
    script_content = """
# 測試腳本
print("Hello, World!")
"""
    script_path = os.path.join(app.config['UPLOAD_FOLDER'], 'test_script.py')
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    yield 'test_script.py'
    
    # 清理
    if os.path.exists(script_path):
        os.remove(script_path)

@pytest.fixture
def sample_schedule(app, sample_script):
    """創建樣本排程"""
    schedule = Schedule(
        script_name=sample_script,
        schedule_type='interval',
        interval_minutes=5,
        description='測試排程'
    )
    db.session.add(schedule)
    db.session.commit()
    
    yield schedule
    
    # 清理
    db.session.delete(schedule)
    db.session.commit()
```

### 3. 實現單元測試

創建模型單元測試：

```python
# tests/unit/models/test_schedule.py
import pytest
from datetime import datetime, timedelta
from app.models import Schedule, ScheduleType, ScheduleStatus

def test_schedule_creation(app):
    """測試排程創建"""
    with app.app_context():
        schedule = Schedule(
            script_name='test.py',
            schedule_type=ScheduleType.INTERVAL.value,
            interval_minutes=5,
            description='測試排程'
        )
        
        assert schedule.script_name == 'test.py'
        assert schedule.schedule_type == ScheduleType.INTERVAL.value
        assert schedule.interval_minutes == 5
        assert schedule.description == '測試排程'
        assert schedule.status == ScheduleStatus.WAITING.value
        assert schedule.is_active == True

def test_schedule_next_run_calculation(app):
    """測試排程下次執行時間計算"""
    with app.app_context():
        # 測試間隔排程
        interval_schedule = Schedule(
            script_name='test.py',
            schedule_type=ScheduleType.INTERVAL.value,
            interval_minutes=5
        )
        
        next_run = interval_schedule._calculate_next_run()
        assert next_run > datetime.now()
        assert next_run < datetime.now() + timedelta(minutes=6)
        
        # 測試每日排程
        daily_schedule = Schedule(
            script_name='test.py',
            schedule_type=ScheduleType.DAILY.value,
            execution_time='08:00'
        )
        
        next_run = daily_schedule._calculate_next_run()
        assert next_run.hour == 8
        assert next_run.minute == 0
```

### 4. 實現集成測試

創建 API 集成測試：

```python
# tests/integration/api/test_scripts_api.py
import pytest
import json
import os
from io import BytesIO

def test_get_scripts(client, sample_script):
    """測試獲取腳本列表 API"""
    response = client.get('/api/v1/scripts')
    assert response.status_code == 200
    
    data = json.loads(response.data)
    assert data['status'] == 'success'
    assert sample_script in [script['name'] for script in data['data']]

def test_upload_script(client):
    """測試上傳腳本 API"""
    script_content = b"print('Test Upload')"
    data = {
        'file': (BytesIO(script_content), 'upload_test.py')
    }
    
    response = client.post(
        '/api/v1/scripts',
        data=data,
        content_type='multipart/form-data'
    )
    
    assert response.status_code == 201
    data = json.loads(response.data)
    assert data['status'] == 'success'
    assert 'upload_test.py' in data['data']['filename']
```

## 部署與遷移考量

### 1. 準備遷移腳本

創建一個遷移腳本，自動化遷移過程：

```python
# scripts/migrate_structure.py
#!/usr/bin/env python
import os
import shutil
import sys

def create_directory(path):
    """創建目錄"""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"✓ 創建目錄: {path}")

def migrate_files(src, dest):
    """遷移文件"""
    if os.path.exists(src):
        if os.path.isdir(src):
            # 如果目標已存在，合併目錄
            if os.path.exists(dest):
                for item in os.listdir(src):
                    s = os.path.join(src, item)
                    d = os.path.join(dest, item)
                    if os.path.isdir(s):
                        migrate_files(s, d)
                    else:
                        shutil.copy2(s, d)
                        print(f"✓ 複製文件: {s} -> {d}")
            else:
                shutil.copytree(src, dest)
                print(f"✓ 複製目錄: {src} -> {dest}")
        else:
            shutil.copy2(src, dest)
            print(f"✓ 複製文件: {src} -> {dest}")

def main():
    """主函數"""
    print("開始架構遷移...")
    
    # 確保當前目錄是專案根目錄
    if not os.path.exists('app') or not os.path.exists('run.py'):
        print("錯誤: 請在專案根目錄下運行此腳本")
        sys.exit(1)
    
    # 創建新的目錄結構
    create_directory('app/data/uploads')
    create_directory('app/data/outputs')
    create_directory('app/api/v1')
    create_directory('app/api/middleware')
    create_directory('app/services/executors')
    create_directory('app/services/managers')
    create_directory('config')
    create_directory('tests/unit/models')
    create_directory('tests/unit/services')
    create_directory('tests/integration/api')
    create_directory('tests/integration/services')
    create_directory('tests/e2e')
    create_directory('docs/architecture')
    create_directory('docs/api')
    create_directory('docs/user')
    
    # 遷移文件
    migrate_files('uploads', 'app/data/uploads')
    migrate_files('outputs', 'app/data/outputs')
    
    print("架構遷移完成！")

if __name__ == "__main__":
    main()
```

### 2. 部署注意事項

1. **數據備份**：在進行任何遷移前，確保備份所有數據和代碼
2. **環境變量**：更新部署環境中的環境變量，特別是 `FLASK_ENV`
3. **路徑配置**：確保所有路徑配置正確，特別是在不同操作系統部署時
4. **權限設置**：確保數據目錄具有適當的讀寫權限
5. **API 相容性**：保持對舊 API 的支持，直到所有客戶端都更新

### 3. 實施計劃

1. **開發環境測試**：先在開發環境完成所有更改和測試
2. **分階段部署**：按照遷移計劃分階段部署
3. **持續監控**：每個階段部署後進行監控，確保系統穩定
4. **回滾計劃**：準備回滾計劃，以應對可能的問題
5. **文檔更新**：隨著部署進行更新文檔 