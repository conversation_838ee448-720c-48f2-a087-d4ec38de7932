#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查數據庫中的排程數據
"""

from app import create_app
from app.models.schedule import Schedule

def check_schedules():
    """檢查數據庫中的排程"""
    app = create_app()
    
    with app.app_context():
        try:
            # 查詢所有排程
            all_schedules = Schedule.query.all()
            print(f"📊 數據庫中總共有 {len(all_schedules)} 個排程")
            
            if all_schedules:
                print("\n📋 排程列表:")
                print("=" * 80)
                for schedule in all_schedules:
                    print(f"ID: {schedule.id}")
                    print(f"名稱: {schedule.description}")
                    print(f"腳本: {schedule.script_name}")
                    print(f"類型: {schedule.schedule_type}")
                    print(f"狀態: {schedule.status}")
                    print(f"啟用: {schedule.is_active}")
                    print(f"創建時間: {schedule.created_at}")
                    print("-" * 40)
            else:
                print("❌ 數據庫中沒有任何排程記錄")
                
            # 查詢活躍的排程
            active_schedules = Schedule.query.filter_by(is_active=True).all()
            print(f"\n🟢 活躍排程數量: {len(active_schedules)}")
            
            # 查詢各種狀態的排程
            pending_schedules = Schedule.query.filter_by(status='pending').all()
            running_schedules = Schedule.query.filter_by(status='running').all()
            completed_schedules = Schedule.query.filter_by(status='completed').all()
            failed_schedules = Schedule.query.filter_by(status='failed').all()
            
            print(f"⏳ 待執行: {len(pending_schedules)}")
            print(f"🔄 執行中: {len(running_schedules)}")
            print(f"✅ 已完成: {len(completed_schedules)}")
            print(f"❌ 失敗: {len(failed_schedules)}")
            
        except Exception as e:
            print(f"❌ 檢查數據庫時發生錯誤: {e}")

if __name__ == "__main__":
    check_schedules()
