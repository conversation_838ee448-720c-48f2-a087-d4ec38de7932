import os
import unittest
from datetime import datetime, timedelta
from app import create_app, db
from app.models import Schedule, ScheduleType, ScheduleStatus, Script, ExecutionLog

class TestModels(unittest.TestCase):
    """測試資料庫模型"""
    
    def setUp(self):
        """測試前準備"""
        # 建立測試用應用程式
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 建立測試資料庫
        db.create_all()
    
    def tearDown(self):
        """測試後清理"""
        # 刪除測試資料庫
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_schedule_creation(self):
        """測試排程建立"""
        # 建立測試排程
        schedule = Schedule(
            script_name='test_script.py',
            schedule_type=ScheduleType.DAILY.value,
            execution_time=datetime.now() + timedelta(hours=1),
            description='測試排程'
        )
        db.session.add(schedule)
        db.session.commit()
        
        # 驗證排程資料
        self.assertIsNotNone(schedule.id)
        self.assertEqual(schedule.script_name, 'test_script.py')
        self.assertEqual(schedule.schedule_type, ScheduleType.DAILY.value)
        self.assertEqual(schedule.status, ScheduleStatus.WAITING.value)
        self.assertTrue(schedule.is_active)
    
    def test_schedule_next_run_calculation(self):
        """測試下次執行時間計算"""
        # 建立每日排程
        now = datetime.now()
        tomorrow = now + timedelta(days=1)
        tomorrow = tomorrow.replace(hour=9, minute=0, second=0, microsecond=0)
        
        schedule = Schedule(
            script_name='test_script.py',
            schedule_type=ScheduleType.DAILY.value,
            execution_time=tomorrow,
            description='每日排程'
        )
        db.session.add(schedule)
        db.session.commit()
        
        # 驗證下次執行時間
        next_run = schedule._calculate_next_run()
        self.assertIsNotNone(next_run)
        self.assertTrue(next_run > now)
        
        # 測試已過期的執行時間
        past_time = now - timedelta(hours=1)
        schedule.execution_time = past_time
        next_run = schedule._calculate_next_run()
        self.assertIsNotNone(next_run)
        self.assertTrue(next_run > now)
    
    def test_execution_log_creation(self):
        """測試執行記錄建立"""
        # 建立測試排程
        schedule = Schedule(
            script_name='test_script.py',
            schedule_type=ScheduleType.IMMEDIATE.value,
            description='立即執行排程'
        )
        db.session.add(schedule)
        db.session.commit()
        
        # 建立執行記錄
        log = ExecutionLog(
            schedule_id=schedule.id,
            status='completed',
            output='測試輸出',
            error=None
        )
        db.session.add(log)
        db.session.commit()
        
        # 驗證執行記錄
        self.assertIsNotNone(log.id)
        self.assertEqual(log.schedule_id, schedule.id)
        self.assertEqual(log.status, 'completed')
        self.assertEqual(log.output, '測試輸出')
        self.assertIsNone(log.error)
    
    def test_script_file_operations(self):
        """測試腳本檔案操作"""
        # 建立測試檔案
        test_file_path = os.path.join(self.app.config['UPLOAD_FOLDER'], 'test_script.py')
        os.makedirs(os.path.dirname(test_file_path), exist_ok=True)
        with open(test_file_path, 'w') as f:
            f.write('print("Hello, World!")')
        
        # 建立腳本記錄
        script = Script(
            name='test_script.py',
            description='測試腳本',
            file_path=test_file_path,
            is_active=True
        )
        db.session.add(script)
        db.session.commit()
        
        # 驗證檔案資訊
        self.assertIsNotNone(script.id)
        self.assertEqual(script.name, 'test_script.py')
        self.assertTrue(os.path.exists(script.file_path))
        
        # 測試檔案刪除
        os.remove(test_file_path)
        self.assertFalse(os.path.exists(test_file_path))
    
    def test_schedule_status_transition(self):
        """測試排程狀態轉換"""
        schedule = Schedule(
            script_name='test_script.py',
            schedule_type=ScheduleType.IMMEDIATE.value,
            description='狀態測試排程'
        )
        db.session.add(schedule)
        db.session.commit()
        
        # 驗證初始狀態
        self.assertEqual(schedule.status, ScheduleStatus.WAITING.value)
        
        # 更新狀態
        schedule.status = ScheduleStatus.RUNNING.value
        db.session.commit()
        self.assertEqual(schedule.status, ScheduleStatus.RUNNING.value)
        
        # 完成執行
        schedule.status = ScheduleStatus.COMPLETED.value
        schedule.last_run = datetime.now()
        db.session.commit()
        self.assertEqual(schedule.status, ScheduleStatus.COMPLETED.value)
        self.assertIsNotNone(schedule.last_run)

if __name__ == '__main__':
    unittest.main() 