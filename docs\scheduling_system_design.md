# Python腳本排程系統設計文件

## 目錄
1. [系統概述](#系統概述)
2. [排程功能架構](#排程功能架構)
3. [智能時間調整機制](#智能時間調整機制)
4. [星期日期轉換處理](#星期日期轉換處理)
5. [使用者介面設計](#使用者介面設計)
6. [錯誤處理機制](#錯誤處理機制)
7. [未來優化方向](#未來優化方向)

## 系統概述

Python腳本排程系統是專為管理與執行Python腳本而設計的排程工具，支援多種執行頻率，並具備智能時間調整功能，避免設定無效的執行時間。本文件主要記錄系統中「新增排程」單元的實作細節與設計理念。

### 主要功能與特點
- 支援多種排程類型：每日、每週、每月、自定義間隔
- 智能時間調整，自動處理過期時間
- 多種方式關閉彈出視窗（按鈕、ESC、背景點擊）
- 即時資源監控顯示
- 週期性排程任務管理

## 排程功能架構

### 後端架構
```
app/
├── models/
│   └── schedule.py       # 排程資料模型
├── routes/
│   └── schedule_routes.py # API路由定義
├── services/
│   ├── schedule_manager.py   # 排程管理服務
│   └── schedule_executor.py  # 排程執行服務
└── static/
    └── js/
        └── schedule.js   # 前端排程邏輯
```

### 資料模型
排程系統使用`Schedule`類來表示排程任務，主要屬性包括：
- `id`：排程任務唯一識別碼
- `script_name`：要執行的腳本名稱
- `schedule_type`：排程類型（立即、一次性、每分鐘、每日、每週、每月）
- `execution_time`：執行時間
- `weekdays`：選定的星期幾（每週排程）
- `days_of_month`：選定的日期（每月排程）
- `status`：執行狀態（等待中、執行中、已完成、執行失敗）

## 智能時間調整機制

### 前端智能調整
系統在前端實作了智能時間調整功能，當使用者設定過去的時間時，系統會自動計算下一個有效的執行時間：

#### 日排程調整邏輯
```javascript
function validateDailyTime() {
    // 檢查所選時間是否已過
    // 若已過，自動調整至明日同一時間
    // 顯示提示信息
}
```

#### 週排程調整邏輯
```javascript
function validateWeeklyTime() {
    // 檢查所選星期與時間
    // 若當天為所選星期之一但時間已過，則調整至下一個所選星期
    // 若當天非所選星期，則調整至下一個最近的所選星期
}

function getNextValidDayFromWeekdays(selectedDays) {
    // 計算下一個有效的星期日期
}

function findNextDate(currentDay, selectedDays, hour, minute) {
    // 根據當前日期和選擇的星期，找出最近的下一個執行日期
}
```

#### 月排程調整邏輯
```javascript
function validateMonthlyTime() {
    // 檢查所選日期與時間
    // 若當天為所選日期之一但時間已過，則調整至下一個所選日期
    // 若當月剩餘日期中沒有所選日期，則調整至下個月最早的所選日期
}
```

### 後端時間處理
後端處理時需注意時區問題，確保時間儲存準確無誤：

```python
# 後端不再檢查過去時間，完全信任前端的智能調整
def create_schedule(self, script_name, schedule_type, execution_time=None, ...):
    # 解析時間字符串為datetime對象
    # 嚴格按照傳來的日期時間創建對象
    # 確保星期格式正確轉換
```

## 星期日期轉換處理

### JavaScript與Python星期格式差異
在處理週排程時，需特別注意JavaScript與Python的星期表示方式不同：
- JavaScript: 0=週日，1=週一，...，6=週六
- Python: 0=週一，1=週二，...，6=週日

此差異是造成排程日期偏移問題的根本原因之一。

### 格式轉換實作
```python
# 轉換JavaScript星期格式為Python格式
python_weekdays = [(d - 1) % 7 for d in weekdays]

# 計算下一個有效執行日期（Python格式）
current_weekday = now.weekday()  # Python格式

# 找出下一個執行日期
next_weekdays = [d for d in python_weekdays if d >= current_weekday]
```

以具體實作為例，`Schedule`模型中的`_calculate_next_run`方法針對每週排程的處理：

```python
# 每週執行
if self.schedule_type == ScheduleType.WEEKLY:
    if not self.execution_time or not self.weekdays:
        return None
        
    # weekdays是JavaScript格式：0=週日，1=週一，...，6=週六
    # 而Python的weekday()是：0=週一，1=週二，...，6=週日
    # 需要轉換JavaScript星期格式為Python格式
    python_weekdays = [(d - 1) % 7 for d in self.weekdays]
    
    current_weekday = now.weekday()  # Python格式 0-6 (週一-週日)
    
    # 找出下一個執行日期（Python格式）
    next_weekdays = [d for d in python_weekdays if d >= current_weekday]
    if next_weekdays:
        # 本週內有符合的日期
        next_weekday = min(next_weekdays)
        days_ahead = next_weekday - current_weekday
    else:
        # 本週內沒有符合的日期，找下週的第一天
        next_weekday = min(python_weekdays)
        days_ahead = 7 + next_weekday - current_weekday
    
    # 計算下次執行時間，但不做任何時區調整
    next_date = now.date() + timedelta(days=days_ahead)
    next_run = datetime.combine(next_date, self.execution_time.time())
    
    return next_run
```

### 時間格式標準化
為避免時區問題，系統統一使用固定的時間格式字符串：
```python
# 儲存為字符串時
execution_time.strftime("%Y-%m-%dT%H:%M:%S")

# 解析字符串時
datetime.strptime(data["execution_time"], "%Y-%m-%dT%H:%M:%S")
```

在`Schedule`類的`to_dict`方法中的實作：

```python
def to_dict(self) -> dict:
    """轉換為字典格式"""
    return {
        "id": self.id,
        "script_name": self.script_name,
        "schedule_type": self.schedule_type.value,
        "execution_time": self.execution_time.strftime("%Y-%m-%dT%H:%M:%S") if self.execution_time else None,
        "interval_minutes": self.interval_minutes,
        "weekdays": self.weekdays,
        "days_of_month": self.days_of_month,
        "description": self.description,
        "status": self.status.value,
        "is_active": self.is_active,
        "created_at": self.created_at.strftime("%Y-%m-%dT%H:%M:%S"),
        "last_run": self.last_run.strftime("%Y-%m-%dT%H:%M:%S") if self.last_run else None,
        "next_run": self.next_run.strftime("%Y-%m-%dT%H:%M:%S") if self.next_run else None,
        "error_message": self.error_message
    }
```

前端JavaScript中的日期計算方式優化：

```javascript
// 輔助函數：找出當前日期之後的下一個最近的選中星期幾
function findNextDate(currentDay, selectedDays, hour, minute) {
    // 獲取今天的基本日期資訊
    const today = new Date();
    
    console.log(`findNextDate - 今天日期: ${today.toLocaleString()}, 星期幾: ${currentDay}`);
    
    // 存儲所有可能的下一個日期
    const possibleDates = [];
    
    // 遍歷所有選中的星期幾
    for (const day of selectedDays) {
        // 計算到下一個該星期幾的天數差
        let dayDiff = (day - currentDay + 7) % 7;
        
        // 如果是今天但時間已過，則設為下週同一天
        if (dayDiff === 0 && !isTimeInFuture(hour, minute)) {
            dayDiff = 7;
        }
        
        // 創建新日期（避免使用加法計算日期，防止月底溢出問題）
        const targetDate = new Date(today);
        targetDate.setDate(today.getDate() + dayDiff);
        targetDate.setHours(hour, minute, 0, 0);
        
        // 記錄這個日期
        possibleDates.push({
            date: targetDate,
            diff: dayDiff
        });
    }
    
    // 按天數差排序，選出最早的日期
    possibleDates.sort((a, b) => a.diff - b.diff);
    const resultDate = possibleDates[0].date;
    
    return resultDate;
}
```

## 使用者介面設計

### 排程表單設計
排程表單設計考慮了使用者體驗，包括：
- 系統資源使用狀況顯示於頂部，提高可見度
- 依排程類型動態顯示相關選項
- 提供清晰的時間調整提示
- 多種視窗關閉方式

### 資源狀態顯示
```css
/* 進度條樣式確保百分比文字清晰可見 */
.progress {
  height: 20px;
  position: relative;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  height: 100%;
}

/* 確保百分比文字在進度條之上清晰顯示 */
.progress-bar::after {
  content: attr(data-content);
  position: absolute;
  color: white;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  font-weight: bold;
}

/* 進度條狀態顏色 */
.progress-bar-success {
  background-color: #28a745;
}

.progress-bar-warning {
  background-color: #ffc107;
}

.progress-bar-danger {
  background-color: #dc3545;
}
```

資源狀態顯示HTML結構：

```html
<div class="progress mb-2">
  <div id="cpu-usage" class="progress-bar progress-bar-success" role="progressbar" data-content="25%" style="width: 25%"></div>
</div>

<div class="progress mb-2">
  <div id="memory-usage" class="progress-bar progress-bar-warning" role="progressbar" data-content="40%" style="width: 40%"></div>
</div>

<div class="progress mb-2">
  <div id="schedule-count" class="progress-bar progress-bar-info" role="progressbar" data-content="3/10" style="width: 30%"></div>
</div>
```

JavaScript更新資源顯示：

```javascript
function updateResourceMeters(data) {
    const cpuUsage = document.getElementById('cpu-usage');
    if (cpuUsage) {
        cpuUsage.style.width = `${data.cpu}%`;
        cpuUsage.setAttribute('data-content', `${data.cpu}%`);
        
        // 根據使用率設置顏色
        if (data.cpu > 80) {
            cpuUsage.className = 'progress-bar progress-bar-danger';
        } else if (data.cpu > 60) {
            cpuUsage.className = 'progress-bar progress-bar-warning';
        } else {
            cpuUsage.className = 'progress-bar progress-bar-success';
        }
    }
    
    // 記憶體和排程數量同理...
}
```

## 錯誤處理機制

### 前端錯誤處理
```javascript
function saveSchedule() {
    try {
        // 驗證表單數據
        // 準備請求數據
        // 發送API請求
    } catch (error) {
        // 顯示錯誤訊息
        console.error('保存排程時發生錯誤:', error);
        alert('保存排程失敗: ' + error.message);
    }
}
```

### 後端錯誤處理
```python
def create_schedule(self, ...):
    try:
        # 解析時間
        # 創建排程
        # 儲存排程
    except ValueError as e:
        # 記錄詳細錯誤
        logger.error(f"創建排程失敗: {str(e)}")
        # 返回明確的錯誤訊息
        raise ValueError(f"創建排程失敗: {str(e)}")
```

## 未來優化方向

### 可能的改進項目
1. **排程執行日誌深化**：提供更詳細的執行記錄和診斷信息
2. **更靈活的時間表達式**：支援類似cron的時間表達式格式
3. **排程依賴關係**：實現排程任務之間的依賴關係
4. **排程分群管理**：按標籤或分類管理排程任務
5. **排程執行失敗重試策略**：可配置的重試邏輯

### 效能優化
1. **排程檢查優化**：減少不必要的排程檢查
2. **快取層實作**：提高頻繁訪問數據的速度
3. **批次處理**：優化大量排程處理的效率 

## 問題診斷與解決

在開發過程中，我們遇到了一些值得記錄的問題和相應的解決方案：

### 1. 日期偏移問題
**問題描述**：每週排程設定為週日執行，但實際儲存的時間卻是週一。

**根本原因**：
- JavaScript和Python對星期幾的表示方式不同（JS: 0=週日，Python: 0=週一）
- 在日期轉換過程中未正確處理格式差異
- 使用`toISOString()`方法導致時區偏移

**解決方案**：
- 在Schedule模型中正確轉換星期格式
```python
python_weekdays = [(d - 1) % 7 for d in self.weekdays]
```
- 避免使用`toISOString()`，改用直接構建時間字符串
```javascript
function convertToServerTime(date) {
    return `${date.getFullYear()}-${pad(date.getMonth()+1)}-${pad(date.getDate())}T${pad(date.getHours())}:${pad(date.getMinutes())}:00`;
}
```
- 修正日期計算邏輯，避免月底溢出問題
```javascript
const targetDate = new Date(today);
targetDate.setDate(today.getDate() + dayDiff);
targetDate.setHours(hour, minute, 0, 0);
```

### 2. 表單資料驗證與儲存
**問題描述**：智能時間調整顯示正確的提示，但保存時仍提示錯誤。

**根本原因**：
- 前端已進行時間調整，但後端仍檢查時間是否過期
- 缺少全域變數來保存調整後的時間

**解決方案**：
- 去除後端過期時間檢查，信任前端的智能調整
```python
# 移除以下檢查
# if exec_time < datetime.now():
#     raise ValueError("不能設定過去的時間")
```
- 使用全域變數保存調整後的時間，確保驗證和保存時使用相同的值
```javascript
window.nextValidWeeklyDate = findNextDate(today, selectedDays, selectedHour, selectedMinute);
``` 