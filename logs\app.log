[2025-03-27 10:57:28,874] DEBUG in __init__: 註冊藍圖...
[2025-03-27 10:57:28,886] DEBUG in __init__: 初始化服務...
[2025-03-27 10:57:28,887] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 10:57:28,892] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 10:57:28,893] INFO in __init__: 所有服務初始化完成
[2025-03-27 10:57:28,893] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 10:57:30,841] DEBUG in __init__: 註冊藍圖...
[2025-03-27 10:57:30,853] DEBUG in __init__: 初始化服務...
[2025-03-27 10:57:30,854] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 10:57:30,859] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 10:57:30,859] INFO in __init__: 所有服務初始化完成
[2025-03-27 10:57:30,859] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 10:57:37,345] INFO in routes: 訪問首頁
[2025-03-27 10:57:37,345] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 10:57:37,346] DEBUG in routes: 模板是否存在: True
[2025-03-27 10:57:37,347] DEBUG in routes: 開始渲染模板
[2025-03-27 10:57:37,364] DEBUG in routes: 模板渲染完成
[2025-03-27 10:57:38,116] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 10:57:38,625] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:57:39,135] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:57:43,378] INFO in routes: 訪問首頁
[2025-03-27 10:57:43,379] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 10:57:43,379] DEBUG in routes: 模板是否存在: True
[2025-03-27 10:57:43,380] DEBUG in routes: 開始渲染模板
[2025-03-27 10:57:43,380] DEBUG in routes: 模板渲染完成
[2025-03-27 10:57:43,627] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:57:44,027] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 10:57:44,231] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:57:45,045] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:57:49,230] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:57:50,041] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:57:54,228] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:57:55,053] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:57:58,418] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 10:57:58,785] DEBUG in routes: Script manager initialized
[2025-03-27 10:57:58,785] DEBUG in routes: 開始處理腳本驗證請求
[2025-03-27 10:57:58,786] DEBUG in routes: 保存臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:57:58,791] DEBUG in routes: 開始驗證腳本
[2025-03-27 10:57:58,793] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 10:57:58,794] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:57:59,228] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:00,060] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:04,530] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:05,041] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:07,043] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 10:58:07,043] DEBUG in routes: 請求方法: POST
[2025-03-27 10:58:07,044] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '648', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundarytOzojDankDW09Bnt', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 10:58:07,046] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', 'TEST3'), ('tag', '備份還原')])
[2025-03-27 10:58:07,046] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'test_script.py' ('application/octet-stream')>)])
[2025-03-27 10:58:07,047] DEBUG in routes: 處理上傳文件: test_script.py
[2025-03-27 10:58:07,047] DEBUG in routes: 描述: TEST3
[2025-03-27 10:58:07,047] DEBUG in routes: 標籤: ['備份還原']
[2025-03-27 10:58:07,048] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:58:07,054] DEBUG in routes: 臨時文件保存成功，大小: 241 字節
[2025-03-27 10:58:07,054] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:58:07,056] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 10:58:07,057] DEBUG in routes: 開始上傳腳本...
[2025-03-27 10:58:07,088] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_test_script.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_test_script.py', 'description': 'TEST3', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 10:58:07,089] INFO in routes: 成功上傳腳本: test_script.py
[2025-03-27 10:58:07,090] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:58:08,110] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 10:58:10,124] DEBUG in __init__: 註冊藍圖...
[2025-03-27 10:58:10,137] DEBUG in __init__: 初始化服務...
[2025-03-27 10:58:10,139] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 10:58:10,144] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 10:58:10,145] INFO in __init__: 所有服務初始化完成
[2025-03-27 10:58:10,145] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 10:58:10,832] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:11,649] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:14,229] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:15,044] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:15,443] INFO in routes: 訪問首頁
[2025-03-27 10:58:15,443] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 10:58:15,444] DEBUG in routes: 模板是否存在: True
[2025-03-27 10:58:15,444] DEBUG in routes: 開始渲染模板
[2025-03-27 10:58:15,458] DEBUG in routes: 模板渲染完成
[2025-03-27 10:58:16,147] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 10:58:16,654] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:17,164] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:21,656] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:22,164] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:26,649] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:27,160] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:31,642] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:32,150] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:36,647] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:37,157] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:41,643] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:42,156] ERROR in schedule_routes: 獲取系統資源信息時發生錯誤: No module named 'app.scheduler.scheduler'
[2025-03-27 10:58:43,348] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 10:58:45,313] DEBUG in __init__: 註冊藍圖...
[2025-03-27 10:58:45,326] DEBUG in __init__: 初始化服務...
[2025-03-27 10:58:45,327] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 10:58:45,332] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 10:58:45,332] INFO in __init__: 所有服務初始化完成
[2025-03-27 10:58:45,333] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 10:59:29,150] INFO in routes: 訪問首頁
[2025-03-27 10:59:29,150] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 10:59:29,150] DEBUG in routes: 模板是否存在: True
[2025-03-27 10:59:29,151] DEBUG in routes: 開始渲染模板
[2025-03-27 10:59:29,163] DEBUG in routes: 模板渲染完成
[2025-03-27 10:59:29,457] DEBUG in __init__: 註冊藍圖...
[2025-03-27 10:59:29,470] DEBUG in __init__: 初始化服務...
[2025-03-27 10:59:29,471] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 10:59:29,476] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 10:59:29,476] INFO in __init__: 所有服務初始化完成
[2025-03-27 10:59:29,476] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 10:59:29,807] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 10:59:31,679] DEBUG in __init__: 註冊藍圖...
[2025-03-27 10:59:31,691] DEBUG in __init__: 初始化服務...
[2025-03-27 10:59:31,692] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 10:59:31,696] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 10:59:31,697] INFO in __init__: 所有服務初始化完成
[2025-03-27 10:59:31,697] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 10:59:31,910] INFO in routes: 訪問首頁
[2025-03-27 10:59:31,910] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 10:59:31,911] DEBUG in routes: 模板是否存在: True
[2025-03-27 10:59:31,911] DEBUG in routes: 開始渲染模板
[2025-03-27 10:59:31,912] DEBUG in routes: 模板渲染完成
[2025-03-27 10:59:32,555] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 10:59:41,121] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 10:59:41,532] DEBUG in routes: Script manager initialized
[2025-03-27 10:59:41,532] DEBUG in routes: 開始處理腳本驗證請求
[2025-03-27 10:59:41,534] DEBUG in routes: 保存臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:59:41,538] DEBUG in routes: 開始驗證腳本
[2025-03-27 10:59:41,540] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 10:59:41,541] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:59:47,821] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 10:59:47,822] DEBUG in routes: 請求方法: POST
[2025-03-27 10:59:47,822] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '648', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryJ0nJlMCmJE5lYH2s', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 10:59:47,824] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', 'TEST3'), ('tag', '檔案管理')])
[2025-03-27 10:59:47,824] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'test_script.py' ('application/octet-stream')>)])
[2025-03-27 10:59:47,825] DEBUG in routes: 處理上傳文件: test_script.py
[2025-03-27 10:59:47,825] DEBUG in routes: 描述: TEST3
[2025-03-27 10:59:47,826] DEBUG in routes: 標籤: ['檔案管理']
[2025-03-27 10:59:47,826] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:59:47,831] DEBUG in routes: 臨時文件保存成功，大小: 241 字節
[2025-03-27 10:59:47,832] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:59:47,833] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 10:59:47,834] DEBUG in routes: 開始上傳腳本...
[2025-03-27 10:59:47,866] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_test_script.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_test_script.py', 'description': 'TEST3', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 10:59:47,867] INFO in routes: 成功上傳腳本: test_script.py
[2025-03-27 10:59:47,867] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 10:59:48,680] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 10:59:48,816] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 10:59:51,147] DEBUG in __init__: 註冊藍圖...
[2025-03-27 10:59:51,161] DEBUG in __init__: 初始化服務...
[2025-03-27 10:59:51,162] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 10:59:51,167] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 10:59:51,167] INFO in __init__: 所有服務初始化完成
[2025-03-27 10:59:51,167] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 10:59:51,322] DEBUG in __init__: 註冊藍圖...
[2025-03-27 10:59:51,335] DEBUG in __init__: 初始化服務...
[2025-03-27 10:59:51,338] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 10:59:51,343] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 10:59:51,343] INFO in __init__: 所有服務初始化完成
[2025-03-27 10:59:51,343] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:00:23,536] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:00:25,483] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:00:25,495] DEBUG in __init__: 初始化服務...
[2025-03-27 11:00:25,496] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:00:25,501] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:00:25,502] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:00:25,502] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:00:39,601] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:00:41,691] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:00:41,702] DEBUG in __init__: 初始化服務...
[2025-03-27 11:00:41,704] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:00:41,708] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:00:41,709] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:00:41,709] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:00:41,887] INFO in routes: 訪問首頁
[2025-03-27 11:00:41,888] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:00:41,889] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:00:41,889] DEBUG in routes: 開始渲染模板
[2025-03-27 11:00:41,900] DEBUG in routes: 模板渲染完成
[2025-03-27 11:00:42,553] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:01:32,585] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:01:32,595] DEBUG in __init__: 初始化服務...
[2025-03-27 11:01:32,597] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:01:32,601] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:01:32,602] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:01:32,602] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:01:34,837] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:01:34,850] DEBUG in __init__: 初始化服務...
[2025-03-27 11:01:34,851] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:01:34,855] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:01:34,856] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:01:34,856] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:01:36,202] INFO in routes: 訪問首頁
[2025-03-27 11:01:36,202] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:01:36,203] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:01:36,203] DEBUG in routes: 開始渲染模板
[2025-03-27 11:01:36,204] DEBUG in routes: 模板渲染完成
[2025-03-27 11:01:36,856] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:01:44,882] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 11:01:45,265] DEBUG in routes: Script manager initialized
[2025-03-27 11:01:45,266] DEBUG in routes: 開始處理腳本驗證請求
[2025-03-27 11:01:45,267] DEBUG in routes: 保存臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:01:45,271] DEBUG in routes: 開始驗證腳本
[2025-03-27 11:01:45,273] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:01:45,274] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:01:54,287] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 11:01:54,287] DEBUG in routes: 請求方法: POST
[2025-03-27 11:01:54,288] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '656', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryn74MbR6DVKFXaL5c', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 11:01:54,290] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', '測試腳本3'), ('tag', '報表生成')])
[2025-03-27 11:01:54,290] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'test_script.py' ('application/octet-stream')>)])
[2025-03-27 11:01:54,291] DEBUG in routes: 處理上傳文件: test_script.py
[2025-03-27 11:01:54,291] DEBUG in routes: 描述: 測試腳本3
[2025-03-27 11:01:54,291] DEBUG in routes: 標籤: ['報表生成']
[2025-03-27 11:01:54,292] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:01:54,298] DEBUG in routes: 臨時文件保存成功，大小: 241 字節
[2025-03-27 11:01:54,299] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:01:54,301] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:01:54,302] DEBUG in routes: 開始上傳腳本...
[2025-03-27 11:01:54,327] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_test_script.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_test_script.py', 'description': '測試腳本3', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 11:01:54,328] INFO in routes: 成功上傳腳本: test_script.py
[2025-03-27 11:01:54,328] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:01:54,993] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:01:55,167] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:01:57,349] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:01:57,365] DEBUG in __init__: 初始化服務...
[2025-03-27 11:01:57,367] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:01:57,371] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:01:57,372] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:01:57,372] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:01:57,481] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:01:57,497] DEBUG in __init__: 初始化服務...
[2025-03-27 11:01:57,499] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:01:57,503] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:01:57,504] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:01:57,504] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:03:16,993] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 11:03:17,386] DEBUG in routes: Script manager initialized
[2025-03-27 11:03:17,387] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 11:03:17,387] DEBUG in routes: 請求方法: POST
[2025-03-27 11:03:17,387] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '656', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundarytLANOPryE7S44l7w', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 11:03:17,388] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', '測試腳本3'), ('tag', '報表生成')])
[2025-03-27 11:03:17,388] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'test_script.py' ('application/octet-stream')>)])
[2025-03-27 11:03:17,388] DEBUG in routes: 處理上傳文件: test_script.py
[2025-03-27 11:03:17,389] DEBUG in routes: 描述: 測試腳本3
[2025-03-27 11:03:17,389] DEBUG in routes: 標籤: ['報表生成']
[2025-03-27 11:03:17,389] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:03:17,394] DEBUG in routes: 臨時文件保存成功，大小: 241 字節
[2025-03-27 11:03:17,394] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:03:17,396] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:03:17,396] DEBUG in routes: 開始上傳腳本...
[2025-03-27 11:03:17,421] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_test_script.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_test_script.py', 'description': '測試腳本3', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 11:03:17,422] INFO in routes: 成功上傳腳本: test_script.py
[2025-03-27 11:03:17,422] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:03:18,878] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:03:21,024] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:03:21,038] DEBUG in __init__: 初始化服務...
[2025-03-27 11:03:21,040] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:03:21,044] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:03:21,045] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:03:21,045] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:04:08,952] INFO in routes: 訪問首頁
[2025-03-27 11:04:08,952] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:04:08,953] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:04:08,954] DEBUG in routes: 開始渲染模板
[2025-03-27 11:04:08,964] DEBUG in routes: 模板渲染完成
[2025-03-27 11:04:09,620] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:04:26,435] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:04:28,382] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:04:28,394] DEBUG in __init__: 初始化服務...
[2025-03-27 11:04:28,396] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:04:28,401] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:04:28,401] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:04:28,402] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:04:42,423] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:04:42,434] DEBUG in __init__: 初始化服務...
[2025-03-27 11:04:42,435] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:04:42,439] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:04:42,440] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:04:42,440] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:04:44,342] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:04:44,355] DEBUG in __init__: 初始化服務...
[2025-03-27 11:04:44,356] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:04:44,360] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:04:44,361] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:04:44,361] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:14:14,619] INFO in routes: 訪問首頁
[2025-03-27 11:14:14,620] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:14:14,621] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:14:14,623] DEBUG in routes: 開始渲染模板
[2025-03-27 11:14:14,649] DEBUG in routes: 模板渲染完成
[2025-03-27 11:14:15,308] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:14:26,435] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 11:14:27,175] DEBUG in routes: Script manager initialized
[2025-03-27 11:14:27,175] DEBUG in routes: 開始處理腳本驗證請求
[2025-03-27 11:14:27,177] DEBUG in routes: 保存臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:14:27,181] DEBUG in routes: 開始驗證腳本
[2025-03-27 11:14:27,183] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:14:27,184] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:14:40,165] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 11:14:40,166] DEBUG in routes: 請求方法: POST
[2025-03-27 11:14:40,166] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '655', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundarywUmNEuo7458gGDSV', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 11:14:40,167] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', '測試腳本'), ('tag', '報表生成')])
[2025-03-27 11:14:40,167] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'test_script.py' ('application/octet-stream')>)])
[2025-03-27 11:14:40,168] DEBUG in routes: 處理上傳文件: test_script.py
[2025-03-27 11:14:40,168] DEBUG in routes: 描述: 測試腳本
[2025-03-27 11:14:40,168] DEBUG in routes: 標籤: ['報表生成']
[2025-03-27 11:14:40,168] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:14:40,172] DEBUG in routes: 臨時文件保存成功，大小: 241 字節
[2025-03-27 11:14:40,172] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:14:40,173] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:14:40,174] DEBUG in routes: 開始上傳腳本...
[2025-03-27 11:14:40,198] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_test_script.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_test_script.py', 'description': '測試腳本', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 11:14:40,199] INFO in routes: 成功上傳腳本: test_script.py
[2025-03-27 11:14:40,200] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:14:40,335] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:14:40,432] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:14:44,362] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:14:44,375] DEBUG in __init__: 初始化服務...
[2025-03-27 11:14:44,376] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:14:44,377] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:14:44,381] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:14:44,381] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:14:44,382] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:14:44,382] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:14:44,382] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:14:44,382] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:20:12,375] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:20:12,385] DEBUG in __init__: 初始化服務...
[2025-03-27 11:20:12,387] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:20:12,391] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:20:12,392] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:20:12,392] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:20:14,504] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:20:14,515] DEBUG in __init__: 初始化服務...
[2025-03-27 11:20:14,516] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:20:14,520] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:20:14,521] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:20:14,521] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:20:26,950] INFO in routes: 訪問首頁
[2025-03-27 11:20:26,950] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:20:26,951] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:20:26,951] DEBUG in routes: 開始渲染模板
[2025-03-27 11:20:26,964] DEBUG in routes: 模板渲染完成
[2025-03-27 11:20:27,623] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:20:44,749] INFO in schedule_manager: 建立排程成功: 1
[2025-03-27 11:20:58,418] INFO in schedule_manager: 刪除排程成功: 1
[2025-03-27 11:21:08,441] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 11:21:08,835] DEBUG in routes: Script manager initialized
[2025-03-27 11:21:08,836] DEBUG in routes: 開始處理腳本驗證請求
[2025-03-27 11:21:08,837] DEBUG in routes: 保存臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:08,841] DEBUG in routes: 開始驗證腳本
[2025-03-27 11:21:08,988] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:21:08,989] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:17,472] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 11:21:17,473] DEBUG in routes: 請求方法: POST
[2025-03-27 11:21:17,474] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '3727', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundary4T398s0p8NBNalkE', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 11:21:17,477] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', 'TEST3'), ('tag', '備份還原')])
[2025-03-27 11:21:17,478] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'output_booking.py' ('application/octet-stream')>)])
[2025-03-27 11:21:17,478] DEBUG in routes: 處理上傳文件: output_booking.py
[2025-03-27 11:21:17,479] DEBUG in routes: 描述: TEST3
[2025-03-27 11:21:17,479] DEBUG in routes: 標籤: ['備份還原']
[2025-03-27 11:21:17,480] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:17,487] DEBUG in routes: 臨時文件保存成功，大小: 3317 字節
[2025-03-27 11:21:17,487] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:17,491] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:21:17,492] DEBUG in routes: 開始上傳腳本...
[2025-03-27 11:21:17,523] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_output_booking.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_output_booking.py', 'description': 'TEST3', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 11:21:17,523] INFO in routes: 成功上傳腳本: output_booking.py
[2025-03-27 11:21:17,524] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:17,859] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:21:18,863] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:21:19,883] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:21:19,896] DEBUG in __init__: 初始化服務...
[2025-03-27 11:21:19,898] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:21:19,902] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:21:19,903] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:21:19,903] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:21:20,763] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:21:20,777] DEBUG in __init__: 初始化服務...
[2025-03-27 11:21:20,778] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:21:20,782] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:21:20,783] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:21:20,783] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:21:23,387] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:21:24,227] INFO in routes: 訪問首頁
[2025-03-27 11:21:24,228] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:21:24,228] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:21:24,228] DEBUG in routes: 開始渲染模板
[2025-03-27 11:21:24,239] DEBUG in routes: 模板渲染完成
[2025-03-27 11:21:24,890] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:21:25,421] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:21:25,432] DEBUG in __init__: 初始化服務...
[2025-03-27 11:21:25,434] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:21:25,438] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:21:25,439] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:21:25,439] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:21:46,839] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 11:21:47,190] DEBUG in routes: Script manager initialized
[2025-03-27 11:21:47,190] DEBUG in routes: 開始處理腳本驗證請求
[2025-03-27 11:21:47,191] DEBUG in routes: 保存臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:47,196] DEBUG in routes: 開始驗證腳本
[2025-03-27 11:21:47,288] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:21:47,289] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:55,342] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 11:21:55,343] DEBUG in routes: 請求方法: POST
[2025-03-27 11:21:55,343] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '3735', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryJHyLhb7RPYK1HaeT', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 11:21:55,345] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', '測試腳本3'), ('tag', '備份還原')])
[2025-03-27 11:21:55,345] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'output_booking.py' ('application/octet-stream')>)])
[2025-03-27 11:21:55,346] DEBUG in routes: 處理上傳文件: output_booking.py
[2025-03-27 11:21:55,346] DEBUG in routes: 描述: 測試腳本3
[2025-03-27 11:21:55,346] DEBUG in routes: 標籤: ['備份還原']
[2025-03-27 11:21:55,346] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:55,351] DEBUG in routes: 臨時文件保存成功，大小: 3317 字節
[2025-03-27 11:21:55,351] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:55,356] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:21:55,356] DEBUG in routes: 開始上傳腳本...
[2025-03-27 11:21:55,390] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_output_booking.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_output_booking.py', 'description': '測試腳本3', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 11:21:55,391] INFO in routes: 成功上傳腳本: output_booking.py
[2025-03-27 11:21:55,391] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:21:56,045] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:21:56,150] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:21:56,662] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:21:58,290] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:21:58,303] DEBUG in __init__: 初始化服務...
[2025-03-27 11:21:58,304] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:21:58,309] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:21:58,309] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:21:58,309] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:21:58,474] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:21:58,487] DEBUG in __init__: 初始化服務...
[2025-03-27 11:21:58,488] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:21:58,492] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:21:58,492] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:21:58,493] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:21:58,749] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:21:58,768] DEBUG in __init__: 初始化服務...
[2025-03-27 11:21:58,769] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:21:58,774] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:21:58,774] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:21:58,774] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:22:05,156] INFO in routes: 訪問首頁
[2025-03-27 11:22:05,157] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:22:05,157] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:22:05,158] DEBUG in routes: 開始渲染模板
[2025-03-27 11:22:05,168] DEBUG in routes: 模板渲染完成
[2025-03-27 11:22:05,819] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:22:23,102] INFO in schedule_manager: 建立排程成功: 1
[2025-03-27 11:22:46,183] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 11:22:46,727] DEBUG in routes: Script manager initialized
[2025-03-27 11:22:46,727] DEBUG in routes: 開始處理腳本驗證請求
[2025-03-27 11:22:46,728] DEBUG in routes: 保存臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:22:46,733] DEBUG in routes: 開始驗證腳本
[2025-03-27 11:22:46,855] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:22:46,856] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:22:55,098] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 11:22:55,098] DEBUG in routes: 請求方法: POST
[2025-03-27 11:22:55,099] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '3735', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryPVByN7bfBzpi5HLt', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 11:22:55,101] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', '測試腳本3'), ('tag', '備份還原')])
[2025-03-27 11:22:55,101] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'output_booking.py' ('application/octet-stream')>)])
[2025-03-27 11:22:55,102] DEBUG in routes: 處理上傳文件: output_booking.py
[2025-03-27 11:22:55,102] DEBUG in routes: 描述: 測試腳本3
[2025-03-27 11:22:55,103] DEBUG in routes: 標籤: ['備份還原']
[2025-03-27 11:22:55,103] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:22:55,110] DEBUG in routes: 臨時文件保存成功，大小: 3317 字節
[2025-03-27 11:22:55,110] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:22:55,114] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:22:55,115] DEBUG in routes: 開始上傳腳本...
[2025-03-27 11:22:55,153] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_output_booking.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_output_booking.py', 'description': '測試腳本3', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 11:22:55,153] INFO in routes: 成功上傳腳本: output_booking.py
[2025-03-27 11:22:55,154] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:22:55,701] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:22:55,853] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:22:57,129] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:22:57,990] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:22:58,003] DEBUG in __init__: 初始化服務...
[2025-03-27 11:22:58,006] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:22:58,011] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:22:58,012] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:22:58,012] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:22:58,156] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:22:58,170] DEBUG in __init__: 初始化服務...
[2025-03-27 11:22:58,172] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:22:58,176] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:22:58,177] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:22:58,177] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:22:59,089] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:22:59,101] DEBUG in __init__: 初始化服務...
[2025-03-27 11:22:59,103] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:22:59,109] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:22:59,110] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:22:59,110] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:23:24,954] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 11:23:25,540] DEBUG in routes: Script manager initialized
[2025-03-27 11:23:25,541] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 11:23:25,541] DEBUG in routes: 請求方法: POST
[2025-03-27 11:23:25,541] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '3735', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryrdMomhBa9XOhnEuw', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 11:23:25,542] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', '測試腳本3'), ('tag', '備份還原')])
[2025-03-27 11:23:25,542] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'output_booking.py' ('application/octet-stream')>)])
[2025-03-27 11:23:25,542] DEBUG in routes: 處理上傳文件: output_booking.py
[2025-03-27 11:23:25,543] DEBUG in routes: 描述: 測試腳本3
[2025-03-27 11:23:25,543] DEBUG in routes: 標籤: ['備份還原']
[2025-03-27 11:23:25,543] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:23:25,547] DEBUG in routes: 臨時文件保存成功，大小: 3317 字節
[2025-03-27 11:23:25,547] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:23:25,640] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:23:25,640] DEBUG in routes: 開始上傳腳本...
[2025-03-27 11:23:25,674] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_output_booking.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_output_booking.py', 'description': '測試腳本3', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 11:23:25,674] INFO in routes: 成功上傳腳本: output_booking.py
[2025-03-27 11:23:25,675] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_output_booking.py
[2025-03-27 11:23:26,253] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:23:26,366] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:23:27,326] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:23:28,657] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:23:28,673] DEBUG in __init__: 初始化服務...
[2025-03-27 11:23:28,675] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:23:28,680] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:23:28,680] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:23:28,680] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:23:28,768] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:23:28,785] DEBUG in __init__: 初始化服務...
[2025-03-27 11:23:28,787] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:23:28,793] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:23:28,793] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:23:28,794] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:23:29,425] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:23:29,435] DEBUG in __init__: 初始化服務...
[2025-03-27 11:23:29,438] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:23:29,443] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:23:29,444] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:23:29,444] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:24:14,521] INFO in routes: 訪問首頁
[2025-03-27 11:24:14,521] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:24:14,522] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:24:14,522] DEBUG in routes: 開始渲染模板
[2025-03-27 11:24:14,534] DEBUG in routes: 模板渲染完成
[2025-03-27 11:24:15,190] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:24:42,806] DEBUG in routes: Project root: C:\Python\Projects\Project04
[2025-03-27 11:24:43,418] DEBUG in routes: Script manager initialized
[2025-03-27 11:24:43,418] DEBUG in routes: 開始處理腳本驗證請求
[2025-03-27 11:24:43,419] DEBUG in routes: 保存臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:24:43,424] DEBUG in routes: 開始驗證腳本
[2025-03-27 11:24:43,426] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:24:43,427] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:24:52,256] DEBUG in routes: 處理腳本上傳請求
[2025-03-27 11:24:52,256] DEBUG in routes: 請求方法: POST
[2025-03-27 11:24:52,256] DEBUG in routes: 請求頭: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '655', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Sec-Ch-Ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"', 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryr7ZXltNI2Zhv6hT0', 'Sec-Ch-Ua-Mobile': '?0', 'Accept': '*/*', 'Origin': 'http://localhost:5000', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:5000/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
[2025-03-27 11:24:52,258] DEBUG in routes: 表單數據: ImmutableMultiDict([('description', '測試腳本'), ('tag', '備份還原')])
[2025-03-27 11:24:52,258] DEBUG in routes: 文件數據: ImmutableMultiDict([('file', <FileStorage: 'test_script.py' ('application/octet-stream')>)])
[2025-03-27 11:24:52,258] DEBUG in routes: 處理上傳文件: test_script.py
[2025-03-27 11:24:52,258] DEBUG in routes: 描述: 測試腳本
[2025-03-27 11:24:52,259] DEBUG in routes: 標籤: ['備份還原']
[2025-03-27 11:24:52,259] DEBUG in routes: 保存臨時文件到: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:24:52,264] DEBUG in routes: 臨時文件保存成功，大小: 241 字節
[2025-03-27 11:24:52,264] DEBUG in routes: 開始驗證腳本: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:24:52,266] DEBUG in routes: 驗證結果: {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}
[2025-03-27 11:24:52,267] DEBUG in routes: 開始上傳腳本...
[2025-03-27 11:24:52,301] DEBUG in routes: 上傳結果: {'status': 'success', 'message': '腳本上傳成功', 'data': {'name': 'temp_test_script.py', 'path': 'C:\\Python\\Projects\\Project04\\data\\scripts\\temp_test_script.py', 'description': '測試腳本', 'validation_result': {'errors': [], 'warnings': ['發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數', '發現 Python 2 風格的 print 語句，建議更新為 print() 函數'], 'missing_packages': [], 'required_packages': [], 'has_severe_errors': False, 'has_missing_packages': False}}}
[2025-03-27 11:24:52,301] INFO in routes: 成功上傳腳本: test_script.py
[2025-03-27 11:24:52,302] DEBUG in routes: 清理臨時文件: C:\Python\Projects\uploads\temp_test_script.py
[2025-03-27 11:24:52,423] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:24:53,096] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:24:53,318] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:24:54,592] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:24:54,606] DEBUG in __init__: 初始化服務...
[2025-03-27 11:24:54,608] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:24:54,612] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:24:54,612] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:24:54,612] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:24:55,373] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:24:55,385] DEBUG in __init__: 初始化服務...
[2025-03-27 11:24:55,386] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:24:55,391] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:24:55,392] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:24:55,392] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:24:55,538] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:24:55,550] DEBUG in __init__: 初始化服務...
[2025-03-27 11:24:55,552] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:24:55,558] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:24:55,559] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:24:55,559] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:24:58,894] INFO in routes: 訪問首頁
[2025-03-27 11:24:58,895] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:24:58,896] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:24:58,896] DEBUG in routes: 開始渲染模板
[2025-03-27 11:24:58,908] DEBUG in routes: 模板渲染完成
[2025-03-27 11:24:59,563] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:26:08,894] INFO in schedule_manager: 建立排程成功: 2
[2025-03-27 11:26:09,181] INFO in schedule_executor: 排程 2 執行完成，狀態: failed
[2025-03-27 11:30:26,721] INFO in routes: 訪問首頁
[2025-03-27 11:30:26,722] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:30:26,723] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:30:26,723] DEBUG in routes: 開始渲染模板
[2025-03-27 11:30:26,732] DEBUG in routes: 模板渲染完成
[2025-03-27 11:30:27,595] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:32:56,887] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:32:56,899] DEBUG in __init__: 初始化服務...
[2025-03-27 11:32:56,901] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:32:56,905] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:32:56,905] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:32:56,906] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:32:58,933] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:32:58,947] DEBUG in __init__: 初始化服務...
[2025-03-27 11:32:58,949] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:32:58,954] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:32:58,954] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:32:58,954] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:33:02,226] INFO in routes: 訪問首頁
[2025-03-27 11:33:02,226] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:33:02,227] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:33:02,228] DEBUG in routes: 開始渲染模板
[2025-03-27 11:33:02,228] DEBUG in routes: 模板渲染完成
[2025-03-27 11:33:02,882] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:33:25,970] INFO in schedule_manager: 建立排程成功: 3
[2025-03-27 11:33:26,122] INFO in schedule_executor: 排程 3 執行完成，狀態: failed
[2025-03-27 11:36:06,864] INFO in routes: 訪問首頁
[2025-03-27 11:36:06,866] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:36:06,867] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:36:06,868] DEBUG in routes: 開始渲染模板
[2025-03-27 11:36:06,870] DEBUG in routes: 模板渲染完成
[2025-03-27 11:36:07,531] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:36:57,542] INFO in schedule_manager: 切換排程 3 狀態為 停用
[2025-03-27 11:37:10,483] INFO in schedule_manager: 切換排程 3 狀態為 啟用
[2025-03-27 11:37:23,087] INFO in schedule_manager: 刪除排程成功: 3
[2025-03-27 11:37:41,525] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:37:41,541] DEBUG in __init__: 初始化服務...
[2025-03-27 11:37:41,543] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:37:41,548] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:37:41,549] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:37:41,549] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:37:43,529] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:37:43,539] DEBUG in __init__: 初始化服務...
[2025-03-27 11:37:43,541] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:37:43,545] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:37:43,545] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:37:43,545] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:37:47,994] INFO in routes: 訪問首頁
[2025-03-27 11:37:47,994] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:37:47,995] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:37:47,995] DEBUG in routes: 開始渲染模板
[2025-03-27 11:37:47,996] DEBUG in routes: 模板渲染完成
[2025-03-27 11:37:48,662] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:41:38,117] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:41:38,256] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:41:40,217] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:41:40,229] DEBUG in __init__: 初始化服務...
[2025-03-27 11:41:40,231] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:41:40,235] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:41:40,235] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:41:40,235] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:41:40,335] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:41:40,355] DEBUG in __init__: 初始化服務...
[2025-03-27 11:41:40,357] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:41:40,361] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:41:40,362] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:41:40,362] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:42:43,528] INFO in schedule_manager: 建立排程成功: 3
[2025-03-27 11:42:43,694] INFO in schedule_executor: 排程 3 執行完成，狀態: failed
[2025-03-27 11:44:21,310] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:44:21,361] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:44:23,593] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:44:23,610] DEBUG in __init__: 初始化服務...
[2025-03-27 11:44:23,612] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:44:23,616] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:44:23,617] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:44:23,617] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:44:23,629] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:44:23,645] DEBUG in __init__: 初始化服務...
[2025-03-27 11:44:23,646] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:44:23,652] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:44:23,652] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:44:23,653] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:44:23,852] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:23,918] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:28,358] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:28,665] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:33,652] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:33,915] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:38,351] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:38,672] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:43,655] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:43,918] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:48,351] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:48,662] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:53,667] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:53,933] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:58,351] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:44:58,654] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:00,910] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:45:01,844] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:45:03,120] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:45:03,138] DEBUG in __init__: 初始化服務...
[2025-03-27 11:45:03,140] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:45:03,144] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:45:03,144] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:45:03,145] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:45:03,659] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:03,926] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:03,991] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:45:04,012] DEBUG in __init__: 初始化服務...
[2025-03-27 11:45:04,014] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:45:04,019] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:45:04,020] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:45:04,029] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:45:08,351] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:08,666] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:13,658] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:13,916] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:18,130] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:45:18,285] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:45:20,376] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:45:20,392] DEBUG in __init__: 初始化服務...
[2025-03-27 11:45:20,394] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:45:20,398] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:45:20,398] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:45:20,398] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:45:20,491] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:45:20,507] DEBUG in __init__: 初始化服務...
[2025-03-27 11:45:20,508] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:45:20,512] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:45:20,513] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:45:20,513] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:45:20,697] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:20,700] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:23,653] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:23,906] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:26,453] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:45:26,574] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 11:45:28,886] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:45:28,902] DEBUG in __init__: 初始化服務...
[2025-03-27 11:45:28,904] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:45:28,907] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:45:28,908] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:45:28,908] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:45:28,982] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:45:29,006] DEBUG in __init__: 初始化服務...
[2025-03-27 11:45:29,007] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:45:29,011] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:45:29,011] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:45:29,011] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:45:29,204] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:29,206] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:33,666] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:33,935] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:38,355] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:38,667] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:40,230] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:45:40,244] DEBUG in __init__: 初始化服務...
[2025-03-27 11:45:40,246] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:45:40,250] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:45:40,250] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:45:40,250] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:45:42,243] DEBUG in __init__: 註冊藍圖...
[2025-03-27 11:45:42,258] DEBUG in __init__: 初始化服務...
[2025-03-27 11:45:42,259] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 11:45:42,264] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 11:45:42,264] INFO in __init__: 所有服務初始化完成
[2025-03-27 11:45:42,264] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 11:45:43,652] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:43,909] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:48,377] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:48,699] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:53,664] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:53,917] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:58,349] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:45:58,667] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:03,666] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:03,933] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:08,353] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:08,666] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:13,667] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:13,919] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:18,354] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:18,666] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:23,668] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:23,919] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:28,352] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:28,666] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:33,666] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:33,918] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:38,350] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:38,667] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:39,513] INFO in routes: 訪問首頁
[2025-03-27 11:46:39,513] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:46:39,514] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:46:39,514] DEBUG in routes: 開始渲染模板
[2025-03-27 11:46:39,530] DEBUG in routes: 模板渲染完成
[2025-03-27 11:46:40,188] WARNING in main_routes: 使用了棄用的 API 路徑: /api/system-resources，請使用 /schedule/api/system-resources
[2025-03-27 11:46:40,192] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:40,193] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:40,501] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:45,182] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:45,440] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:50,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:50,433] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:54,867] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:46:55,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:00,182] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:00,434] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:04,869] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:05,183] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:10,183] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:10,436] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:14,868] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:15,180] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:20,181] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:20,434] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:24,870] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:25,183] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:30,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:30,435] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:34,870] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:35,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:40,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:40,424] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:44,870] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:45,183] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:50,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:50,434] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:54,869] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:47:55,183] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:00,183] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:00,436] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:04,870] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:05,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:10,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:10,434] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:14,868] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:15,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:20,190] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:20,436] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:24,869] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:25,178] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:30,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:30,435] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:34,870] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:35,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:40,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:40,450] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:44,868] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:45,183] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:50,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:50,451] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:54,870] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:48:55,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:00,183] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:00,435] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:04,869] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:05,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:10,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:10,435] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:14,869] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:15,187] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:20,205] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:20,469] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:24,873] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:25,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:30,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:30,436] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:34,873] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:35,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:40,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:40,437] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:44,869] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:45,189] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:50,172] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:50,438] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:54,876] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:49:55,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:00,176] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:00,438] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:04,870] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:05,182] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:10,187] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:10,453] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:14,873] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:15,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:20,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:20,438] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:24,869] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:25,176] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:30,173] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:30,436] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:34,871] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:35,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:40,178] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:40,440] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:44,874] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:45,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:50,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:50,438] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:54,869] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:50:55,188] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:00,172] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:00,437] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:04,870] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:05,184] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:10,212] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:10,444] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:14,869] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:15,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:20,187] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:20,437] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:24,870] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:25,185] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:30,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:30,436] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:34,871] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:35,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:40,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:40,436] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:44,871] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:45,187] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:50,175] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:50,425] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:54,872] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:51:55,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:00,186] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:00,437] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:04,872] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:05,187] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:10,187] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:10,450] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:14,871] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:15,187] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:17,847] INFO in routes: 訪問首頁
[2025-03-27 11:52:17,847] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 11:52:17,848] DEBUG in routes: 模板是否存在: True
[2025-03-27 11:52:17,848] DEBUG in routes: 開始渲染模板
[2025-03-27 11:52:17,867] DEBUG in routes: 模板渲染完成
[2025-03-27 11:52:18,572] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:18,580] WARNING in main_routes: 使用了棄用的 API 路徑: /api/system-resources，請使用 /schedule/api/system-resources
[2025-03-27 11:52:18,588] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:18,889] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:23,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:23,788] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:28,221] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:28,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:33,523] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:33,788] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:38,221] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:38,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:43,524] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:43,786] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:48,221] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:48,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:53,526] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:53,778] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:58,222] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:52:58,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:03,526] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:03,790] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:08,216] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:08,526] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:13,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:13,788] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:18,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:18,530] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:23,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:23,793] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:28,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:28,528] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:33,522] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:33,780] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:38,213] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:38,528] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:43,214] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:43,522] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:48,213] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:48,522] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:53,224] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:53,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:58,530] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:53:58,794] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:04,076] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:04,393] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:08,527] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:08,795] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:13,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:13,529] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:18,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:18,788] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:23,222] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:23,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:28,530] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:28,786] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:33,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:33,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:38,529] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:38,793] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:43,215] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:43,524] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:48,224] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:48,529] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:53,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:53,785] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:58,224] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:54:58,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:03,524] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:03,796] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:08,224] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:08,530] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:13,549] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:13,805] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:18,217] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:18,536] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:23,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:23,790] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:28,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:28,789] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:33,238] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:33,556] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:38,525] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:38,798] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:43,226] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:43,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:48,528] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:48,790] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:53,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:53,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:58,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:55:58,789] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:03,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:03,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:08,536] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:08,789] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:13,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:13,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:18,536] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:18,789] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:23,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:23,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:28,523] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:28,778] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:33,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:33,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:38,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:38,802] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:43,226] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:43,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:48,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:48,800] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:53,221] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:53,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:58,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:56:58,788] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:03,221] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:03,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:08,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:08,794] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:13,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:13,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:18,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:18,795] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:23,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:23,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:28,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:28,795] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:33,224] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:33,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:38,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:38,806] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:43,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:43,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:48,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:48,788] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:53,226] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:53,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:58,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:57:58,790] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:03,226] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:03,546] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:08,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:08,791] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:13,234] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:13,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:18,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:18,808] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:23,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:23,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:28,528] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:28,782] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:33,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:33,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:38,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:38,792] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:43,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:43,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:48,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:48,785] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:53,216] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:53,526] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:58,222] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:58:58,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:03,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:03,780] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:08,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:08,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:13,527] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:13,777] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:18,226] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:18,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:23,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:23,787] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:28,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:28,546] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:33,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:33,800] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:38,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:38,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:43,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:43,804] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:48,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:48,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:53,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:53,793] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:58,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 11:59:58,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:03,551] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:03,793] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:08,230] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:08,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:13,526] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:13,783] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:18,230] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:18,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:23,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:23,797] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:28,226] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:28,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:33,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:33,792] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:38,230] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:38,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:43,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:43,795] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:48,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:48,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:53,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:53,793] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:58,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:00:58,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:03,532] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:03,776] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:08,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:08,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:13,532] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:13,800] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:18,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:18,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:23,530] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:23,793] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:28,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:28,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:33,527] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:33,786] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:38,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:38,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:43,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:43,795] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:48,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:48,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:53,547] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:53,796] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:58,233] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:01:58,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:03,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:03,809] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:08,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:08,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:13,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:13,794] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:18,220] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:18,528] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:23,522] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:23,780] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:28,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:28,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:33,532] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:33,779] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:38,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:38,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:43,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:43,792] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:48,219] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:48,528] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:53,529] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:53,783] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:58,218] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:02:58,527] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:03,217] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:03,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:08,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:08,536] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:13,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:13,783] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:18,218] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:18,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:23,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:23,536] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:28,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:28,787] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:33,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:33,546] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:38,527] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:38,794] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:43,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:43,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:48,527] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:48,793] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:53,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:53,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:58,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:03:58,795] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:03,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:03,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:08,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:08,794] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:13,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:13,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:18,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:18,785] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:23,230] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:23,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:28,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:28,793] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:33,231] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:33,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:38,536] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:38,804] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:43,217] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:43,525] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:48,231] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:48,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:53,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:53,804] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:58,217] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:04:58,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:03,222] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:03,528] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:08,546] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:08,787] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:13,220] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:13,527] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:18,536] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:18,799] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:23,231] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:23,550] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:28,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:28,800] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:33,239] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:33,556] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:38,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:38,794] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:43,232] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:43,551] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:48,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:48,797] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:53,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:53,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:58,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:05:58,795] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:03,230] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:03,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:08,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:08,806] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:13,218] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:13,529] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:18,230] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:18,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:23,530] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:23,796] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:28,231] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:28,547] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:33,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:33,801] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:38,231] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:38,553] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:43,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:43,801] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:48,233] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:48,547] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:53,529] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:53,800] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:58,222] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:06:58,532] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:03,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:03,795] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:08,233] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:08,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:13,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:13,804] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:18,230] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:18,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:23,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:23,805] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:28,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:28,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:33,527] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:33,794] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:38,227] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:38,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:43,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:43,790] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:48,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:48,538] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:53,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:53,797] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:58,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:07:58,547] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:03,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:03,797] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:08,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:08,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:13,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:13,803] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:18,222] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:18,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:23,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:23,796] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:28,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:28,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:33,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:33,796] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:38,226] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:38,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:43,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:43,805] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:48,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:48,530] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:53,531] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:53,785] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:58,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:08:58,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:03,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:03,796] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:08,222] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:08,547] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:13,233] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:13,547] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:18,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:18,807] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:23,233] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:23,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:28,531] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:28,814] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:33,235] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:33,551] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:38,549] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:38,799] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:43,233] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:43,550] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:48,535] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:48,791] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:53,234] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:53,555] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:58,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:09:58,807] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:03,235] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:03,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:08,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:08,811] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:13,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:13,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:18,549] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:18,814] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:23,223] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:23,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:28,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:28,804] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:33,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:33,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:38,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:38,803] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:43,235] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:43,550] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:48,531] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:48,788] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:53,236] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:53,551] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:58,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:10:58,811] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:03,226] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:03,532] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:08,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:08,796] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:13,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:13,539] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:18,546] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:18,810] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:23,234] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:23,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:28,536] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:28,799] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:33,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:33,533] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:38,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:38,804] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:43,231] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:43,550] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:48,544] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:48,805] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:53,231] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:53,550] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:58,550] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:11:58,805] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:03,237] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:03,551] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:08,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:08,795] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:13,234] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:13,552] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:18,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:18,805] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:23,229] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:23,550] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:28,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:28,809] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:33,233] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:33,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:38,552] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:38,796] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:43,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:43,549] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:48,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:48,801] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:53,234] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:53,543] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:58,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:12:58,797] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:03,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:03,547] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:08,542] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:08,804] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:13,235] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:13,552] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:18,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:18,806] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:23,238] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:23,551] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:28,534] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:28,801] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:33,232] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:33,553] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:38,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:38,793] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:43,232] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:43,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:48,545] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:48,801] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:53,230] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:53,547] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:58,549] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:13:58,800] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:03,234] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:03,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:08,528] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:08,792] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:13,226] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:13,548] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:18,541] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:18,806] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:23,235] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:23,554] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:28,551] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:28,812] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:33,268] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:33,652] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:38,551] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:38,806] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:43,224] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:43,540] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:48,532] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:48,790] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:53,235] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:53,547] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:58,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:14:58,778] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:15:03,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:15:03,537] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:44:52,225] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:44:52,424] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:26,228] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:26,569] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:36,112] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:36,376] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:38,473] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:38,791] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:44,385] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:44,645] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:49,051] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:49,367] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:54,367] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:54,630] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:59,062] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:45:59,377] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:04,370] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:04,611] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:09,054] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:09,362] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:14,368] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:14,631] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:19,050] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:19,362] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:24,360] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:24,626] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:29,054] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:29,370] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:34,368] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:34,606] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:39,059] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:39,366] ERROR in main_routes: 獲取系統資源使用狀況失敗: 'Flask' object has no attribute 'system_monitor'
[2025-03-27 12:46:55,395] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 12:46:55,397] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 12:46:59,156] DEBUG in __init__: 註冊藍圖...
[2025-03-27 12:46:59,157] DEBUG in __init__: 註冊藍圖...
[2025-03-27 12:46:59,171] DEBUG in __init__: 初始化服務...
[2025-03-27 12:46:59,172] DEBUG in __init__: 初始化服務...
[2025-03-27 12:46:59,173] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 12:46:59,173] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 12:46:59,177] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 12:46:59,177] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 12:46:59,178] INFO in __init__: 所有服務初始化完成
[2025-03-27 12:46:59,178] INFO in __init__: 所有服務初始化完成
[2025-03-27 12:46:59,178] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 12:46:59,178] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 12:47:09,319] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 12:47:09,333] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 12:47:12,360] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 12:47:12,490] INFO in schedule_executor: 排程執行器已停止
[2025-03-27 13:19:29,821] DEBUG in __init__: 註冊藍圖...
[2025-03-27 13:19:29,836] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 13:19:29,928] INFO in __init__: API模組已初始化
[2025-03-27 13:19:29,928] INFO in __init__: API模組初始化完成
[2025-03-27 13:19:29,931] INFO in __init__: 服務初始化完成
[2025-03-27 13:26:43,905] INFO in routes: 訪問首頁
[2025-03-27 13:26:43,905] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:26:43,906] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:26:43,906] DEBUG in routes: 開始渲染模板
[2025-03-27 13:26:43,926] DEBUG in routes: 模板渲染完成
[2025-03-27 13:26:44,606] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:26:44,607] WARNING in version: 使用了棄用的 API 路徑: /api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:26:44,608] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/schedule，請使用 /api/v1/schedules
[2025-03-27 13:26:44,679] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:26:44,871] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:26:44,927] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/schedule，請使用 /api/v1/schedules
[2025-03-27 13:26:44,929] ERROR in schedules: 獲取排程失敗: 服務 schedule_service 未註冊
[2025-03-27 13:26:44,938] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:26:45,251] ERROR in schedules: 獲取排程失敗: 服務 schedule_service 未註冊
[2025-03-27 13:26:48,533] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:26:49,291] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:26:49,606] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:26:54,297] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:26:54,617] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:26:57,205] WARNING in version: 使用了棄用的 API 路徑: /script/validate，請使用 /api/v1/scripts/validate
[2025-03-27 13:26:57,527] ERROR in scripts: 獲取腳本 validate 失敗: 服務 script_service 未註冊
[2025-03-27 13:26:59,296] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:26:59,605] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:04,290] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:04,622] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:05,969] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:27:09,614] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:09,879] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:14,602] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:14,855] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:19,614] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:19,878] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:24,617] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:24,886] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:29,612] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:29,880] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:34,607] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:34,871] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:39,609] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:39,875] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:44,611] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:44,874] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:49,618] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:49,884] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:54,615] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:54,880] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:59,601] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:27:59,866] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:04,609] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:04,873] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:09,613] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:09,877] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:14,608] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:14,870] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:19,612] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:19,861] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:24,614] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:24,880] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:29,608] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:29,871] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:34,604] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:34,867] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:39,607] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:39,873] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:44,608] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:44,873] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:49,605] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:49,868] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:54,611] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:54,874] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:59,605] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:28:59,870] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:04,614] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:04,878] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:09,615] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:09,879] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:14,606] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:14,873] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:19,614] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:19,876] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:24,607] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:24,873] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:29,604] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:29,870] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:34,619] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:29:34,888] WARNING in version: 使用了棄用的 API 路徑: /schedule/api/api/v1/system/resources，請使用 /api/v1/system/resources
[2025-03-27 13:35:01,337] DEBUG in __init__: 註冊藍圖...
[2025-03-27 13:35:01,351] DEBUG in __init__: 藍圖註冊完成
[2025-03-27 13:35:01,398] INFO in __init__: API模組已初始化
[2025-03-27 13:35:01,398] INFO in __init__: API模組初始化完成
[2025-03-27 13:35:01,401] ERROR in __init__: 系統監控服務初始化失敗: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
[2025-03-27 13:35:01,403] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 13:35:01,403] ERROR in __init__: 腳本執行服務初始化失敗: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
[2025-03-27 13:35:01,404] ERROR in __init__: 排程管理服務初始化失敗: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
[2025-03-27 13:35:01,409] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 13:35:01,410] ERROR in __init__: 排程執行服務初始化失敗: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
[2025-03-27 13:35:01,410] INFO in __init__: 服務初始化完成
[2025-03-27 13:35:04,287] INFO in routes: 訪問首頁
[2025-03-27 13:35:04,288] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:35:04,288] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:35:04,289] DEBUG in routes: 開始渲染模板
[2025-03-27 13:35:04,305] DEBUG in routes: 模板渲染完成
[2025-03-27 13:35:04,996] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:35:05,293] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:35:07,517] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:35:25,931] INFO in routes: 訪問首頁
[2025-03-27 13:35:25,932] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:35:25,933] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:35:25,933] DEBUG in routes: 開始渲染模板
[2025-03-27 13:35:25,934] DEBUG in routes: 模板渲染完成
[2025-03-27 13:35:26,597] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:35:26,926] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:42:52,460] INFO in __init__: 應用程式日誌系統初始化完成
[2025-03-27 13:42:52,474] INFO in api_logger: API日誌記錄器已設置
[2025-03-27 13:42:52,474] INFO in __init__: API日誌記錄器初始化完成
[2025-03-27 13:42:52,717] DEBUG in __init__: 註冊藍圖...
[2025-03-27 13:42:52,721] DEBUG in __init__: 核心藍圖註冊完成
[2025-03-27 13:42:52,724] DEBUG in __init__: 主頁藍圖註冊完成
[2025-03-27 13:42:52,732] DEBUG in __init__: 排程藍圖註冊完成
[2025-03-27 13:42:52,734] DEBUG in __init__: 腳本藍圖註冊完成
[2025-03-27 13:42:52,735] DEBUG in __init__: 系統藍圖註冊完成
[2025-03-27 13:42:52,736] DEBUG in __init__: 所有藍圖註冊完成
[2025-03-27 13:42:52,790] INFO in __init__: API模組已初始化
[2025-03-27 13:42:52,790] INFO in __init__: API模組初始化完成
[2025-03-27 13:42:52,791] INFO in __init__: 開始初始化服務...
[2025-03-27 13:42:52,793] ERROR in __init__: 系統監控服務初始化失敗: SystemMonitor.__init__() takes 1 positional argument but 2 were given
[2025-03-27 13:42:52,794] ERROR in __init__: 腳本執行器初始化失敗: ScriptExecutor.__init__() takes 1 positional argument but 2 were given
[2025-03-27 13:42:52,794] INFO in __init__: 服務已註冊: schedule_manager
[2025-03-27 13:42:52,795] INFO in __init__: 排程管理器初始化完成
[2025-03-27 13:42:52,797] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 13:42:52,802] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 13:42:52,803] INFO in __init__: 服務已註冊: schedule_executor
[2025-03-27 13:42:52,804] INFO in __init__: 排程執行器初始化完成
[2025-03-27 13:42:52,805] INFO in __init__: 所有服務初始化完成，已註冊 2 個服務
[2025-03-27 13:42:52,805] INFO in __init__: 服務初始化完成
[2025-03-27 13:42:52,854] INFO in __init__: 資料庫表格初始化完成
[2025-03-27 13:46:29,878] INFO in __init__: 應用程式日誌系統初始化完成
[2025-03-27 13:46:29,884] INFO in api_logger: API日誌記錄器已設置
[2025-03-27 13:46:29,885] INFO in __init__: API日誌記錄器初始化完成
[2025-03-27 13:47:10,012] INFO in __init__: 應用程式日誌系統初始化完成
[2025-03-27 13:47:10,016] INFO in api_logger: API日誌記錄器已設置
[2025-03-27 13:47:10,016] INFO in __init__: API日誌記錄器初始化完成
[2025-03-27 13:47:10,237] DEBUG in __init__: 註冊藍圖...
[2025-03-27 13:47:10,241] DEBUG in __init__: 核心藍圖註冊完成
[2025-03-27 13:47:10,245] DEBUG in __init__: 主頁藍圖註冊完成
[2025-03-27 13:47:10,251] DEBUG in __init__: 排程藍圖註冊完成
[2025-03-27 13:47:10,253] DEBUG in __init__: 腳本藍圖註冊完成
[2025-03-27 13:47:10,255] DEBUG in __init__: 系統藍圖註冊完成
[2025-03-27 13:47:10,255] DEBUG in __init__: 所有藍圖註冊完成
[2025-03-27 13:47:10,277] INFO in __init__: API模組已初始化
[2025-03-27 13:47:10,277] INFO in __init__: API模組初始化完成
[2025-03-27 13:47:10,278] INFO in __init__: 開始初始化服務...
[2025-03-27 13:47:10,291] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-03-27 13:47:10,291] INFO in __init__: 服務已註冊: system_monitor
[2025-03-27 13:47:10,292] INFO in __init__: 系統監控服務初始化完成
[2025-03-27 13:47:10,292] ERROR in __init__: 腳本執行器初始化失敗: ScriptExecutor.__init__() takes 1 positional argument but 2 were given
[2025-03-27 13:47:10,293] INFO in __init__: 服務已註冊: schedule_manager
[2025-03-27 13:47:10,293] INFO in __init__: 排程管理器初始化完成
[2025-03-27 13:47:10,295] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 13:47:10,300] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 13:47:10,300] INFO in __init__: 服務已註冊: schedule_executor
[2025-03-27 13:47:10,301] INFO in __init__: 排程執行器初始化完成
[2025-03-27 13:47:10,301] INFO in __init__: 所有服務初始化完成，已註冊 3 個服務
[2025-03-27 13:47:10,302] INFO in __init__: 服務初始化完成
[2025-03-27 13:47:10,343] INFO in __init__: 資料庫表格初始化完成
[2025-03-27 13:47:12,133] INFO in routes: 訪問首頁
[2025-03-27 13:47:12,134] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:47:12,135] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:47:12,136] DEBUG in routes: 開始渲染模板
[2025-03-27 13:47:12,149] DEBUG in routes: 模板渲染完成
[2025-03-27 13:47:12,860] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:47:13,156] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:47:15,202] INFO in routes: 訪問首頁
[2025-03-27 13:47:15,203] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:47:15,204] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:47:15,204] DEBUG in routes: 開始渲染模板
[2025-03-27 13:47:15,205] DEBUG in routes: 模板渲染完成
[2025-03-27 13:47:15,881] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:47:16,164] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:47:19,093] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:51:53,443] INFO in __init__: 應用程式日誌系統初始化完成
[2025-03-27 13:51:53,447] INFO in api_logger: API日誌記錄器已設置
[2025-03-27 13:51:53,448] INFO in __init__: API日誌記錄器初始化完成
[2025-03-27 13:51:53,671] DEBUG in __init__: 註冊藍圖...
[2025-03-27 13:51:53,674] DEBUG in __init__: 核心藍圖註冊完成
[2025-03-27 13:51:53,677] DEBUG in __init__: 主頁藍圖註冊完成
[2025-03-27 13:51:53,684] DEBUG in __init__: 排程藍圖註冊完成
[2025-03-27 13:51:53,686] DEBUG in __init__: 腳本藍圖註冊完成
[2025-03-27 13:51:53,688] DEBUG in __init__: 系統藍圖註冊完成
[2025-03-27 13:51:53,688] DEBUG in __init__: 所有藍圖註冊完成
[2025-03-27 13:51:53,705] INFO in __init__: API模組已初始化
[2025-03-27 13:51:53,706] INFO in __init__: API模組初始化完成
[2025-03-27 13:51:53,706] INFO in __init__: 開始初始化服務...
[2025-03-27 13:51:53,709] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-03-27 13:51:53,710] INFO in __init__: 服務已註冊: system_monitor
[2025-03-27 13:51:53,710] INFO in __init__: 服務已註冊: SystemMonitor
[2025-03-27 13:51:53,711] INFO in __init__: 系統監控服務初始化完成
[2025-03-27 13:51:53,711] ERROR in __init__: 腳本執行器初始化失敗: ScriptExecutor.__init__() takes 1 positional argument but 2 were given
[2025-03-27 13:51:53,712] INFO in __init__: 服務已註冊: schedule_manager
[2025-03-27 13:51:53,712] INFO in __init__: 服務已註冊: schedule_service
[2025-03-27 13:51:53,712] INFO in __init__: 排程管理器初始化完成
[2025-03-27 13:51:53,714] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\uploads
輸出目錄：C:\Python\Projects\Project04\outputs
超時時間：5秒
[2025-03-27 13:51:53,718] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 13:51:53,719] INFO in __init__: 服務已註冊: schedule_executor
[2025-03-27 13:51:53,720] INFO in __init__: 服務已註冊: execution_service
[2025-03-27 13:51:53,720] INFO in __init__: 排程執行器初始化完成
[2025-03-27 13:51:53,721] INFO in __init__: 所有服務初始化完成，已註冊 6 個服務
[2025-03-27 13:51:53,721] INFO in __init__: 服務初始化完成
[2025-03-27 13:51:53,760] INFO in __init__: 資料庫表格初始化完成
[2025-03-27 13:52:03,979] INFO in routes: 訪問首頁
[2025-03-27 13:52:03,980] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:52:03,981] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:52:03,981] DEBUG in routes: 開始渲染模板
[2025-03-27 13:52:03,993] DEBUG in routes: 模板渲染完成
[2025-03-27 13:52:17,156] INFO in routes: 訪問首頁
[2025-03-27 13:52:17,157] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:52:17,157] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:52:17,158] DEBUG in routes: 開始渲染模板
[2025-03-27 13:52:17,159] DEBUG in routes: 模板渲染完成
[2025-03-27 13:52:17,845] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:52:18,163] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:52:19,915] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:52:22,222] INFO in routes: 訪問首頁
[2025-03-27 13:52:22,223] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:52:22,223] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:52:22,224] DEBUG in routes: 開始渲染模板
[2025-03-27 13:52:22,224] DEBUG in routes: 模板渲染完成
[2025-03-27 13:52:22,904] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:52:23,226] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:52:27,574] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:52:43,764] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:52:50,049] INFO in routes: 訪問首頁
[2025-03-27 13:52:50,049] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:52:50,050] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:52:50,051] DEBUG in routes: 開始渲染模板
[2025-03-27 13:52:50,051] DEBUG in routes: 模板渲染完成
[2025-03-27 13:52:50,758] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:52:51,039] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:52:51,940] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:57:40,274] INFO in __init__: 應用程式日誌系統初始化完成
[2025-03-27 13:57:40,280] INFO in api_logger: API日誌記錄器已設置
[2025-03-27 13:57:40,281] INFO in __init__: API日誌記錄器初始化完成
[2025-03-27 13:57:40,281] INFO in __init__: 開始初始化服務...
[2025-03-27 13:57:40,309] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-03-27 13:57:40,310] INFO in __init__: 服務已註冊: system_monitor
[2025-03-27 13:57:40,310] INFO in __init__: 服務已註冊: SystemMonitor
[2025-03-27 13:57:40,311] INFO in __init__: 系統監控服務初始化完成
[2025-03-27 13:57:40,313] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-03-27 13:57:40,314] INFO in __init__: 服務已註冊: script_executor
[2025-03-27 13:57:40,315] INFO in __init__: 服務已註冊: script_service
[2025-03-27 13:57:40,315] INFO in __init__: 腳本執行器初始化完成
[2025-03-27 13:57:40,315] INFO in __init__: 服務已註冊: schedule_manager
[2025-03-27 13:57:40,316] INFO in __init__: 服務已註冊: schedule_service
[2025-03-27 13:57:40,316] INFO in __init__: 排程管理器初始化完成
[2025-03-27 13:57:40,321] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 13:57:40,322] INFO in __init__: 服務已註冊: schedule_executor
[2025-03-27 13:57:40,322] INFO in __init__: 服務已註冊: execution_service
[2025-03-27 13:57:40,322] INFO in __init__: 排程執行器初始化完成
[2025-03-27 13:57:40,323] INFO in __init__: 所有服務初始化完成，已註冊 8 個服務
[2025-03-27 13:57:40,323] INFO in __init__: 服務初始化完成
[2025-03-27 13:57:40,545] DEBUG in __init__: 註冊藍圖...
[2025-03-27 13:57:40,548] DEBUG in __init__: 核心藍圖註冊完成
[2025-03-27 13:57:40,551] DEBUG in __init__: 主頁藍圖註冊完成
[2025-03-27 13:57:40,559] DEBUG in __init__: 排程藍圖註冊完成
[2025-03-27 13:57:40,561] DEBUG in __init__: 腳本藍圖註冊完成
[2025-03-27 13:57:40,562] DEBUG in __init__: 系統藍圖註冊完成
[2025-03-27 13:57:40,563] DEBUG in __init__: 所有藍圖註冊完成
[2025-03-27 13:57:40,580] INFO in __init__: API模組已初始化
[2025-03-27 13:57:40,581] INFO in __init__: API模組初始化完成
[2025-03-27 13:57:40,588] INFO in __init__: 資料庫表格初始化完成
[2025-03-27 13:57:52,679] INFO in routes: 訪問首頁
[2025-03-27 13:57:52,679] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:57:52,680] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:57:52,681] DEBUG in routes: 開始渲染模板
[2025-03-27 13:57:52,693] DEBUG in routes: 模板渲染完成
[2025-03-27 13:57:53,402] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:57:53,667] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:57:58,605] INFO in routes: 訪問首頁
[2025-03-27 13:57:58,606] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 13:57:58,607] DEBUG in routes: 模板是否存在: True
[2025-03-27 13:57:58,608] DEBUG in routes: 開始渲染模板
[2025-03-27 13:57:58,609] DEBUG in routes: 模板渲染完成
[2025-03-27 13:57:59,272] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:57:59,601] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 13:58:05,674] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:00:32,247] INFO in __init__: 應用程式日誌系統初始化完成
[2025-03-27 14:00:32,252] INFO in api_logger: API日誌記錄器已設置
[2025-03-27 14:00:32,252] INFO in __init__: API日誌記錄器初始化完成
[2025-03-27 14:00:32,253] DEBUG in __init__: 初始化服務...
[2025-03-27 14:00:32,281] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-03-27 14:00:32,282] INFO in __init__: 服務已註冊: system_monitor
[2025-03-27 14:00:32,282] INFO in __init__: 服務已註冊: SystemMonitor
[2025-03-27 14:00:32,283] INFO in __init__: 服務已註冊: system_service
[2025-03-27 14:00:32,283] INFO in __init__: 系統監控服務初始化完成
[2025-03-27 14:00:32,285] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-03-27 14:00:32,286] INFO in __init__: 服務已註冊: script_executor
[2025-03-27 14:00:32,286] INFO in __init__: 服務已註冊: script_service
[2025-03-27 14:00:32,287] INFO in __init__: 腳本執行器初始化完成
[2025-03-27 14:00:32,287] INFO in __init__: 服務已註冊: schedule_manager
[2025-03-27 14:00:32,287] INFO in __init__: 服務已註冊: schedule_service
[2025-03-27 14:00:32,288] INFO in __init__: 排程管理器初始化完成
[2025-03-27 14:00:32,292] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 14:00:32,292] INFO in __init__: 服務已註冊: schedule_executor
[2025-03-27 14:00:32,293] INFO in __init__: 服務已註冊: execution_service
[2025-03-27 14:00:32,293] INFO in __init__: 排程執行器初始化完成
[2025-03-27 14:00:32,294] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-03-27 14:00:32,294] INFO in __init__: 服務初始化完成
[2025-03-27 14:00:32,537] DEBUG in __init__: 註冊藍圖...
[2025-03-27 14:00:32,542] DEBUG in __init__: 核心藍圖註冊完成
[2025-03-27 14:00:32,545] DEBUG in __init__: 主頁藍圖註冊完成
[2025-03-27 14:00:32,555] DEBUG in __init__: 排程藍圖註冊完成
[2025-03-27 14:00:32,557] DEBUG in __init__: 腳本藍圖註冊完成
[2025-03-27 14:00:32,558] DEBUG in __init__: 系統藍圖註冊完成
[2025-03-27 14:00:32,559] DEBUG in __init__: 所有藍圖註冊完成
[2025-03-27 14:00:32,576] INFO in __init__: API模組已初始化
[2025-03-27 14:00:32,577] INFO in __init__: API模組初始化完成
[2025-03-27 14:00:32,584] INFO in __init__: 資料庫表格初始化完成
[2025-03-27 14:01:10,276] INFO in routes: 訪問首頁
[2025-03-27 14:01:10,276] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 14:01:10,277] DEBUG in routes: 模板是否存在: True
[2025-03-27 14:01:10,278] DEBUG in routes: 開始渲染模板
[2025-03-27 14:01:10,290] DEBUG in routes: 模板渲染完成
[2025-03-27 14:01:10,988] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:01:11,299] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:01:44,464] INFO in routes: 訪問首頁
[2025-03-27 14:01:44,464] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 14:01:44,465] DEBUG in routes: 模板是否存在: True
[2025-03-27 14:01:44,466] DEBUG in routes: 開始渲染模板
[2025-03-27 14:01:44,467] DEBUG in routes: 模板渲染完成
[2025-03-27 14:01:45,162] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:01:45,445] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:01:47,471] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:01:58,683] INFO in routes: 訪問首頁
[2025-03-27 14:01:58,684] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 14:01:58,685] DEBUG in routes: 模板是否存在: True
[2025-03-27 14:01:58,686] DEBUG in routes: 開始渲染模板
[2025-03-27 14:01:58,686] DEBUG in routes: 模板渲染完成
[2025-03-27 14:01:59,345] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:01:59,653] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:03:27,915] INFO in __init__: 應用程式日誌系統初始化完成
[2025-03-27 14:03:27,918] INFO in api_logger: API日誌記錄器已設置
[2025-03-27 14:03:27,919] INFO in __init__: API日誌記錄器初始化完成
[2025-03-27 14:03:27,919] DEBUG in __init__: 初始化服務...
[2025-03-27 14:03:27,943] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-03-27 14:03:27,943] INFO in __init__: 服務已註冊: system_monitor
[2025-03-27 14:03:27,944] INFO in __init__: 服務已註冊: SystemMonitor
[2025-03-27 14:03:27,945] INFO in __init__: 服務已註冊: system_service
[2025-03-27 14:03:27,945] INFO in __init__: 系統監控服務初始化完成
[2025-03-27 14:03:27,947] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-03-27 14:03:27,948] INFO in __init__: 服務已註冊: script_executor
[2025-03-27 14:03:27,948] INFO in __init__: 服務已註冊: script_service
[2025-03-27 14:03:27,948] INFO in __init__: 腳本執行器初始化完成
[2025-03-27 14:03:27,948] INFO in __init__: 服務已註冊: schedule_manager
[2025-03-27 14:03:27,949] INFO in __init__: 服務已註冊: schedule_service
[2025-03-27 14:03:27,949] INFO in __init__: 排程管理器初始化完成
[2025-03-27 14:03:27,953] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 14:03:27,953] INFO in __init__: 服務已註冊: schedule_executor
[2025-03-27 14:03:27,954] INFO in __init__: 服務已註冊: execution_service
[2025-03-27 14:03:27,954] INFO in __init__: 排程執行器初始化完成
[2025-03-27 14:03:27,954] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-03-27 14:03:27,955] INFO in __init__: 服務初始化完成
[2025-03-27 14:03:28,305] DEBUG in __init__: 註冊藍圖...
[2025-03-27 14:03:28,308] DEBUG in __init__: 核心藍圖註冊完成
[2025-03-27 14:03:28,312] DEBUG in __init__: 主頁藍圖註冊完成
[2025-03-27 14:03:28,320] DEBUG in __init__: 排程藍圖註冊完成
[2025-03-27 14:03:28,322] DEBUG in __init__: 腳本藍圖註冊完成
[2025-03-27 14:03:28,323] DEBUG in __init__: 系統藍圖註冊完成
[2025-03-27 14:03:28,324] DEBUG in __init__: 所有藍圖註冊完成
[2025-03-27 14:03:28,388] INFO in __init__: API模組已初始化
[2025-03-27 14:03:28,394] INFO in __init__: API模組初始化完成
[2025-03-27 14:03:28,400] INFO in __init__: 資料庫表格初始化完成
[2025-03-27 14:10:55,287] INFO in routes: 訪問首頁
[2025-03-27 14:10:55,288] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 14:10:55,289] DEBUG in routes: 模板是否存在: True
[2025-03-27 14:10:55,289] DEBUG in routes: 開始渲染模板
[2025-03-27 14:10:55,290] DEBUG in routes: 模板渲染完成
[2025-03-27 14:10:55,959] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:10:56,272] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:10:58,776] INFO in routes: 訪問首頁
[2025-03-27 14:10:58,776] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 14:10:58,777] DEBUG in routes: 模板是否存在: True
[2025-03-27 14:10:58,778] DEBUG in routes: 開始渲染模板
[2025-03-27 14:10:58,779] DEBUG in routes: 模板渲染完成
[2025-03-27 14:10:59,449] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:10:59,728] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:11:01,878] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:11:06,591] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    scripts = Script.query.all()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:15:07,584] INFO in __init__: 應用程式日誌系統初始化完成
[2025-03-27 14:15:07,588] INFO in api_logger: API日誌記錄器已設置
[2025-03-27 14:15:07,589] INFO in __init__: API日誌記錄器初始化完成
[2025-03-27 14:15:07,590] DEBUG in __init__: 初始化服務...
[2025-03-27 14:15:07,617] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-03-27 14:15:07,617] INFO in __init__: 服務已註冊: system_monitor
[2025-03-27 14:15:07,618] INFO in __init__: 服務已註冊: SystemMonitor
[2025-03-27 14:15:07,618] INFO in __init__: 服務已註冊: system_service
[2025-03-27 14:15:07,619] INFO in __init__: 系統監控服務初始化完成
[2025-03-27 14:15:07,621] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-03-27 14:15:07,622] INFO in __init__: 服務已註冊: script_executor
[2025-03-27 14:15:07,623] INFO in __init__: 服務已註冊: script_service
[2025-03-27 14:15:07,623] INFO in __init__: 腳本執行器初始化完成
[2025-03-27 14:15:07,624] INFO in __init__: 服務已註冊: schedule_manager
[2025-03-27 14:15:07,624] INFO in __init__: 服務已註冊: schedule_service
[2025-03-27 14:15:07,625] INFO in __init__: 排程管理器初始化完成
[2025-03-27 14:15:07,630] INFO in schedule_executor: 排程執行器已啟動
[2025-03-27 14:15:07,631] INFO in __init__: 服務已註冊: schedule_executor
[2025-03-27 14:15:07,632] INFO in __init__: 服務已註冊: execution_service
[2025-03-27 14:15:07,632] INFO in __init__: 排程執行器初始化完成
[2025-03-27 14:15:07,633] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-03-27 14:15:07,633] INFO in __init__: 服務初始化完成
[2025-03-27 14:15:07,893] DEBUG in __init__: 註冊藍圖...
[2025-03-27 14:15:07,898] DEBUG in __init__: 核心藍圖註冊完成
[2025-03-27 14:15:07,902] DEBUG in __init__: 主頁藍圖註冊完成
[2025-03-27 14:15:07,909] DEBUG in __init__: 排程藍圖註冊完成
[2025-03-27 14:15:07,911] DEBUG in __init__: 腳本藍圖註冊完成
[2025-03-27 14:15:07,913] DEBUG in __init__: 系統藍圖註冊完成
[2025-03-27 14:15:07,913] DEBUG in __init__: 所有藍圖註冊完成
[2025-03-27 14:15:07,928] INFO in __init__: API模組已初始化
[2025-03-27 14:15:07,928] INFO in __init__: API模組初始化完成
[2025-03-27 14:15:07,935] INFO in __init__: 資料庫表格初始化完成
[2025-03-27 14:19:56,371] INFO in routes: 訪問首頁
[2025-03-27 14:19:56,372] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 14:19:56,373] DEBUG in routes: 模板是否存在: True
[2025-03-27 14:19:56,374] DEBUG in routes: 開始渲染模板
[2025-03-27 14:19:56,375] DEBUG in routes: 模板渲染完成
[2025-03-27 14:19:57,043] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    @bp.route('/', methods=['GET'])
                  ^^^^^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:19:57,358] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    @bp.route('/', methods=['GET'])
                  ^^^^^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:52:26,096] INFO in routes: 訪問首頁
[2025-03-27 14:52:26,097] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-03-27 14:52:26,098] DEBUG in routes: 模板是否存在: True
[2025-03-27 14:52:26,098] DEBUG in routes: 開始渲染模板
[2025-03-27 14:52:26,099] DEBUG in routes: 模板渲染完成
[2025-03-27 14:52:26,818] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    @bp.route('/', methods=['GET'])
                  ^^^^^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-03-27 14:52:27,067] ERROR in app: Exception on /script/ [GET]
Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: scripts.file_path

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\app\routes\script_routes.py", line 16, in get_scripts
    @bp.route('/', methods=['GET'])
                  ^^^^^^^^^^^^^^^^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2773, in all
    return self._iter().all()
           ~~~~~~~~~~^^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
        statement,
        params,
        execution_options={"_sa_orm_load_options": self.load_options},
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1717, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1710, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, multiparams, params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1577, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1953, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, statement, parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2134, in _handle_dbapi_exception
    util.raise_(
    ~~~~~~~~~~~^
        sqlalchemy_exception, with_traceback=exc_info[2], from_=e
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1910, in _execute_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, statement, parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python\Projects\Project04\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:32:46,666] INFO in __init__: 應用程式日誌系統初始化完成
[2025-06-12 13:32:46,674] INFO in api_logger: API日誌記錄器已設置
[2025-06-12 13:32:46,674] INFO in __init__: API日誌記錄器初始化完成
[2025-06-12 13:32:46,674] DEBUG in __init__: 初始化服務...
[2025-06-12 13:32:46,791] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-06-12 13:32:46,792] INFO in __init__: 服務已註冊: system_monitor
[2025-06-12 13:32:46,792] INFO in __init__: 服務已註冊: SystemMonitor
[2025-06-12 13:32:46,793] INFO in __init__: 服務已註冊: system_service
[2025-06-12 13:32:46,794] INFO in __init__: 系統監控服務初始化完成
[2025-06-12 13:32:46,798] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-06-12 13:32:46,798] INFO in __init__: 服務已註冊: script_executor
[2025-06-12 13:32:46,798] INFO in __init__: 服務已註冊: script_service
[2025-06-12 13:32:46,798] INFO in __init__: 腳本執行器初始化完成
[2025-06-12 13:32:46,799] INFO in __init__: 服務已註冊: schedule_manager
[2025-06-12 13:32:46,799] INFO in __init__: 服務已註冊: schedule_service
[2025-06-12 13:32:46,799] INFO in __init__: 排程管理器初始化完成
[2025-06-12 13:32:46,804] INFO in schedule_executor: 排程執行器已啟動
[2025-06-12 13:32:46,806] INFO in __init__: 服務已註冊: schedule_executor
[2025-06-12 13:32:46,807] INFO in __init__: 服務已註冊: execution_service
[2025-06-12 13:32:46,807] INFO in __init__: 排程執行器初始化完成
[2025-06-12 13:32:46,807] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-06-12 13:32:46,808] INFO in __init__: 服務初始化完成
[2025-06-12 13:32:47,293] DEBUG in __init__: 註冊藍圖...
[2025-06-12 13:32:47,301] DEBUG in __init__: 核心藍圖註冊完成
[2025-06-12 13:32:47,314] DEBUG in __init__: 主頁藍圖註冊完成
[2025-06-12 13:32:47,330] DEBUG in __init__: 排程藍圖註冊完成
[2025-06-12 13:32:47,334] DEBUG in __init__: 腳本藍圖註冊完成
[2025-06-12 13:32:47,338] DEBUG in __init__: 系統藍圖註冊完成
[2025-06-12 13:32:47,339] DEBUG in __init__: 所有藍圖註冊完成
[2025-06-12 13:32:47,427] INFO in __init__: API模組已初始化
[2025-06-12 13:32:47,430] INFO in __init__: API模組初始化完成
[2025-06-12 13:32:47,440] INFO in __init__: 資料庫表格初始化完成
[2025-06-12 13:33:45,948] INFO in routes: 訪問首頁
[2025-06-12 13:33:45,949] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-06-12 13:33:45,950] DEBUG in routes: 模板是否存在: True
[2025-06-12 13:33:45,952] DEBUG in routes: 開始渲染模板
[2025-06-12 13:33:45,967] DEBUG in routes: 模板渲染完成
[2025-06-12 13:33:47,094] INFO in api_logger: [1749706427093] 請求開始: GET /script/
[2025-06-12 13:33:47,099] DEBUG in api_logger: [1749706427093] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:47,108] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:47,110] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.13ms
[2025-06-12 13:33:47,110] INFO in api_logger: [1749706427093] 請求完成: GET /script/ - 耗時: 8.70ms
[2025-06-12 13:33:47,140] INFO in api_logger: [1749706427140] 請求開始: GET /script/
[2025-06-12 13:33:47,141] DEBUG in api_logger: [1749706427140] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:47,151] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:47,153] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 8.91ms
[2025-06-12 13:33:47,153] INFO in api_logger: [1749706427140] 請求完成: GET /script/ - 耗時: 9.46ms
[2025-06-12 13:33:58,585] INFO in api_logger: [1749706438585] 請求開始: GET /script/
[2025-06-12 13:33:58,586] DEBUG in api_logger: [1749706438585] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:33:58,595] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:33:58,597] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.67ms
[2025-06-12 13:33:58,598] INFO in api_logger: [1749706438585] 請求完成: GET /script/ - 耗時: 7.81ms
[2025-06-12 13:35:31,994] INFO in api_logger: [1749706531994] 請求開始: GET /script/
[2025-06-12 13:35:31,995] DEBUG in api_logger: [1749706531994] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-06-12 13:35:32,002] ERROR in script_routes: 獲取腳本列表失敗: (sqlite3.OperationalError) no such column: scripts.file_path
[SQL: SELECT scripts.id AS scripts_id, scripts.name AS scripts_name, scripts.description AS scripts_description, scripts.file_path AS scripts_file_path, scripts.created_at AS scripts_created_at, scripts.updated_at AS scripts_updated_at, scripts.is_active AS scripts_is_active 
FROM scripts]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
[2025-06-12 13:35:32,003] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 6.99ms
[2025-06-12 13:35:32,004] INFO in api_logger: [1749706531994] 請求完成: GET /script/ - 耗時: 7.50ms
