[2025-07-08 17:25:46,688] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:25:46,695] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:25:46,695] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:25:46,696] DEBUG in __init__: 初始化服務...
[2025-07-08 17:25:46,723] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:25:46,724] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:25:46,725] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:25:46,725] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:25:46,725] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:25:46,726] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:25:46,738] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:25:46,738] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:25:46,738] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:25:46,739] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:25:46,739] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:25:46,739] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:25:46,744] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:25:46,744] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:25:46,745] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:25:46,745] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:25:46,745] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:25:46,746] INFO in __init__: 服務初始化完成
[2025-07-08 17:25:46,931] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:25:46,935] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:25:46,939] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:25:46,946] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:25:46,948] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:25:46,950] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:25:46,950] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:25:46,966] INFO in __init__: API模組已初始化
[2025-07-08 17:25:46,967] INFO in __init__: API模組初始化完成
[2025-07-08 17:25:46,975] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:25:48,703] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:25:48,707] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:25:48,708] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:25:48,708] DEBUG in __init__: 初始化服務...
[2025-07-08 17:25:48,733] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:25:48,734] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:25:48,734] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:25:48,734] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:25:48,735] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:25:48,736] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:25:48,737] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:25:48,737] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:25:48,737] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:25:48,737] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:25:48,737] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:25:48,738] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:25:48,743] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:25:48,744] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:25:48,745] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:25:48,745] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:25:48,745] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:25:48,745] INFO in __init__: 服務初始化完成
[2025-07-08 17:25:49,001] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:25:49,004] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:25:49,008] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:25:49,016] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:25:49,019] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:25:49,020] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:25:49,020] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:25:49,037] INFO in __init__: API模組已初始化
[2025-07-08 17:25:49,037] INFO in __init__: API模組初始化完成
[2025-07-08 17:25:49,045] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:27:22,651] INFO in api_logger: [1751966842650] 請求開始: GET /script/
[2025-07-08 17:27:22,652] DEBUG in api_logger: [1751966842650] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:27:22,658] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:27:22,658] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:27:22,659] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:27:22,668] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:27:22,681] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.02ms
[2025-07-08 17:27:22,681] INFO in api_logger: [1751966842650] 請求完成: GET /script/ - 耗時: 28.36ms
[2025-07-08 17:27:50,356] INFO in api_logger: [1751966870354] 請求開始: POST /script/validate
[2025-07-08 17:27:50,357] DEBUG in api_logger: [1751966870354] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryLcY9lSmD2QSnM7n2", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:27:50,358] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:27:50,368] DEBUG in script_executor: 驗證腳本: C:\TEMP\tmp2ngqkmxz.py
[2025-07-08 17:27:50,551] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:27:50,553] INFO in api_logger: [1751966870354] 請求完成: POST /script/validate - 耗時: 195.31ms
[2025-07-08 17:28:15,184] INFO in api_logger: [1751966895183] 請求開始: POST /script/
[2025-07-08 17:28:15,185] DEBUG in api_logger: [1751966895183] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary8qDAcFudVHMVG5E3", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "備份還原"}}
[2025-07-08 17:28:15,186] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:28:15,187] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:28:15,192] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:28:15,220] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:28:15,224] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.99ms
[2025-07-08 17:28:15,225] INFO in api_logger: [1751966895183] 請求完成: POST /script/ - 耗時: 38.29ms
[2025-07-08 17:28:19,418] INFO in api_logger: [1751966899418] 請求開始: GET /script/
[2025-07-08 17:28:19,419] DEBUG in api_logger: [1751966899418] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:28:19,424] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:28:19,424] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:28:19,425] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:28:19,437] INFO in script_routes: 成功獲取腳本列表，共 9 個腳本
[2025-07-08 17:28:19,447] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.50ms
[2025-07-08 17:28:19,447] INFO in api_logger: [1751966899418] 請求完成: GET /script/ - 耗時: 27.86ms
[2025-07-08 17:28:52,417] INFO in api_logger: [1751966932417] 請求開始: GET /script/
[2025-07-08 17:28:52,417] DEBUG in api_logger: [1751966932417] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:28:52,422] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:28:52,423] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:28:52,423] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:28:52,435] INFO in script_routes: 成功獲取腳本列表，共 9 個腳本
[2025-07-08 17:28:52,447] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 29.13ms
[2025-07-08 17:28:52,448] INFO in api_logger: [1751966932417] 請求完成: GET /script/ - 耗時: 29.46ms
[2025-07-08 17:29:02,780] INFO in api_logger: [1751966942779] 請求開始: GET /script/
[2025-07-08 17:29:02,780] DEBUG in api_logger: [1751966942779] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:02,785] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:02,786] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:02,786] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:02,795] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:29:02,807] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 26.33ms
[2025-07-08 17:29:02,807] INFO in api_logger: [1751966942779] 請求完成: GET /script/ - 耗時: 26.69ms
[2025-07-08 17:29:07,351] INFO in api_logger: [1751966947351] 請求開始: GET /script/
[2025-07-08 17:29:07,352] DEBUG in api_logger: [1751966947351] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:07,356] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:07,357] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:07,358] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:07,368] INFO in script_routes: 成功獲取腳本列表，共 8 個腳本
[2025-07-08 17:29:07,380] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.31ms
[2025-07-08 17:29:07,380] INFO in api_logger: [1751966947351] 請求完成: GET /script/ - 耗時: 27.77ms
[2025-07-08 17:29:25,416] INFO in api_logger: [1751966965416] 請求開始: GET /script/
[2025-07-08 17:29:25,417] DEBUG in api_logger: [1751966965416] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:25,423] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:25,423] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:25,424] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:25,437] INFO in script_routes: 成功獲取腳本列表，共 7 個腳本
[2025-07-08 17:29:25,454] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 36.29ms
[2025-07-08 17:29:25,455] INFO in api_logger: [1751966965416] 請求完成: GET /script/ - 耗時: 36.92ms
[2025-07-08 17:29:30,514] INFO in api_logger: [1751966970513] 請求開始: GET /script/
[2025-07-08 17:29:30,515] DEBUG in api_logger: [1751966970513] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:30,523] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:30,524] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:30,525] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:30,531] INFO in script_routes: 成功獲取腳本列表，共 7 個腳本
[2025-07-08 17:29:30,542] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.21ms
[2025-07-08 17:29:30,542] INFO in api_logger: [1751966970513] 請求完成: GET /script/ - 耗時: 25.51ms
[2025-07-08 17:29:37,899] INFO in api_logger: [1751966977898] 請求開始: GET /script/
[2025-07-08 17:29:37,899] DEBUG in api_logger: [1751966977898] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:37,905] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:37,906] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:37,907] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:37,914] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:29:37,927] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 26.33ms
[2025-07-08 17:29:37,927] INFO in api_logger: [1751966977898] 請求完成: GET /script/ - 耗時: 26.75ms
[2025-07-08 17:29:45,453] INFO in api_logger: [1751966985453] 請求開始: GET /script/
[2025-07-08 17:29:45,453] DEBUG in api_logger: [1751966985453] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:45,457] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:45,458] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:45,458] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:45,467] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:29:45,475] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.49ms
[2025-07-08 17:29:45,475] INFO in api_logger: [1751966985453] 請求完成: GET /script/ - 耗時: 20.95ms
[2025-07-08 17:29:47,866] INFO in api_logger: [1751966987866] 請求開始: GET /script/
[2025-07-08 17:29:47,867] DEBUG in api_logger: [1751966987866] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:47,877] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:29:47,878] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:29:47,879] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:29:47,885] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:29:47,893] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.27ms
[2025-07-08 17:29:47,893] INFO in api_logger: [1751966987866] 請求完成: GET /script/ - 耗時: 23.61ms
[2025-07-08 17:29:56,285] INFO in api_logger: [1751966996284] 請求開始: POST /script/validate
[2025-07-08 17:29:56,285] DEBUG in api_logger: [1751966996284] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryVwkCa1kJG0QIR41j", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:29:56,287] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:29:56,293] DEBUG in script_executor: 驗證腳本: C:\TEMP\tmp57wa8kgj.py
[2025-07-08 17:29:56,318] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:29:56,325] INFO in api_logger: [1751966996284] 請求完成: POST /script/validate - 耗時: 37.17ms
[2025-07-08 17:30:06,052] INFO in api_logger: [1751967006052] 請求開始: POST /script/
[2025-07-08 17:30:06,053] DEBUG in api_logger: [1751967006052] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryTPk7n5a2gwsHxavY", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:30:06,054] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:30:06,055] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:30:06,061] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:30:06,094] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:30:06,101] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 46.65ms
[2025-07-08 17:30:06,102] INFO in api_logger: [1751967006052] 請求完成: POST /script/ - 耗時: 47.32ms
[2025-07-08 17:30:06,623] INFO in schedule_executor: 排程執行器已停止
[2025-07-08 17:30:08,345] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:30:08,348] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:30:08,349] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:30:08,349] DEBUG in __init__: 初始化服務...
[2025-07-08 17:30:08,372] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:30:08,372] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:30:08,372] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:30:08,373] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:30:08,373] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:30:08,374] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:30:08,375] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:30:08,375] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:30:08,376] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:30:08,376] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:30:08,376] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:30:08,376] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:30:08,381] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:30:08,382] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:30:08,382] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:30:08,382] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:30:08,383] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:30:08,383] INFO in __init__: 服務初始化完成
[2025-07-08 17:30:08,669] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:30:08,673] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:30:08,676] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:30:08,685] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:30:08,688] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:30:08,689] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:30:08,689] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:30:08,709] INFO in __init__: API模組已初始化
[2025-07-08 17:30:08,710] INFO in __init__: API模組初始化完成
[2025-07-08 17:30:08,716] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:30:11,866] INFO in api_logger: [1751967011862] 請求開始: GET /script/
[2025-07-08 17:30:11,867] DEBUG in api_logger: [1751967011862] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:30:11,873] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:30:11,874] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:30:11,874] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:30:11,882] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:30:11,893] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.41ms
[2025-07-08 17:30:11,894] INFO in api_logger: [1751967011862] 請求完成: GET /script/ - 耗時: 25.77ms
[2025-07-08 17:31:59,234] INFO in schedule_executor: 排程執行器已停止
[2025-07-08 17:32:00,894] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:32:00,897] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:32:00,898] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:32:00,898] DEBUG in __init__: 初始化服務...
[2025-07-08 17:32:00,920] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:32:00,920] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:32:00,921] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:32:00,921] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:32:00,922] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:32:00,923] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:32:00,924] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:32:00,924] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:32:00,924] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:32:00,925] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:32:00,925] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:32:00,926] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:32:00,929] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:32:00,931] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:32:00,931] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:32:00,931] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:32:00,931] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:32:00,932] INFO in __init__: 服務初始化完成
[2025-07-08 17:32:01,121] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:32:01,124] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:32:01,127] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:32:01,138] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:32:01,140] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:32:01,141] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:32:01,142] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:32:01,165] INFO in __init__: API模組已初始化
[2025-07-08 17:32:01,166] INFO in __init__: API模組初始化完成
[2025-07-08 17:32:01,172] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:32:04,336] INFO in api_logger: [1751967124333] 請求開始: GET /script/
[2025-07-08 17:32:04,337] DEBUG in api_logger: [1751967124333] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:32:04,344] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:32:04,344] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:32:04,345] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:32:04,354] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:32:04,365] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.14ms
[2025-07-08 17:32:04,366] INFO in api_logger: [1751967124333] 請求完成: GET /script/ - 耗時: 27.53ms
[2025-07-08 17:32:15,738] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:32:15,741] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:32:15,742] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:32:15,742] DEBUG in __init__: 初始化服務...
[2025-07-08 17:32:15,774] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:32:15,775] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:32:15,775] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:32:15,776] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:32:15,776] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:32:15,777] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:32:15,778] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:32:15,778] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:32:15,779] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:32:15,779] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:32:15,779] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:32:15,779] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:32:15,786] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:32:15,787] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:32:15,787] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:32:15,787] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:32:15,788] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:32:15,788] INFO in __init__: 服務初始化完成
[2025-07-08 17:32:15,971] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:32:15,974] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:32:15,977] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:32:15,986] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:32:15,988] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:32:15,989] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:32:15,989] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:32:16,007] INFO in __init__: API模組已初始化
[2025-07-08 17:32:16,007] INFO in __init__: API模組初始化完成
[2025-07-08 17:32:16,012] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:32:17,624] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:32:17,627] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:32:17,627] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:32:17,627] DEBUG in __init__: 初始化服務...
[2025-07-08 17:32:17,648] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:32:17,649] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:32:17,649] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:32:17,650] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:32:17,650] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:32:17,652] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:32:17,653] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:32:17,653] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:32:17,653] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:32:17,654] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:32:17,654] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:32:17,654] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:32:17,658] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:32:17,659] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:32:17,659] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:32:17,659] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:32:17,659] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:32:17,660] INFO in __init__: 服務初始化完成
[2025-07-08 17:32:17,843] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:32:17,848] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:32:17,852] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:32:17,859] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:32:17,862] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:32:17,863] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:32:17,863] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:32:17,878] INFO in __init__: API模組已初始化
[2025-07-08 17:32:17,879] INFO in __init__: API模組初始化完成
[2025-07-08 17:32:17,886] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:32:19,517] INFO in api_logger: [1751967139514] 請求開始: GET /script/
[2025-07-08 17:32:19,518] DEBUG in api_logger: [1751967139514] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:32:19,526] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:32:19,527] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:32:19,528] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:32:19,536] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:32:19,547] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.28ms
[2025-07-08 17:32:19,547] INFO in api_logger: [1751967139514] 請求完成: GET /script/ - 耗時: 27.68ms
[2025-07-08 17:33:20,097] INFO in routes: 訪問首頁
[2025-07-08 17:33:20,097] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-08 17:33:20,098] DEBUG in routes: 模板是否存在: True
[2025-07-08 17:33:20,098] DEBUG in routes: 開始渲染模板
[2025-07-08 17:33:20,109] DEBUG in routes: 模板渲染完成
[2025-07-08 17:33:20,798] INFO in api_logger: [1751967200798] 請求開始: GET /script/
[2025-07-08 17:33:20,802] DEBUG in api_logger: [1751967200798] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:20,813] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:20,814] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:20,815] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:20,821] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:33:20,840] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.03ms
[2025-07-08 17:33:20,840] INFO in api_logger: [1751967200798] 請求完成: GET /script/ - 耗時: 35.47ms
[2025-07-08 17:33:21,114] INFO in api_logger: [1751967201114] 請求開始: GET /script/
[2025-07-08 17:33:21,115] DEBUG in api_logger: [1751967201114] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:21,120] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:21,120] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:21,121] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:21,125] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:33:21,134] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 17.78ms
[2025-07-08 17:33:21,134] INFO in api_logger: [1751967201114] 請求完成: GET /script/ - 耗時: 18.10ms
[2025-07-08 17:33:22,700] INFO in api_logger: [1751967202700] 請求開始: GET /script/
[2025-07-08 17:33:22,701] DEBUG in api_logger: [1751967202700] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:22,704] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:22,705] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:22,705] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:22,714] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:33:22,724] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.05ms
[2025-07-08 17:33:22,724] INFO in api_logger: [1751967202700] 請求完成: GET /script/ - 耗時: 23.35ms
[2025-07-08 17:33:27,929] INFO in api_logger: [1751967207929] 請求開始: GET /script/
[2025-07-08 17:33:27,931] DEBUG in api_logger: [1751967207929] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:27,939] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:33:27,940] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:33:27,942] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:33:27,948] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:33:27,958] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.94ms
[2025-07-08 17:33:27,959] INFO in api_logger: [1751967207929] 請求完成: GET /script/ - 耗時: 26.42ms
[2025-07-08 17:33:49,764] INFO in api_logger: [1751967229763] 請求開始: POST /script/validate
[2025-07-08 17:33:49,764] DEBUG in api_logger: [1751967229763] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary3sPCws3SRUfyU2MC", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:33:49,765] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:33:49,772] DEBUG in script_executor: 驗證腳本: C:\TEMP\tmpq5yszt_2.py
[2025-07-08 17:33:49,877] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:33:49,880] INFO in api_logger: [1751967229763] 請求完成: POST /script/validate - 耗時: 115.32ms
[2025-07-08 17:34:26,171] INFO in api_logger: [1751967266170] 請求開始: POST /script/
[2025-07-08 17:34:26,172] DEBUG in api_logger: [1751967266170] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryoTDraM0oAHMcyZCu", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:34:26,173] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:34:26,174] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:34:26,178] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:34:26,186] INFO in script_routes: 腳本上傳調試信息:
[2025-07-08 17:34:26,187] INFO in script_routes:   文件名: output_www.py
[2025-07-08 17:34:26,187] INFO in script_routes:   描述: 官網資訊爬取
[2025-07-08 17:34:26,188] INFO in script_routes:   標籤: 未分類
[2025-07-08 17:34:26,188] INFO in script_routes:   表單數據: {'description': '官網資訊爬取', 'tag': '網頁爬蟲'}
[2025-07-08 17:34:26,208] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:34:26,214] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 40.85ms
[2025-07-08 17:34:26,215] INFO in api_logger: [1751967266170] 請求完成: POST /script/ - 耗時: 41.95ms
[2025-07-08 17:34:27,625] INFO in schedule_executor: 排程執行器已停止
[2025-07-08 17:34:29,468] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:34:29,471] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:34:29,471] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:34:29,471] DEBUG in __init__: 初始化服務...
[2025-07-08 17:34:29,498] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:34:29,498] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:34:29,498] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:34:29,499] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:34:29,499] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:34:29,500] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:34:29,501] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:34:29,501] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:34:29,502] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:34:29,502] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:34:29,502] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:34:29,502] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:34:29,507] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:34:29,507] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:34:29,508] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:34:29,508] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:34:29,508] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:34:29,508] INFO in __init__: 服務初始化完成
[2025-07-08 17:34:29,704] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:34:29,708] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:34:29,712] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:34:29,719] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:34:29,721] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:34:29,722] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:34:29,723] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:34:29,741] INFO in __init__: API模組已初始化
[2025-07-08 17:34:29,741] INFO in __init__: API模組初始化完成
[2025-07-08 17:34:29,749] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:34:42,617] INFO in api_logger: [1751967282614] 請求開始: GET /script/
[2025-07-08 17:34:42,618] DEBUG in api_logger: [1751967282614] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:34:42,624] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:34:42,625] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:34:42,626] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:34:42,634] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:34:42,656] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 36.75ms
[2025-07-08 17:34:42,657] INFO in api_logger: [1751967282614] 請求完成: GET /script/ - 耗時: 37.35ms
[2025-07-08 17:37:33,113] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:37:33,116] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:37:33,116] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:37:33,116] DEBUG in __init__: 初始化服務...
[2025-07-08 17:37:33,143] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:37:33,143] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:37:33,144] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:37:33,144] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:37:33,144] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:37:33,145] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:37:33,146] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:37:33,147] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:37:33,147] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:37:33,147] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:37:33,147] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:37:33,148] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:37:33,154] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:37:33,154] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:37:33,155] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:37:33,155] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:37:33,155] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:37:33,156] INFO in __init__: 服務初始化完成
[2025-07-08 17:37:33,337] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:37:33,340] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:37:33,344] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:37:33,351] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:37:33,354] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:37:33,355] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:37:33,355] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:37:33,373] INFO in __init__: API模組已初始化
[2025-07-08 17:37:33,374] INFO in __init__: API模組初始化完成
[2025-07-08 17:37:33,381] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:37:34,922] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:37:34,925] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:37:34,926] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:37:34,926] DEBUG in __init__: 初始化服務...
[2025-07-08 17:37:34,950] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:37:34,950] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:37:34,951] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:37:34,951] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:37:34,951] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:37:34,954] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:37:34,955] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:37:34,956] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:37:34,956] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:37:34,956] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:37:34,957] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:37:34,957] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:37:34,962] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:37:34,963] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:37:34,964] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:37:34,964] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:37:34,965] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:37:34,965] INFO in __init__: 服務初始化完成
[2025-07-08 17:37:35,139] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:37:35,143] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:37:35,146] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:37:35,153] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:37:35,155] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:37:35,157] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:37:35,157] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:37:35,180] INFO in __init__: API模組已初始化
[2025-07-08 17:37:35,181] INFO in __init__: API模組初始化完成
[2025-07-08 17:37:35,187] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:38:02,045] INFO in routes: 訪問首頁
[2025-07-08 17:38:02,046] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-08 17:38:02,048] DEBUG in routes: 模板是否存在: True
[2025-07-08 17:38:02,049] DEBUG in routes: 開始渲染模板
[2025-07-08 17:38:02,068] DEBUG in routes: 模板渲染完成
[2025-07-08 17:38:02,698] INFO in api_logger: [1751967482696] 請求開始: GET /script/
[2025-07-08 17:38:02,699] DEBUG in api_logger: [1751967482696] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:02,704] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:02,704] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:02,705] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:02,715] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:02,727] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 27.82ms
[2025-07-08 17:38:02,728] INFO in api_logger: [1751967482696] 請求完成: GET /script/ - 耗時: 28.32ms
[2025-07-08 17:38:02,930] INFO in api_logger: [1751967482930] 請求開始: GET /script/
[2025-07-08 17:38:02,931] DEBUG in api_logger: [1751967482930] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:02,944] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:02,948] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:02,950] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:02,956] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:02,970] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.97ms
[2025-07-08 17:38:02,970] INFO in api_logger: [1751967482930] 請求完成: GET /script/ - 耗時: 36.48ms
[2025-07-08 17:38:05,028] INFO in api_logger: [1751967485027] 請求開始: GET /script/
[2025-07-08 17:38:05,029] DEBUG in api_logger: [1751967485027] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:05,037] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:05,037] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:05,038] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:05,045] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:05,055] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.80ms
[2025-07-08 17:38:05,055] INFO in api_logger: [1751967485027] 請求完成: GET /script/ - 耗時: 24.13ms
[2025-07-08 17:38:09,075] INFO in api_logger: [1751967489075] 請求開始: GET /script/
[2025-07-08 17:38:09,076] DEBUG in api_logger: [1751967489075] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:09,080] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:09,081] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:09,081] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:09,085] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:38:09,092] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.70ms
[2025-07-08 17:38:09,093] INFO in api_logger: [1751967489075] 請求完成: GET /script/ - 耗時: 16.07ms
[2025-07-08 17:38:15,879] INFO in api_logger: [1751967495878] 請求開始: POST /script/validate
[2025-07-08 17:38:15,880] DEBUG in api_logger: [1751967495878] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7026", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryHg3UCjCJQ4isrzxt", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:15,881] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:38:15,889] DEBUG in script_executor: 驗證腳本: C:\TEMP\tmphrrkdxd5.py
[2025-07-08 17:38:16,021] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:38:16,024] INFO in api_logger: [1751967495878] 請求完成: POST /script/validate - 耗時: 143.41ms
[2025-07-08 17:38:26,197] INFO in api_logger: [1751967506195] 請求開始: POST /script/
[2025-07-08 17:38:26,198] DEBUG in api_logger: [1751967506195] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7244", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarypOeYwEgYY6OQrAuc", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:38:26,200] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:38:26,200] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:38:26,207] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:38:26,216] INFO in script_routes: 腳本上傳調試信息:
[2025-07-08 17:38:26,217] INFO in script_routes:   文件名: output_www.py
[2025-07-08 17:38:26,217] INFO in script_routes:   描述: 官網資訊爬取
[2025-07-08 17:38:26,217] INFO in script_routes:   標籤: 未分類
[2025-07-08 17:38:26,218] INFO in script_routes:   表單數據: {'description': '官網資訊爬取', 'tag': '網頁爬蟲'}
[2025-07-08 17:38:26,233] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:38:26,237] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.38ms
[2025-07-08 17:38:26,237] INFO in api_logger: [1751967506195] 請求完成: POST /script/ - 耗時: 37.71ms
[2025-07-08 17:38:27,366] INFO in schedule_executor: 排程執行器已停止
[2025-07-08 17:38:29,086] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:38:29,089] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:38:29,090] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:38:29,090] DEBUG in __init__: 初始化服務...
[2025-07-08 17:38:29,117] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:38:29,118] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:38:29,119] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:38:29,119] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:38:29,119] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:38:29,121] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:38:29,122] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:38:29,123] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:38:29,123] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:38:29,124] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:38:29,124] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:38:29,124] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:38:29,130] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:38:29,130] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:38:29,131] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:38:29,131] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:38:29,131] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:38:29,132] INFO in __init__: 服務初始化完成
[2025-07-08 17:38:29,316] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:38:29,319] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:38:29,323] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:38:29,331] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:38:29,334] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:38:29,335] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:38:29,335] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:38:29,354] INFO in __init__: API模組已初始化
[2025-07-08 17:38:29,355] INFO in __init__: API模組初始化完成
[2025-07-08 17:38:29,363] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:38:39,361] INFO in routes: 訪問首頁
[2025-07-08 17:38:39,363] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-08 17:38:39,364] DEBUG in routes: 模板是否存在: True
[2025-07-08 17:38:39,365] DEBUG in routes: 開始渲染模板
[2025-07-08 17:38:39,380] DEBUG in routes: 模板渲染完成
[2025-07-08 17:38:40,064] INFO in api_logger: [1751967520061] 請求開始: GET /script/
[2025-07-08 17:38:40,066] DEBUG in api_logger: [1751967520061] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:40,076] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:40,077] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:40,079] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:40,086] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:40,103] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 35.37ms
[2025-07-08 17:38:40,104] INFO in api_logger: [1751967520061] 請求完成: GET /script/ - 耗時: 36.79ms
[2025-07-08 17:38:40,378] INFO in api_logger: [1751967520378] 請求開始: GET /script/
[2025-07-08 17:38:40,379] DEBUG in api_logger: [1751967520378] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:40,383] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:40,384] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:40,384] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:40,390] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:40,400] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 20.22ms
[2025-07-08 17:38:40,400] INFO in api_logger: [1751967520378] 請求完成: GET /script/ - 耗時: 20.55ms
[2025-07-08 17:38:41,629] INFO in api_logger: [1751967521629] 請求開始: GET /script/
[2025-07-08 17:38:41,630] DEBUG in api_logger: [1751967521629] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:38:41,636] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:38:41,637] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:38:41,638] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:38:41,646] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:38:41,655] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.02ms
[2025-07-08 17:38:41,655] INFO in api_logger: [1751967521629] 請求完成: GET /script/ - 耗時: 23.37ms
[2025-07-08 17:39:30,262] INFO in api_logger: [1751967570261] 請求開始: GET /script/
[2025-07-08 17:39:30,263] DEBUG in api_logger: [1751967570261] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:39:30,270] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:39:30,271] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:39:30,272] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:39:30,278] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:39:30,286] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 21.14ms
[2025-07-08 17:39:30,286] INFO in api_logger: [1751967570261] 請求完成: GET /script/ - 耗時: 21.46ms
[2025-07-08 17:39:34,815] INFO in routes: 訪問首頁
[2025-07-08 17:39:34,816] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-08 17:39:34,817] DEBUG in routes: 模板是否存在: True
[2025-07-08 17:39:34,819] DEBUG in routes: 開始渲染模板
[2025-07-08 17:39:34,820] DEBUG in routes: 模板渲染完成
[2025-07-08 17:39:35,545] INFO in api_logger: [1751967575545] 請求開始: GET /script/
[2025-07-08 17:39:35,548] DEBUG in api_logger: [1751967575545] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:39:35,555] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:39:35,556] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:39:35,557] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:39:35,563] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:39:35,574] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 25.18ms
[2025-07-08 17:39:35,574] INFO in api_logger: [1751967575545] 請求完成: GET /script/ - 耗時: 25.66ms
[2025-07-08 17:39:35,897] INFO in api_logger: [1751967575896] 請求開始: GET /script/
[2025-07-08 17:39:35,897] DEBUG in api_logger: [1751967575896] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:39:35,905] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:39:35,906] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:39:35,907] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:39:35,915] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:39:35,923] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.96ms
[2025-07-08 17:39:35,923] INFO in api_logger: [1751967575896] 請求完成: GET /script/ - 耗時: 24.28ms
[2025-07-08 17:39:37,079] INFO in api_logger: [1751967577078] 請求開始: GET /script/
[2025-07-08 17:39:37,080] DEBUG in api_logger: [1751967577078] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:39:37,085] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:39:37,086] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:39:37,087] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:39:37,090] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:39:37,103] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.43ms
[2025-07-08 17:39:37,103] INFO in api_logger: [1751967577078] 請求完成: GET /script/ - 耗時: 22.95ms
[2025-07-08 17:40:01,531] INFO in api_logger: [1751967601531] 請求開始: GET /script/
[2025-07-08 17:40:01,532] DEBUG in api_logger: [1751967601531] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:40:01,547] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:40:01,548] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:40:01,550] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:40:01,556] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:40:01,571] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 37.89ms
[2025-07-08 17:40:01,571] INFO in api_logger: [1751967601531] 請求完成: GET /script/ - 耗時: 38.29ms
[2025-07-08 17:42:48,063] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:42:48,066] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:42:48,067] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:42:48,067] DEBUG in __init__: 初始化服務...
[2025-07-08 17:42:48,093] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:42:48,094] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:42:48,094] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:42:48,095] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:42:48,095] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:42:48,097] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:42:48,097] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:42:48,098] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:42:48,098] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:42:48,098] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:42:48,099] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:42:48,099] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:42:48,103] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:42:48,104] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:42:48,104] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:42:48,104] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:42:48,105] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:42:48,105] INFO in __init__: 服務初始化完成
[2025-07-08 17:42:48,302] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:42:48,305] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:42:48,310] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:42:48,318] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:42:48,321] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:42:48,322] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:42:48,322] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:42:48,343] INFO in __init__: API模組已初始化
[2025-07-08 17:42:48,344] INFO in __init__: API模組初始化完成
[2025-07-08 17:42:48,350] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:42:50,353] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-08 17:42:50,356] INFO in api_logger: API日誌記錄器已設置
[2025-07-08 17:42:50,356] INFO in __init__: API日誌記錄器初始化完成
[2025-07-08 17:42:50,356] DEBUG in __init__: 初始化服務...
[2025-07-08 17:42:50,385] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-08 17:42:50,385] INFO in __init__: 服務已註冊: system_monitor
[2025-07-08 17:42:50,385] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-08 17:42:50,386] INFO in __init__: 服務已註冊: system_service
[2025-07-08 17:42:50,386] INFO in __init__: 系統監控服務初始化完成
[2025-07-08 17:42:50,387] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-08 17:42:50,387] INFO in __init__: 服務已註冊: script_executor
[2025-07-08 17:42:50,387] INFO in __init__: 服務已註冊: script_service
[2025-07-08 17:42:50,388] INFO in __init__: 腳本執行器初始化完成
[2025-07-08 17:42:50,388] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-08 17:42:50,388] INFO in __init__: 服務已註冊: schedule_service
[2025-07-08 17:42:50,388] INFO in __init__: 排程管理器初始化完成
[2025-07-08 17:42:50,395] INFO in schedule_executor: 排程執行器已啟動
[2025-07-08 17:42:50,395] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-08 17:42:50,396] INFO in __init__: 服務已註冊: execution_service
[2025-07-08 17:42:50,396] INFO in __init__: 排程執行器初始化完成
[2025-07-08 17:42:50,397] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-08 17:42:50,397] INFO in __init__: 服務初始化完成
[2025-07-08 17:42:50,601] DEBUG in __init__: 註冊藍圖...
[2025-07-08 17:42:50,604] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-08 17:42:50,609] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-08 17:42:50,616] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-08 17:42:50,618] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-08 17:42:50,619] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-08 17:42:50,619] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-08 17:42:50,640] INFO in __init__: API模組已初始化
[2025-07-08 17:42:50,640] INFO in __init__: API模組初始化完成
[2025-07-08 17:42:50,648] INFO in __init__: 資料庫表格初始化完成
[2025-07-08 17:42:50,868] INFO in routes: 訪問首頁
[2025-07-08 17:42:50,868] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-08 17:42:50,869] DEBUG in routes: 模板是否存在: True
[2025-07-08 17:42:50,869] DEBUG in routes: 開始渲染模板
[2025-07-08 17:42:50,882] DEBUG in routes: 模板渲染完成
[2025-07-08 17:42:51,596] INFO in api_logger: [1751967771591] 請求開始: GET /script/
[2025-07-08 17:42:51,597] DEBUG in api_logger: [1751967771591] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:42:51,606] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:42:51,606] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:42:51,609] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:42:51,615] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:42:51,628] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 28.67ms
[2025-07-08 17:42:51,628] INFO in api_logger: [1751967771591] 請求完成: GET /script/ - 耗時: 29.25ms
[2025-07-08 17:42:51,912] INFO in api_logger: [1751967771911] 請求開始: GET /script/
[2025-07-08 17:42:51,913] DEBUG in api_logger: [1751967771911] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:42:51,936] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:42:51,947] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:42:51,983] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:42:52,013] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:42:52,048] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 131.99ms
[2025-07-08 17:42:52,049] INFO in api_logger: [1751967771911] 請求完成: GET /script/ - 耗時: 132.74ms
[2025-07-08 17:43:15,960] INFO in routes: 訪問首頁
[2025-07-08 17:43:15,961] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-08 17:43:15,962] DEBUG in routes: 模板是否存在: True
[2025-07-08 17:43:15,962] DEBUG in routes: 開始渲染模板
[2025-07-08 17:43:15,963] DEBUG in routes: 模板渲染完成
[2025-07-08 17:43:16,638] INFO in api_logger: [1751967796638] 請求開始: GET /script/
[2025-07-08 17:43:16,640] DEBUG in api_logger: [1751967796638] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:16,658] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:43:16,662] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:43:16,663] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:43:16,668] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:43:16,686] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 44.70ms
[2025-07-08 17:43:16,687] INFO in api_logger: [1751967796638] 請求完成: GET /script/ - 耗時: 45.17ms
[2025-07-08 17:43:16,975] INFO in api_logger: [1751967796975] 請求開始: GET /script/
[2025-07-08 17:43:16,975] DEBUG in api_logger: [1751967796975] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:16,979] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:43:16,980] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:43:16,980] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:43:16,985] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:43:16,995] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 18.71ms
[2025-07-08 17:43:16,995] INFO in api_logger: [1751967796975] 請求完成: GET /script/ - 耗時: 19.03ms
[2025-07-08 17:43:18,291] INFO in api_logger: [1751967798290] 請求開始: GET /script/
[2025-07-08 17:43:18,291] DEBUG in api_logger: [1751967798290] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:18,296] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:43:18,297] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:43:18,298] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:43:18,304] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-08 17:43:18,317] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 24.72ms
[2025-07-08 17:43:18,318] INFO in api_logger: [1751967798290] 請求完成: GET /script/ - 耗時: 25.31ms
[2025-07-08 17:43:31,409] INFO in api_logger: [1751967811408] 請求開始: POST /script/validate
[2025-07-08 17:43:31,409] DEBUG in api_logger: [1751967811408] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7073", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary1GFDZw0fo6W8ansH", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:31,410] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-08 17:43:31,416] DEBUG in script_executor: 驗證腳本: C:\TEMP\tmp41jcfic8.py
[2025-07-08 17:43:31,519] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-08 17:43:31,522] INFO in api_logger: [1751967811408] 請求完成: POST /script/validate - 耗時: 112.45ms
[2025-07-08 17:43:40,230] INFO in api_logger: [1751967820230] 請求開始: POST /script/
[2025-07-08 17:43:40,231] DEBUG in api_logger: [1751967820230] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7291", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary6IjzaAuvgHADp73M", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}, "form": {"description": "官網資訊爬取", "tag": "網頁爬蟲"}}
[2025-07-08 17:43:40,232] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-08 17:43:40,232] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-08 17:43:40,237] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-08 17:43:40,247] INFO in script_routes: 腳本上傳調試信息:
[2025-07-08 17:43:40,247] INFO in script_routes:   文件名: output_www.py
[2025-07-08 17:43:40,247] INFO in script_routes:   描述: 官網資訊爬取
[2025-07-08 17:43:40,248] INFO in script_routes:   標籤: 未分類
[2025-07-08 17:43:40,248] INFO in script_routes:   表單數據: {'description': '官網資訊爬取', 'tag': '網頁爬蟲'}
[2025-07-08 17:43:40,264] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-08 17:43:40,269] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 37.67ms
[2025-07-08 17:43:40,270] INFO in api_logger: [1751967820230] 請求完成: POST /script/ - 耗時: 38.12ms
[2025-07-08 17:43:50,106] INFO in api_logger: [1751967830105] 請求開始: GET /script/
[2025-07-08 17:43:50,107] DEBUG in api_logger: [1751967830105] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Dnt": "1", "Sec-Gpc": "1"}, "args": {}}
[2025-07-08 17:43:50,113] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-08 17:43:50,114] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-08 17:43:50,115] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-08 17:43:50,121] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-08 17:43:50,130] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.03ms
[2025-07-08 17:43:50,131] INFO in api_logger: [1751967830105] 請求完成: GET /script/ - 耗時: 22.37ms
[2025-07-10 10:14:32,958] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-10 10:14:32,964] INFO in api_logger: API日誌記錄器已設置
[2025-07-10 10:14:32,964] INFO in __init__: API日誌記錄器初始化完成
[2025-07-10 10:14:32,964] DEBUG in __init__: 初始化服務...
[2025-07-10 10:14:33,014] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-10 10:14:33,014] INFO in __init__: 服務已註冊: system_monitor
[2025-07-10 10:14:33,015] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-10 10:14:33,015] INFO in __init__: 服務已註冊: system_service
[2025-07-10 10:14:33,015] INFO in __init__: 系統監控服務初始化完成
[2025-07-10 10:14:33,016] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-10 10:14:33,017] INFO in __init__: 服務已註冊: script_executor
[2025-07-10 10:14:33,017] INFO in __init__: 服務已註冊: script_service
[2025-07-10 10:14:33,018] INFO in __init__: 腳本執行器初始化完成
[2025-07-10 10:14:33,019] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-10 10:14:33,019] INFO in __init__: 服務已註冊: schedule_service
[2025-07-10 10:14:33,019] INFO in __init__: 排程管理器初始化完成
[2025-07-10 10:14:33,026] INFO in schedule_executor: 排程執行器已啟動
[2025-07-10 10:14:33,027] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-10 10:14:33,027] INFO in __init__: 服務已註冊: execution_service
[2025-07-10 10:14:33,027] INFO in __init__: 排程執行器初始化完成
[2025-07-10 10:14:33,028] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-10 10:14:33,028] INFO in __init__: 服務初始化完成
[2025-07-10 10:14:33,317] DEBUG in __init__: 註冊藍圖...
[2025-07-10 10:14:33,320] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-10 10:14:33,324] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-10 10:14:33,332] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-10 10:14:33,334] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-10 10:14:33,335] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-10 10:14:33,336] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-10 10:14:33,353] INFO in __init__: API模組已初始化
[2025-07-10 10:14:33,354] INFO in __init__: API模組初始化完成
[2025-07-10 10:14:33,361] INFO in __init__: 資料庫表格初始化完成
[2025-07-10 10:14:35,113] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-10 10:14:35,116] INFO in api_logger: API日誌記錄器已設置
[2025-07-10 10:14:35,116] INFO in __init__: API日誌記錄器初始化完成
[2025-07-10 10:14:35,116] DEBUG in __init__: 初始化服務...
[2025-07-10 10:14:35,141] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-10 10:14:35,142] INFO in __init__: 服務已註冊: system_monitor
[2025-07-10 10:14:35,142] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-10 10:14:35,142] INFO in __init__: 服務已註冊: system_service
[2025-07-10 10:14:35,142] INFO in __init__: 系統監控服務初始化完成
[2025-07-10 10:14:35,144] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-10 10:14:35,145] INFO in __init__: 服務已註冊: script_executor
[2025-07-10 10:14:35,146] INFO in __init__: 服務已註冊: script_service
[2025-07-10 10:14:35,146] INFO in __init__: 腳本執行器初始化完成
[2025-07-10 10:14:35,146] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-10 10:14:35,146] INFO in __init__: 服務已註冊: schedule_service
[2025-07-10 10:14:35,147] INFO in __init__: 排程管理器初始化完成
[2025-07-10 10:14:35,151] INFO in schedule_executor: 排程執行器已啟動
[2025-07-10 10:14:35,151] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-10 10:14:35,152] INFO in __init__: 服務已註冊: execution_service
[2025-07-10 10:14:35,152] INFO in __init__: 排程執行器初始化完成
[2025-07-10 10:14:35,152] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-10 10:14:35,152] INFO in __init__: 服務初始化完成
[2025-07-10 10:14:35,329] DEBUG in __init__: 註冊藍圖...
[2025-07-10 10:14:35,332] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-10 10:14:35,335] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-10 10:14:35,344] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-10 10:14:35,346] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-10 10:14:35,347] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-10 10:14:35,347] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-10 10:14:35,362] INFO in __init__: API模組已初始化
[2025-07-10 10:14:35,362] INFO in __init__: API模組初始化完成
[2025-07-10 10:14:35,369] INFO in __init__: 資料庫表格初始化完成
[2025-07-10 10:15:22,268] INFO in routes: 訪問首頁
[2025-07-10 10:15:22,269] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-10 10:15:22,270] DEBUG in routes: 模板是否存在: True
[2025-07-10 10:15:22,270] DEBUG in routes: 開始渲染模板
[2025-07-10 10:15:22,296] DEBUG in routes: 模板渲染完成
[2025-07-10 10:15:23,030] INFO in api_logger: [1752113723020] 請求開始: GET /script/
[2025-07-10 10:15:23,033] DEBUG in api_logger: [1752113723020] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:15:23,048] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:15:23,049] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:15:23,050] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:15:23,060] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:15:23,071] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 34.75ms
[2025-07-10 10:15:23,072] INFO in api_logger: [1752113723020] 請求完成: GET /script/ - 耗時: 35.19ms
[2025-07-10 10:15:23,382] INFO in api_logger: [1752113723382] 請求開始: GET /script/
[2025-07-10 10:15:23,383] DEBUG in api_logger: [1752113723382] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Windows\"", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:15:23,388] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:15:23,388] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:15:23,389] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:15:23,396] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:15:23,407] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 23.77ms
[2025-07-10 10:15:23,408] INFO in api_logger: [1752113723382] 請求完成: GET /script/ - 耗時: 24.82ms
[2025-07-10 10:16:00,680] INFO in routes: 訪問首頁
[2025-07-10 10:16:00,680] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-10 10:16:00,681] DEBUG in routes: 模板是否存在: True
[2025-07-10 10:16:00,682] DEBUG in routes: 開始渲染模板
[2025-07-10 10:16:00,682] DEBUG in routes: 模板渲染完成
[2025-07-10 10:16:01,391] INFO in api_logger: [1752113761390] 請求開始: GET /script/
[2025-07-10 10:16:01,399] DEBUG in api_logger: [1752113761390] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:01,415] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:01,416] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:01,417] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:01,424] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:16:01,448] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 42.82ms
[2025-07-10 10:16:01,449] INFO in api_logger: [1752113761390] 請求完成: GET /script/ - 耗時: 43.31ms
[2025-07-10 10:16:01,738] INFO in api_logger: [1752113761737] 請求開始: GET /script/
[2025-07-10 10:16:01,739] DEBUG in api_logger: [1752113761737] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:01,750] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:01,751] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:01,754] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:01,765] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:16:01,777] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 34.83ms
[2025-07-10 10:16:01,777] INFO in api_logger: [1752113761737] 請求完成: GET /script/ - 耗時: 35.24ms
[2025-07-10 10:16:07,604] INFO in api_logger: [1752113767604] 請求開始: GET /script/
[2025-07-10 10:16:07,605] DEBUG in api_logger: [1752113767604] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:07,610] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:07,611] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:07,611] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:07,618] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:16:07,629] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.94ms
[2025-07-10 10:16:07,629] INFO in api_logger: [1752113767604] 請求完成: GET /script/ - 耗時: 23.29ms
[2025-07-10 10:16:14,160] INFO in api_logger: [1752113774160] 請求開始: GET /script/
[2025-07-10 10:16:14,161] DEBUG in api_logger: [1752113774160] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:14,167] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:14,168] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:14,168] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:14,172] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:16:14,185] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.57ms
[2025-07-10 10:16:14,185] INFO in api_logger: [1752113774160] 請求完成: GET /script/ - 耗時: 22.98ms
[2025-07-10 10:16:19,965] INFO in api_logger: [1752113779965] 請求開始: GET /script/
[2025-07-10 10:16:19,966] DEBUG in api_logger: [1752113779965] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:19,972] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:16:19,972] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:16:19,973] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:16:19,978] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:16:19,987] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 19.68ms
[2025-07-10 10:16:19,988] INFO in api_logger: [1752113779965] 請求完成: GET /script/ - 耗時: 20.12ms
[2025-07-10 10:16:45,099] INFO in api_logger: [1752113805098] 請求開始: POST /script/validate
[2025-07-10 10:16:45,100] DEBUG in api_logger: [1752113805098] 請求數據: {"method": "POST", "path": "/script/validate", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7073", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryeEfHuuXMVcRwcijb", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:16:45,101] DEBUG in script_routes: 處理腳本驗證請求
[2025-07-10 10:16:45,111] DEBUG in script_executor: 驗證腳本: C:\TEMP\tmpsn3ds_kz.py
[2025-07-10 10:16:45,337] INFO in script_routes: 腳本驗證完成: output_www.py, 有效: True
[2025-07-10 10:16:45,340] INFO in api_logger: [1752113805098] 請求完成: POST /script/validate - 耗時: 239.70ms
[2025-07-10 10:17:50,920] INFO in api_logger: [1752113870919] 請求開始: POST /script/
[2025-07-10 10:17:50,920] DEBUG in api_logger: [1752113870919] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7285", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryQuVI8WKUTJVfOY4a", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}, "form": {"description": "測試腳本", "tag": "網頁爬蟲"}}
[2025-07-10 10:17:50,921] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-10 10:17:50,922] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-10 10:17:50,928] DEBUG in script_routes: 保存文件到: C:\Python\Projects\Project04\app\data\uploads\output_www.py
[2025-07-10 10:17:50,938] INFO in script_routes: 腳本上傳調試信息:
[2025-07-10 10:17:50,938] INFO in script_routes:   文件名: output_www.py
[2025-07-10 10:17:50,939] INFO in script_routes:   描述: 測試腳本
[2025-07-10 10:17:50,939] INFO in script_routes:   標籤: 未分類
[2025-07-10 10:17:50,940] INFO in script_routes:   表單數據: {'description': '測試腳本', 'tag': '網頁爬蟲'}
[2025-07-10 10:17:50,970] INFO in script_routes: 腳本上傳成功: output_www.py
[2025-07-10 10:17:50,979] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 57.52ms
[2025-07-10 10:17:50,979] INFO in api_logger: [1752113870919] 請求完成: POST /script/ - 耗時: 58.05ms
[2025-07-10 10:17:52,484] INFO in schedule_executor: 排程執行器已停止
[2025-07-10 10:17:54,651] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-10 10:17:54,654] INFO in api_logger: API日誌記錄器已設置
[2025-07-10 10:17:54,654] INFO in __init__: API日誌記錄器初始化完成
[2025-07-10 10:17:54,654] DEBUG in __init__: 初始化服務...
[2025-07-10 10:17:54,682] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-10 10:17:54,683] INFO in __init__: 服務已註冊: system_monitor
[2025-07-10 10:17:54,683] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-10 10:17:54,683] INFO in __init__: 服務已註冊: system_service
[2025-07-10 10:17:54,683] INFO in __init__: 系統監控服務初始化完成
[2025-07-10 10:17:54,684] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-10 10:17:54,685] INFO in __init__: 服務已註冊: script_executor
[2025-07-10 10:17:54,685] INFO in __init__: 服務已註冊: script_service
[2025-07-10 10:17:54,685] INFO in __init__: 腳本執行器初始化完成
[2025-07-10 10:17:54,685] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-10 10:17:54,685] INFO in __init__: 服務已註冊: schedule_service
[2025-07-10 10:17:54,686] INFO in __init__: 排程管理器初始化完成
[2025-07-10 10:17:54,690] INFO in schedule_executor: 排程執行器已啟動
[2025-07-10 10:17:54,691] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-10 10:17:54,691] INFO in __init__: 服務已註冊: execution_service
[2025-07-10 10:17:54,691] INFO in __init__: 排程執行器初始化完成
[2025-07-10 10:17:54,692] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-10 10:17:54,692] INFO in __init__: 服務初始化完成
[2025-07-10 10:17:54,874] DEBUG in __init__: 註冊藍圖...
[2025-07-10 10:17:54,877] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-10 10:17:54,880] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-10 10:17:54,889] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-10 10:17:54,892] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-10 10:17:54,894] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-10 10:17:54,894] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-10 10:17:54,912] INFO in __init__: API模組已初始化
[2025-07-10 10:17:54,913] INFO in __init__: API模組初始化完成
[2025-07-10 10:17:54,918] INFO in __init__: 資料庫表格初始化完成
[2025-07-10 10:17:58,796] INFO in api_logger: [1752113878794] 請求開始: POST /script/
[2025-07-10 10:17:58,798] DEBUG in api_logger: [1752113878794] 請求數據: {"method": "POST", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Content-Length": "7285", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryyp2FVxXtt45i43lv", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Origin": "http://localhost:5000", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}, "form": {"description": "測試腳本", "tag": "網頁爬蟲"}}
[2025-07-10 10:17:58,801] DEBUG in script_routes: 處理腳本上傳請求
[2025-07-10 10:17:58,802] DEBUG in script_routes: 準備上傳腳本: output_www.py
[2025-07-10 10:17:58,818] WARNING in script_routes: 腳本已存在: output_www.py
[2025-07-10 10:17:58,818] INFO in api_logger: 功能 [腳本上傳] 正常運行 - 耗時: 17.74ms
[2025-07-10 10:17:58,819] INFO in api_logger: [1752113878794] 請求完成: POST /script/ - 耗時: 18.63ms
[2025-07-10 10:18:44,583] INFO in api_logger: [1752113924579] 請求開始: GET /script/
[2025-07-10 10:18:44,584] DEBUG in api_logger: [1752113924579] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:18:44,592] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:18:44,593] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:18:44,594] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:18:44,599] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:18:44,609] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.90ms
[2025-07-10 10:18:44,609] INFO in api_logger: [1752113924579] 請求完成: GET /script/ - 耗時: 23.19ms
[2025-07-10 10:20:08,914] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-10 10:20:08,917] INFO in api_logger: API日誌記錄器已設置
[2025-07-10 10:20:08,917] INFO in __init__: API日誌記錄器初始化完成
[2025-07-10 10:20:08,917] DEBUG in __init__: 初始化服務...
[2025-07-10 10:20:08,940] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-10 10:20:08,940] INFO in __init__: 服務已註冊: system_monitor
[2025-07-10 10:20:08,940] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-10 10:20:08,941] INFO in __init__: 服務已註冊: system_service
[2025-07-10 10:20:08,941] INFO in __init__: 系統監控服務初始化完成
[2025-07-10 10:20:08,942] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-10 10:20:08,942] INFO in __init__: 服務已註冊: script_executor
[2025-07-10 10:20:08,943] INFO in __init__: 服務已註冊: script_service
[2025-07-10 10:20:08,943] INFO in __init__: 腳本執行器初始化完成
[2025-07-10 10:20:08,943] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-10 10:20:08,943] INFO in __init__: 服務已註冊: schedule_service
[2025-07-10 10:20:08,943] INFO in __init__: 排程管理器初始化完成
[2025-07-10 10:20:08,947] INFO in schedule_executor: 排程執行器已啟動
[2025-07-10 10:20:08,948] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-10 10:20:08,948] INFO in __init__: 服務已註冊: execution_service
[2025-07-10 10:20:08,948] INFO in __init__: 排程執行器初始化完成
[2025-07-10 10:20:08,949] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-10 10:20:08,949] INFO in __init__: 服務初始化完成
[2025-07-10 10:20:09,122] DEBUG in __init__: 註冊藍圖...
[2025-07-10 10:20:09,125] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-10 10:20:09,129] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-10 10:20:09,135] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-10 10:20:09,138] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-10 10:20:09,139] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-10 10:20:09,140] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-10 10:20:09,155] INFO in __init__: API模組已初始化
[2025-07-10 10:20:09,155] INFO in __init__: API模組初始化完成
[2025-07-10 10:20:09,160] INFO in __init__: 資料庫表格初始化完成
[2025-07-10 10:20:10,781] INFO in __init__: 應用程式日誌系統初始化完成
[2025-07-10 10:20:10,786] INFO in api_logger: API日誌記錄器已設置
[2025-07-10 10:20:10,786] INFO in __init__: API日誌記錄器初始化完成
[2025-07-10 10:20:10,786] DEBUG in __init__: 初始化服務...
[2025-07-10 10:20:10,815] DEBUG in system_monitor: 系統監控服務初始化完成
[2025-07-10 10:20:10,816] INFO in __init__: 服務已註冊: system_monitor
[2025-07-10 10:20:10,816] INFO in __init__: 服務已註冊: SystemMonitor
[2025-07-10 10:20:10,816] INFO in __init__: 服務已註冊: system_service
[2025-07-10 10:20:10,816] INFO in __init__: 系統監控服務初始化完成
[2025-07-10 10:20:10,819] DEBUG in script_executor: 腳本執行器初始化完成：
腳本目錄：C:\Python\Projects\Project04\app\data\uploads
輸出目錄：C:\Python\Projects\Project04\app\data\outputs
超時時間：60秒
[2025-07-10 10:20:10,819] INFO in __init__: 服務已註冊: script_executor
[2025-07-10 10:20:10,820] INFO in __init__: 服務已註冊: script_service
[2025-07-10 10:20:10,820] INFO in __init__: 腳本執行器初始化完成
[2025-07-10 10:20:10,820] INFO in __init__: 服務已註冊: schedule_manager
[2025-07-10 10:20:10,821] INFO in __init__: 服務已註冊: schedule_service
[2025-07-10 10:20:10,821] INFO in __init__: 排程管理器初始化完成
[2025-07-10 10:20:10,826] INFO in schedule_executor: 排程執行器已啟動
[2025-07-10 10:20:10,827] INFO in __init__: 服務已註冊: schedule_executor
[2025-07-10 10:20:10,827] INFO in __init__: 服務已註冊: execution_service
[2025-07-10 10:20:10,827] INFO in __init__: 排程執行器初始化完成
[2025-07-10 10:20:10,828] INFO in __init__: 所有服務初始化完成，已註冊 9 個服務
[2025-07-10 10:20:10,828] INFO in __init__: 服務初始化完成
[2025-07-10 10:20:11,036] DEBUG in __init__: 註冊藍圖...
[2025-07-10 10:20:11,040] DEBUG in __init__: 核心藍圖註冊完成
[2025-07-10 10:20:11,044] DEBUG in __init__: 主頁藍圖註冊完成
[2025-07-10 10:20:11,051] DEBUG in __init__: 排程藍圖註冊完成
[2025-07-10 10:20:11,054] DEBUG in __init__: 腳本藍圖註冊完成
[2025-07-10 10:20:11,056] DEBUG in __init__: 系統藍圖註冊完成
[2025-07-10 10:20:11,056] DEBUG in __init__: 所有藍圖註冊完成
[2025-07-10 10:20:11,077] INFO in __init__: API模組已初始化
[2025-07-10 10:20:11,078] INFO in __init__: API模組初始化完成
[2025-07-10 10:20:11,084] INFO in __init__: 資料庫表格初始化完成
[2025-07-10 10:20:34,122] INFO in routes: 訪問首頁
[2025-07-10 10:20:34,123] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-10 10:20:34,124] DEBUG in routes: 模板是否存在: True
[2025-07-10 10:20:34,124] DEBUG in routes: 開始渲染模板
[2025-07-10 10:20:34,134] DEBUG in routes: 模板渲染完成
[2025-07-10 10:20:35,124] INFO in api_logger: [1752114035120] 請求開始: GET /script/
[2025-07-10 10:20:35,125] DEBUG in api_logger: [1752114035120] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:20:35,139] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:20:35,141] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:20:35,142] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:20:35,154] INFO in script_routes: 成功獲取腳本列表，共 6 個腳本
[2025-07-10 10:20:35,168] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 40.43ms
[2025-07-10 10:20:35,168] INFO in api_logger: [1752114035120] 請求完成: GET /script/ - 耗時: 40.78ms
[2025-07-10 10:20:41,637] INFO in api_logger: [1752114041636] 請求開始: GET /script/
[2025-07-10 10:20:41,638] DEBUG in api_logger: [1752114041636] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:20:41,645] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:20:41,645] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:20:41,646] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:20:41,650] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:20:41,662] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 22.51ms
[2025-07-10 10:20:41,662] INFO in api_logger: [1752114041636] 請求完成: GET /script/ - 耗時: 23.07ms
[2025-07-10 10:21:06,338] INFO in routes: 訪問首頁
[2025-07-10 10:21:06,339] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-10 10:21:06,341] DEBUG in routes: 模板是否存在: True
[2025-07-10 10:21:06,342] DEBUG in routes: 開始渲染模板
[2025-07-10 10:21:06,343] DEBUG in routes: 模板渲染完成
[2025-07-10 10:21:07,303] INFO in api_logger: [1752114067303] 請求開始: GET /script/
[2025-07-10 10:21:07,303] DEBUG in api_logger: [1752114067303] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:21:07,308] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:21:07,308] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:21:07,309] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:21:07,313] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:21:07,322] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 17.97ms
[2025-07-10 10:21:07,322] INFO in api_logger: [1752114067303] 請求完成: GET /script/ - 耗時: 18.32ms
[2025-07-10 10:22:35,228] INFO in routes: 訪問首頁
[2025-07-10 10:22:35,228] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-10 10:22:35,229] DEBUG in routes: 模板是否存在: True
[2025-07-10 10:22:35,229] DEBUG in routes: 開始渲染模板
[2025-07-10 10:22:35,230] DEBUG in routes: 模板渲染完成
[2025-07-10 10:22:36,215] INFO in api_logger: [1752114156215] 請求開始: GET /script/
[2025-07-10 10:22:36,216] DEBUG in api_logger: [1752114156215] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:22:36,224] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:22:36,225] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:22:36,225] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:22:36,230] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:22:36,246] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 29.08ms
[2025-07-10 10:22:36,246] INFO in api_logger: [1752114156215] 請求完成: GET /script/ - 耗時: 29.54ms
[2025-07-10 10:23:05,441] INFO in routes: 訪問首頁
[2025-07-10 10:23:05,442] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-10 10:23:05,443] DEBUG in routes: 模板是否存在: True
[2025-07-10 10:23:05,444] DEBUG in routes: 開始渲染模板
[2025-07-10 10:23:05,445] DEBUG in routes: 模板渲染完成
[2025-07-10 10:23:06,430] INFO in api_logger: [1752114186430] 請求開始: GET /script/
[2025-07-10 10:23:06,431] DEBUG in api_logger: [1752114186430] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "localhost:5000", "Connection": "keep-alive", "Pragma": "no-cache", "Cache-Control": "no-cache", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 10:23:06,435] WARNING in script_routes: 資料庫中的腳本 test_tag_script.py 文件不存在於磁碟: test_path.py
[2025-07-10 10:23:06,435] WARNING in script_routes: 資料庫中的腳本 direct_tag_test.py 文件不存在於磁碟: /fake/path/direct_tag_test.py
[2025-07-10 10:23:06,436] WARNING in script_routes: 資料庫中的腳本 tag_test_script.py 文件不存在於磁碟: /fake/path/tag_test_script.py
[2025-07-10 10:23:06,440] INFO in script_routes: 成功獲取腳本列表，共 5 個腳本
[2025-07-10 10:23:06,447] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 15.53ms
[2025-07-10 10:23:06,447] INFO in api_logger: [1752114186430] 請求完成: GET /script/ - 耗時: 15.79ms
