[2025-07-10 14:07:47,231] ERROR in script_executor: Traceback (most recent call last):
  File "C:\Python\Projects\Project04\app\services\script_executor.py", line 408, in execute_script
    raise ValueError(f"腳本不存在: {script_id}")
ValueError: 腳本不存在: output_news.py

[2025-07-10 14:07:47,230] INFO in script_executor: 獲取腳本 ID: output_news.py
[2025-07-10 14:07:47,249] ERROR in schedule_executor: 執行排程 4 時發生錯誤: too many values to unpack (expected 3)
[2025-07-10 14:07:47,251] WARNING in script_executor: 找不到腳本 ID: output_news.py
[2025-07-10 14:07:47,252] ERROR in script_executor: 執行腳本失敗: output_news.py, 錯誤: 腳本不存在: output_news.py
[2025-07-10 14:07:47,269] ERROR in script_executor: Traceback (most recent call last):
  File "C:\Python\Projects\Project04\app\services\script_executor.py", line 408, in execute_script
    raise ValueError(f"腳本不存在: {script_id}")
ValueError: 腳本不存在: output_news.py

[2025-07-10 14:07:47,270] ERROR in schedule_executor: 執行排程 4 時發生錯誤: too many values to unpack (expected 3)
[2025-07-10 14:07:48,036] INFO in routes: 訪問首頁
[2025-07-10 14:07:48,038] DEBUG in routes: 模板路徑: C:\Python\Projects\Project04\app\templates\index.html
[2025-07-10 14:07:48,039] DEBUG in routes: 模板是否存在: True
[2025-07-10 14:07:48,039] DEBUG in routes: 開始渲染模板
[2025-07-10 14:07:48,041] DEBUG in routes: 模板渲染完成
[2025-07-10 14:07:48,478] INFO in api_logger: [1752127668478] 請求開始: GET /script/
[2025-07-10 14:07:48,581] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 14:07:48,581] DEBUG in api_logger: [1752127668478] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "127.0.0.1:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://127.0.0.1:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 14:07:48,771] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-10 14:07:48,786] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 36.81ms
[2025-07-10 14:07:48,787] INFO in api_logger: [1752127668478] 請求完成: GET /script/ - 耗時: 37.59ms
[2025-07-10 14:07:48,794] INFO in api_logger: [1752127668793] 請求開始: GET /script/
[2025-07-10 14:07:48,806] DEBUG in api_logger: [1752127668793] 請求數據: {"method": "GET", "path": "/script/", "remote_addr": "127.0.0.1", "headers": {"Host": "127.0.0.1:5000", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Sec-Ch-Ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "Sec-Ch-Ua-Mobile": "?1", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://127.0.0.1:5000/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "args": {}}
[2025-07-10 14:07:48,832] INFO in script_routes: 成功獲取腳本列表，共 1 個腳本
[2025-07-10 14:07:48,838] INFO in api_logger: 功能 [腳本列表頁面] 正常運行 - 耗時: 29.97ms
[2025-07-10 14:07:48,838] INFO in api_logger: [1752127668793] 請求完成: GET /script/ - 耗時: 30.47ms
[2025-07-10 14:07:48,863] WARNING in system_monitor: 獲取排程數量失敗: 'Schedule' object has no attribute 'get'
[2025-07-10 14:07:48,869] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 14:07:48,895] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 14:07:49,017] WARNING in system_monitor: 獲取排程數量失敗: 'Schedule' object has no attribute 'get'
[2025-07-10 14:07:49,022] DEBUG in system_routes: 使用系統監控服務獲取資源信息
[2025-07-10 14:07:49,027] INFO in system_routes: 獲取系統資源使用情況
[2025-07-10 14:07:49,147] WARNING in system_monitor: 獲取排程數量失敗: 'Schedule' object has no attribute 'get'
[2025-07-10 14:07:49,150] DEBUG in system_routes: 使用系統監控服務獲取資源信息
