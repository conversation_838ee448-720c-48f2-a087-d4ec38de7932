#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試路由是否正確註冊
"""

import requests
import json

def test_api_routes():
    """測試API路由"""
    base_url = "http://127.0.0.1:5000"
    
    # 測試路由列表
    test_routes = [
        "/api/v1/schedules",
        "/api/v1/system/resources",
        "/api/v1/scripts"
    ]
    
    print("🔍 測試API路由...")
    
    for route in test_routes:
        url = f"{base_url}{route}"
        try:
            print(f"\n📍 測試: {url}")
            response = requests.get(url, timeout=5)
            print(f"   狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   響應: {json.dumps(data, ensure_ascii=False, indent=2)[:200]}...")
                except:
                    print(f"   響應: {response.text[:200]}...")
            else:
                print(f"   錯誤: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"   連接錯誤: {e}")
    
    # 測試刪除排程API
    print(f"\n🗑️ 測試刪除排程API...")
    delete_url = f"{base_url}/api/v1/schedules/1"
    try:
        print(f"📍 測試: DELETE {delete_url}")
        response = requests.delete(delete_url, timeout=5)
        print(f"   狀態碼: {response.status_code}")
        print(f"   響應: {response.text[:200]}...")
    except requests.exceptions.RequestException as e:
        print(f"   連接錯誤: {e}")

if __name__ == "__main__":
    test_api_routes()
