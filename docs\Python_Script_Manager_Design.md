# Python 腳本管理系統設計文檔

## 1. 系統概述

### 1.1 功能簡介
Python腳本管理系統提供一個集中化的平台，用於管理和執行Python腳本文件。系統支持腳本的上傳、查看、刪除等基本操作，並提供友好的Web介面。

### 1.2 技術架構
- 後端：Flask（Python Web框架）
- 前端：HTML5 + CSS3 + JavaScript
- 資料存儲：檔案系統 + JSON配置
- UI框架：Bootstrap 5

### 1.3 目錄結構
```
Project04/
├── app/
│   ├── core/            # 核心功能模組
│   ├── scripts/         # 腳本管理相關邏輯
│   ├── static/          # 靜態資源
│   │   ├── css/        # 樣式文件
│   │   └── js/         # JavaScript文件
│   └── templates/       # 模板文件
├── data/
│   ├── scripts/        # 腳本存儲目錄
│   └── outputs/        # 腳本輸出目錄
└── docs/               # 文檔目錄
```

## 2. 腳本管理功能

### 2.1 基本功能
1. 腳本上傳
   - 支持.py文件上傳
   - 檔案大小限制：16MB
   - 支持拖放上傳
   - 文件名衝突處理

2. 腳本刪除
   - 刪除前確認
   - 級聯刪除相關配置
   - 清理相關輸出文件

3. 腳本查看
   - 顯示腳本內容
   - 顯示基本信息（大小、修改時間等）
   - 支持代碼格式化顯示

### 2.2 腳本元數據管理
```json
{
    "script_name.py": {
        "description": "腳本描述",
        "created_at": "創建時間",
        "modified_at": "修改時間",
        "size": "檔案大小",
        "author": "作者",
        "tags": ["標籤1", "標籤2"]
    }
}
```

### 2.3 腳本描述功能
- 支持添加和編輯腳本描述
- 描述顯示在腳本列表和詳情頁
- 用於腳本搜索和分類
- 支持HTML格式的描述內容

## 3. 用戶界面設計

### 3.1 界面布局
1. 頂部操作欄
   - 新增腳本按鈕
   - 搜索框
   - 視圖切換

2. 主要內容區
   - 腳本列表/網格視圖
   - 拖放上傳區域
   - 狀態提示

3. 模態框
   - 上傳對話框
   - 腳本詳情
   - 確認對話框

### 3.2 拖放上傳功能
1. 拖放區域設計
   ```html
   <div class="upload-zone">
       <div class="upload-prompt">
           <i class="fas fa-cloud-upload-alt"></i>
           <p>拖放文件至此處或點擊上傳</p>
       </div>
   </div>
   ```

2. 視覺反饋
   - 拖入時邊框高亮
   - 上傳進度條
   - 成功/失敗提示

### 3.3 交互流程
1. 腳本上傳
   ```mermaid
   sequenceDiagram
      User->>UI: 拖放/選擇文件
      UI->>Backend: 發送文件
      Backend->>FileSystem: 保存文件
      Backend->>MetaData: 更新元數據
      Backend->>UI: 返回結果
      UI->>User: 顯示結果
   ```

2. 腳本管理
   - 點擊查看：顯示詳情
   - 點擊刪除：確認後執行
   - 點擊編輯：開啟編輯模式

## 4. API設計

### 4.1 端點列表
| 方法   | 路徑                    | 說明             |
|--------|------------------------|-----------------|
| GET    | /api/scripts           | 獲取腳本列表      |
| POST   | /api/scripts/upload    | 上傳腳本         |
| DELETE | /api/scripts/{name}    | 刪除腳本         |
| GET    | /api/scripts/{name}/info| 獲取腳本信息     |
| PUT    | /api/scripts/{name}/desc| 更新腳本描述     |

### 4.2 請求/響應格式
1. 獲取腳本列表
   ```json
   {
       "status": "success",
       "data": [
           {
               "name": "script.py",
               "description": "描述",
               "size": 1024,
               "modified": "2025-03-18 15:30:00"
           }
       ]
   }
   ```

2. 上傳腳本
   ```json
   {
       "status": "success",
       "data": {
           "name": "uploaded_script.py",
           "path": "/path/to/script",
           "size": 1024
       }
   }
   ```

## 5. 數據存儲

### 5.1 文件存儲結構
```
data/
├── scripts/           # 腳本文件
│   ├── script1.py
│   └── script2.py
├── meta/             # 元數據
│   └── scripts.json
└── outputs/          # 輸出文件
    ├── script1/
    └── script2/
```

### 5.2 元數據存儲
- 使用JSON文件存儲
- 定期備份
- 支持並發訪問
- 自動修復損壞

## 6. 安全考慮

### 6.1 文件安全
1. 類型限制
   - 僅允許.py文件
   - 文件內容檢查
   - 大小限制

2. 路徑安全
   - 防止路徑遍歷
   - 文件名淨化
   - 權限控制

### 6.2 錯誤處理
1. 上傳錯誤
   - 文件太大
   - 類型不支持
   - 存儲失敗

2. 刪除錯誤
   - 文件不存在
   - 權限不足
   - 刪除失敗

### 6.3 日誌記錄
- 操作日誌
- 錯誤日誌
- 訪問日誌

## 7. 未來擴展

### 7.1 計劃功能
1. 腳本功能
   - 腳本版本控制
   - 腳本依賴管理
   - 腳本測試功能

2. 界面功能
   - 批量操作
   - 高級搜索
   - 自定義視圖

3. 系統功能
   - 用戶權限管理
   - 腳本執行環境隔離
   - API權限控制

### 7.2 技術改進
- 引入數據庫
- 添加緩存層
- 實現分佈式存儲
- 優化性能
