# 系統架構遷移指南

本文檔提供從舊架構到新架構的遷移步驟指南。

## 遷移準備

1. **備份**
   - 在進行任何遷移操作前，先完整備份整個專案
   - 備份數據庫

2. **環境檢查**
   - 確認 Python 版本（建議 Python 3.8+）
   - 確認所有依賴套件已安裝

## 遷移步驟

### 1. 創建新目錄結構

創建新的目錄結構，以符合新架構設計：

```bash
mkdir -p app/api/v1
mkdir -p app/api/middleware
mkdir -p app/data/uploads
mkdir -p app/data/outputs
mkdir -p app/services/executors
mkdir -p app/services/managers
mkdir -p config
mkdir -p tests/unit/models
mkdir -p tests/unit/services
mkdir -p tests/integration/api
mkdir -p tests/integration/services
mkdir -p tests/e2e
mkdir -p docs/architecture
mkdir -p docs/api
mkdir -p docs/user
mkdir -p scripts
```

### 2. 遷移數據文件

使用數據遷移腳本將上傳和輸出文件轉移到新位置：

```bash
python scripts/migrate_data.py
```

### 3. 遷移配置文件

使用配置遷移腳本將舊配置轉換為新配置結構：

```bash
python scripts/migrate_config.py
```

### 4. 更新 API 路徑

更新所有前端使用的 API 路徑：

```bash
python scripts/update_api_paths.py
```

### 5. 初始化新架構

運行初始化腳本，設置新架構：

```bash
python scripts/init_architecture.py
```

## 測試遷移結果

### 1. 啟動應用

```bash
flask run
```

### 2. 測試關鍵功能

- 測試腳本上傳/下載
- 測試排程創建/修改/刪除
- 測試腳本執行

### 3. 驗證 API 請求

確保所有 API 請求都使用新的路徑格式：

- `/api/v1/scripts`
- `/api/v1/schedules`
- `/api/v1/system/resources`

## 故障排除

### API 路徑不一致

如果前端與後端 API 路徑不一致：

1. 啟用 API 版本控制中間件，確保舊路徑請求正確重定向
2. 使用開發者工具檢查網絡請求，確認請求 URL 格式

### 文件權限問題

如果遇到新目錄權限問題：

1. 檢查目錄權限
2. 確保應用有讀寫權限 

### 服務初始化失敗

如果服務初始化失敗：

1. 檢查服務依賴關係
2. 檢查服務註冊順序

## 回滾計劃

如需回滾到舊架構：

1. 停止應用
2. 還原之前的備份
3. 重新啟動應用

## 遷移後檢查清單

- [ ] 所有 API 端點可正常訪問
- [ ] 文件上傳功能正常
- [ ] 排程功能正常
- [ ] 系統資源監控正常
- [ ] 所有前端頁面顯示正常
- [ ] 日誌正確記錄到新位置 