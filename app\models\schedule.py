from datetime import datetime, timedelta
from typing import List, Optional
from enum import Enum
import uuid
from .. import db

class ScheduleType(Enum):
    """排程類型"""
    IMMEDIATE = 'immediate'  # 立即執行
    ONCE = 'once'          # 一次性
    MINUTE = 'minute'      # 每分鐘
    DAILY = 'daily'        # 每日
    WEEKLY = 'weekly'      # 每週
    MONTHLY = 'monthly'    # 每月

class ScheduleStatus(Enum):
    """排程狀態"""
    WAITING = 'waiting'    # 等待中
    RUNNING = 'running'    # 執行中
    COMPLETED = 'completed'  # 已完成
    FAILED = 'failed'      # 執行失敗
    STOPPED = 'stopped'    # 已停止
    PAUSED = 'paused'      # 已暫停
    PENDING = 'pending'    # 待執行

class Schedule(db.Model):
    """排程模型"""
    __tablename__ = 'schedules'
    
    id = db.Column(db.Integer, primary_key=True)
    script_name = db.Column(db.String(255), nullable=False)
    schedule_type = db.Column(db.String(20), nullable=False)
    execution_time = db.Column(db.DateTime, nullable=True)
    interval_minutes = db.Column(db.Integer, nullable=True)
    weekdays = db.Column(db.String(50), nullable=True)  # 儲存為 JSON 字串
    days_of_month = db.Column(db.String(50), nullable=True)  # 儲存為 JSON 字串
    description = db.Column(db.String(500), nullable=True)
    status = db.Column(db.String(20), nullable=False, default=ScheduleStatus.WAITING.value)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    last_run = db.Column(db.DateTime, nullable=True)
    next_run = db.Column(db.DateTime, nullable=True)
    error_message = db.Column(db.Text, nullable=True)
    
    # 關聯
    execution_logs = db.relationship('ExecutionLog', backref='schedule', lazy=True,
                                   cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        # 確保 schedule_type 和 status 是字串值
        if 'schedule_type' in kwargs:
            if isinstance(kwargs['schedule_type'], ScheduleType):
                kwargs['schedule_type'] = kwargs['schedule_type'].value
            elif isinstance(kwargs['schedule_type'], str):
                # 驗證字串值是否為有效的排程類型
                if kwargs['schedule_type'] not in [t.value for t in ScheduleType]:
                    raise ValueError(f"Invalid schedule_type: {kwargs['schedule_type']}")
        
        if 'status' in kwargs:
            if isinstance(kwargs['status'], ScheduleStatus):
                kwargs['status'] = kwargs['status'].value
            elif isinstance(kwargs['status'], str):
                # 驗證字串值是否為有效的狀態
                if kwargs['status'] not in [s.value for s in ScheduleStatus]:
                    raise ValueError(f"Invalid status: {kwargs['status']}")
        elif 'status' not in kwargs:
            kwargs['status'] = ScheduleStatus.WAITING.value
            
        super(Schedule, self).__init__(**kwargs)
    
    def to_dict(self) -> dict:
        """轉換為字典格式"""
        # 處理 weekdays 和 days_of_month
        weekdays = [int(d) for d in self.weekdays.split(',')] if self.weekdays else []
        days_of_month = [int(d) for d in self.days_of_month.split(',')] if self.days_of_month else []
        
        return {
            "id": self.id,
            "script_name": self.script_name,
            "schedule_type": self.schedule_type,
            "execution_time": self.execution_time.strftime("%Y-%m-%dT%H:%M:%S") if self.execution_time else None,
            "interval_minutes": self.interval_minutes,
            "weekdays": weekdays,
            "days_of_month": days_of_month,
            "description": self.description,
            "status": self.status,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime("%Y-%m-%dT%H:%M:%S"),
            "last_run": self.last_run.strftime("%Y-%m-%dT%H:%M:%S") if self.last_run else None,
            "next_run": self.next_run.strftime("%Y-%m-%dT%H:%M:%S") if self.next_run else None,
            "error_message": self.error_message
        }
    
    def _calculate_next_run(self) -> Optional[datetime]:
        """計算下次執行時間"""
        now = datetime.now()
        
        if not self.is_active:
            return None
            
        if self.schedule_type == ScheduleType.IMMEDIATE.value:
            return now
            
        if self.schedule_type == ScheduleType.ONCE.value:
            if self.execution_time and self.execution_time > now:
                return self.execution_time
            return None
            
        if self.schedule_type == ScheduleType.MINUTE.value:
            if self.interval_minutes and self.interval_minutes > 0:
                if self.last_run:
                    return self.last_run + timedelta(minutes=self.interval_minutes)
                return now
            return None
            
        if self.schedule_type == ScheduleType.DAILY.value:
            if not self.execution_time:
                return None
                
            next_run = datetime.combine(now.date(), self.execution_time.time())
            if next_run <= now:
                next_run += timedelta(days=1)
            return next_run
            
        if self.schedule_type == ScheduleType.WEEKLY.value:
            if not self.execution_time or not self.weekdays:
                return None
                
            # 將 JavaScript 星期格式轉換為 Python 格式
            weekdays = [int(d) for d in self.weekdays.split(',')]
            python_weekdays = [(d - 1) % 7 for d in weekdays]
            
            current_weekday = now.weekday()
            next_weekdays = [d for d in python_weekdays if d >= current_weekday]
            
            if next_weekdays:
                next_weekday = min(next_weekdays)
                days_ahead = next_weekday - current_weekday
            else:
                next_weekday = min(python_weekdays)
                days_ahead = 7 + next_weekday - current_weekday
            
            next_date = now.date() + timedelta(days=days_ahead)
            next_run = datetime.combine(next_date, self.execution_time.time())
            
            return next_run
            
        if self.schedule_type == ScheduleType.MONTHLY.value:
            if not self.execution_time or not self.days_of_month:
                return None
                
            days = [int(d) for d in self.days_of_month.split(',')]
            current_day = now.day
            
            # 找出本月剩餘的執行日期
            remaining_days = [d for d in days if d > current_day]
            
            if remaining_days:
                next_day = min(remaining_days)
                next_date = now.replace(day=next_day, hour=self.execution_time.hour, 
                                      minute=self.execution_time.minute, second=0)
            else:
                # 如果本月沒有剩餘日期，找下個月最早的日期
                next_day = min(days)
                if now.month == 12:
                    next_date = now.replace(year=now.year + 1, month=1, day=next_day,
                                          hour=self.execution_time.hour,
                                          minute=self.execution_time.minute, second=0)
                else:
                    next_date = now.replace(month=now.month + 1, day=next_day,
                                          hour=self.execution_time.hour,
                                          minute=self.execution_time.minute, second=0)
            
            return next_date
            
        return None

    @classmethod
    def from_dict(cls, data: dict) -> 'Schedule':
        """從字典格式創建實例"""
        # 解析時間字符串為datetime對象
        try:
            execution_time = datetime.strptime(data["execution_time"], "%Y-%m-%dT%H:%M:%S") if data.get("execution_time") else None
            created_at = datetime.strptime(data["created_at"], "%Y-%m-%dT%H:%M:%S")
            last_run = datetime.strptime(data["last_run"], "%Y-%m-%dT%H:%M:%S") if data.get("last_run") else None
            next_run = datetime.strptime(data["next_run"], "%Y-%m-%dT%H:%M:%S") if data.get("next_run") else None
        except ValueError:
            # 如果標準格式解析失敗，嘗試使用fromisoformat
            execution_time = datetime.fromisoformat(data["execution_time"]) if data.get("execution_time") else None
            created_at = datetime.fromisoformat(data["created_at"])
            last_run = datetime.fromisoformat(data["last_run"]) if data.get("last_run") else None
            next_run = datetime.fromisoformat(data["next_run"]) if data.get("next_run") else None
            
        schedule = cls(
            script_name=data["script_name"],
            schedule_type=ScheduleType(data["schedule_type"]),
            execution_time=execution_time,
            interval_minutes=data.get("interval_minutes"),
            weekdays=data.get("weekdays", ""),
            days_of_month=data.get("days_of_month", ""),
            description=data.get("description", ""),
            status=ScheduleStatus(data.get("status", ScheduleStatus.WAITING.value)),
            is_active=data.get("is_active", True),
            created_at=created_at,
            last_run=last_run,
            next_run=next_run,
            error_message=data.get("error_message")
        )
        schedule.id = data.get("id", str(uuid.uuid4()))
        return schedule 