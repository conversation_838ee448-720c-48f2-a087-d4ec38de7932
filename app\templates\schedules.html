{% extends "base.html" %}
{% from "components/schedule_list.html" import schedule_list %}

{% block title %}排程管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">排程管理</h2>
            {{ schedule_list(schedules) }}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 更新系統資源使用狀況
function updateSystemResources() {
    fetch('/schedule/api/api/v1/system/resources')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                const resources = data.data;
                document.getElementById('cpu-usage').style.width = `${resources.cpu}%`;
                document.getElementById('cpu-usage').textContent = `${resources.cpu.toFixed(1)}%`;
                
                document.getElementById('memory-usage').style.width = `${resources.memory}%`;
                document.getElementById('memory-usage').textContent = `${resources.memory.toFixed(1)}%`;
                
                document.getElementById('schedule-count').style.width = 
                    `${(resources.schedules.current / resources.schedules.max) * 100}%`;
                document.getElementById('schedule-count').textContent = 
                    `${resources.schedules.current}/${resources.schedules.max}`;
            }
        })
        .catch(error => console.error('Error updating system resources:', error));
}

// 定期更新系統資源
setInterval(updateSystemResources, 5000);
updateSystemResources(); // 初始化時立即更新一次
</script>
{% endblock %} 