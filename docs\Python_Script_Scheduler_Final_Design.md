收到！以下是根據我們完整討論後，為你整理的 **正式專案規劃說明文件 (明文檔最終版)**，讓你未來可以直接交給開發團隊或內部參考：

---

# **Python 腳本排程與執行管理系統 — 專案規劃說明文件**

## 一、專案目標

建立一套 **以排程總覽為核心，並結合腳本管理與執行任務狀態監控** 的簡潔直覺 Python 腳本排程系統，提供集中、易操作的 Web UI，達成：
- **排程與即時執行總覽集中一頁管理**。
- **腳本管理、排程設定皆透過彈窗完成**，避免頁面跳轉。
- **即時任務控制（等待、執行、暫停、繼續、完成、失敗）**。
- 保持「不複雜化」、「彈性」、「防呆」的設計核心。

---

## 二、系統設計核心概念

| 元素                        | 說明                                                         |
|---------------------------|------------------------------------------------------------|
| **排程總覽頁 (主頁)**            | 進入系統第一畫面，集中顯示所有排程及即時任務狀態。                                |
| **腳本管理（彈窗）**             | 系統內所有可執行腳本之新增、刪除、描述修改。                                      |
| **新增排程（彈窗）**             | 針對指定腳本新增排程，並依需求設定排程條件。                                      |
| **即時任務狀態監控**              | 在排程總覽內直接顯示任務執行中、暫停、失敗等狀態，並提供控制按鈕。                           |
| **不跳頁，彈窗管理**              | 所有設定、操作均於彈窗內完成，回到排程總覽即同步反映，避免切換視窗。                           |

---

## 三、排程總覽頁設計 (主畫面)

### 欄位呈現

| 腳本名稱    | 排程類型    | 排程條件        | 下一次執行時間      | 當前執行狀態        | 排程操作          | 任務執行操作                  |
|---------|---------|------------|--------------|---------------|--------------|----------------------------|
| ScriptA | 每日      | 每日 02:00   | 2025/03/17 02:00 | 執行中（進度條） | [停用] [修改] [刪除] | [暫停] [停止] [查看 log]    |
| ScriptB | 每月      | 每月 1, 15日 | 2025/04/01 09:00 | 等待            | [啟用] [修改] [刪除] | -                          |
| ScriptC | 一次      | 03/18 12:00 | 2025/03/18 12:00 | 失敗 [點查看原因] | [啟用] [修改] [刪除] | [重新執行]                    |

---

## 四、排程類型 (新增排程必備)

| 類型         | 必填條件                                |
|------------|------------------------------------------|
| 立即      | 腳本名稱                                   |
| 一次      | 腳本名稱、執行日期、執行時間                 |
| 每分      | 腳本名稱、間隔分鐘數                        |
| 每日      | 腳本名稱、執行時間                          |
| 每周      | 腳本名稱、星期幾（複選）、執行時間           |
| 每月      | 腳本名稱、日期（複選）、執行時間             |

### 排程防呆機制
- 不允許過去時間。
- 每幾分鐘不得低於系統規範（如5分鐘）。
- 已重複的時間排程會提醒（但不強制禁止）。
- 排程重疊提醒（如系統資源負荷過高時提示）。

---

## 五、腳本管理 (彈窗)

| 功能                  | 說明                                                   |
|---------------------|----------------------------------------------------|
| 新增腳本               | 上傳 `.py` 檔案，自動登錄腳本清單。                                |
| 刪除腳本               | 從清單移除，選擇是否刪除實體檔案 `/scripts/`。                           |
| 修改腳本描述           | 編輯腳本用途或說明，方便排程時辨識。                                    |

---

## 六、任務執行狀態管理

| 任務狀態          | 系統顯示                   | 可操作按鈕                          |
|--------------|------------------------|--------------------------------|
| 等待           | 等待排程開始               | 無                                 |
| 執行          | 進度條（或旋轉圖示）         | [暫停] [停止] [查看 log]               |
| 暫停           | 暫停中標記               | [繼續] [停止]                      |
| 繼續           | 執行中標記               | [暫停] [停止] [查看 log]               |
| 完成           | 綠勾標記 + 完成時間        | [查看 log]                         |
| 失敗           | 紅叉標記 + 錯誤訊息        | [重新執行] [查看 log]                  |

---

## 七、主要互動流程圖

| 操作流程                 | 結果                                                 |
|----------------------|--------------------------------------------------|
| 點「新增排程」             | 彈窗開啟 → 設定條件 → 送出 → 排程總覽即時新增                           |
| 點「腳本管理」             | 彈窗開啟 → 增減/修改腳本 → 完成即返回排程總覽                           |
| 排程總覽內「任務執行操作」    | 任務即時狀態更新，如暫停→執行→完成等，畫面同步反映                      |
| 排程總覽內「排程操作」       | 排程啟用/停用/修改/刪除，動態更新列表                               |

---

## 八、視覺互動整體結構（核心）

```
---------------------------------------------------
| [新增排程] [腳本管理]                                    |
---------------------------------------------------
| 腳本名稱 | 排程類型 | 條件  | 下一次執行 | 當前執行 | 排程操作 | 任務執行操作 |
---------------------------------------------------
| ...（所有排程一覽）                                       |
---------------------------------------------------
```

> **不需分頁，集中總覽所有狀態與操作**。

---

## 九、未來擴充彈性（預留）

| 擴充功能                      | 備註                        |
|---------------------------|---------------------------|
| 腳本分組、標籤                   | 針對大量腳本的未來分群管理需求。     |
| 腳本即時編輯                   | 可支援線上編輯腳本內容（進階版功能）。 |
| 系統資源保護機制（佇列/負載平衡）  | 針對大量排程同時執行時保護機制。    |

---

## 十、總結
1. **排程為中心、執行同步整合**。
2. **操作簡單、直覺、無跳頁**，以彈窗為唯一操作入口。
3. **全面考慮執行控制、即時反饋、錯誤重試**。
4. **保持簡單核心，保留未來擴充彈性**。

---

