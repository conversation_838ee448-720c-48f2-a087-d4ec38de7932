# -*- coding: utf-8 -*-
import os
import re
import sys

def log_update(old_path, new_path, file_path):
    """記錄API路徑更新"""
    print(f"更新: {old_path} -> {new_path} 在文件: {file_path}")

def update_api_paths(file_path):
    """更新文件中的API路徑"""
    # API路徑映射
    path_mappings = [
        # 腳本API
        (r'/script/api/scripts', '/api/v1/scripts'),
        (r'/script/api/script/([^/]+)', r'/api/v1/scripts/\1'),
        (r'/script/api/upload', '/api/v1/scripts/upload'),
        (r'/script/api/execute/([^/]+)', r'/api/v1/scripts/\1/execute'),
        
        # 排程API
        (r'/schedule/api/schedules', '/api/v1/schedules'),
        (r'/schedule/api/schedule/([^/]+)', r'/api/v1/schedules/\1'),
        (r'/schedule/api/schedule/([^/]+)/toggle', r'/api/v1/schedules/\1/toggle'),
        
        # 系統資源API
        (r'/system-resources', '/api/v1/system/resources'),
        (r'/api/system-resources', '/api/v1/system/resources')
    ]
    
    # 檢查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    # 讀取文件內容
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
    except Exception as e:
        print(f"讀取文件出錯: {file_path}: {str(e)}")
        return False
    
    # 原始內容
    original_content = content
    
    # 更新API路徑
    for old_pattern, new_pattern in path_mappings:
        # 計算替換前出現次數
        matches = re.findall(old_pattern, content)
        match_count = len(matches)
        
        if match_count > 0:
            # 執行替換
            new_content = re.sub(old_pattern, new_pattern, content)
            
            # 如果內容有變化，更新內容並記錄
            if new_content != content:
                content = new_content
                log_update(old_pattern, new_pattern, file_path)
    
    # 如果內容有變化，寫回文件
    if content != original_content:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"✓ 已更新文件: {file_path}")
            return True
        except Exception as e:
            print(f"! 寫入文件出錯: {file_path}: {str(e)}")
            return False
    else:
        print(f"- 文件無需更新: {file_path}")
        return False

def scan_directory(directory, extensions=None):
    """掃描目錄並更新檔案中的API路徑"""
    if extensions is None:
        extensions = ['.py', '.js', '.html']
    
    updated_files = 0
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            _, ext = os.path.splitext(file)
            if ext.lower() in extensions:
                file_path = os.path.join(root, file)
                if update_api_paths(file_path):
                    updated_files += 1
    
    return updated_files

def main():
    """主函數"""
    print("=== 開始更新API路徑 ===")
    
    # 確保當前目錄是專案根目錄
    if not os.path.exists('app'):
        print("錯誤: 請在專案根目錄下運行此腳本")
        sys.exit(1)
    
    # 需要掃描的目錄
    directories = ['app']
    
    # 掃描目錄並更新API路徑
    total_updated = 0
    for directory in directories:
        print(f"\n掃描目錄: {directory}")
        updated = scan_directory(directory)
        total_updated += updated
    
    print(f"\n=== API路徑更新完成，共更新了 {total_updated} 個文件 ===")

if __name__ == "__main__":
    main() 