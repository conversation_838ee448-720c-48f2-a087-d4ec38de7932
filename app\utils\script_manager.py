import os
import logging
import multiprocessing
import signal
import time
from datetime import datetime
from flask import current_app
from app import socketio

class ScriptManager:
    def __init__(self):
        self.running_scripts = {}
        self.script_states = {}
        self.script_logs = {}
        
    def get_script_info(self, script_name):
        """獲取腳本信息"""
        path = os.path.join(current_app.config['SCRIPTS_FOLDER'], script_name)
        state = self.script_states.get(script_name, {
            'status': 'STOPPED',
            'text': current_app.config['SCRIPT_STATES']['STOPPED']['text'],
            'color': current_app.config['SCRIPT_STATES']['STOPPED']['color']
        })
        return {
            'name': script_name,
            'path': path,
            'status': state['status'],
            'statusText': state['text'],
            'statusColor': state['color'],
            'lastRun': None  # TODO: 實現最後運行時間追踪
        }

    def run_script(self, script_name):
        """執行腳本"""
        if script_name in self.running_scripts:
            return False

        try:
            script_path = os.path.join(current_app.config['SCRIPTS_FOLDER'], script_name)
            process = multiprocessing.Process(
                target=self._run_script_process,
                args=(script_path, script_name)
            )
            process.start()
            
            self.running_scripts[script_name] = process
            self._update_script_state(script_name, 'RUNNING')
            self._log_script(script_name, f"Started script {script_name}")
            return True
        except Exception as e:
            self._log_script(script_name, f"Error starting script: {str(e)}")
            return False

    def pause_script(self, script_name):
        """暫停腳本"""
        if script_name not in self.running_scripts:
            return False
        
        process = self.running_scripts[script_name]
        try:
            os.kill(process.pid, signal.SIGSTOP)
            self._update_script_state(script_name, 'PAUSED')
            self._log_script(script_name, f"Paused script {script_name}")
            return True
        except Exception as e:
            self._log_script(script_name, f"Error pausing script: {str(e)}")
            return False

    def resume_script(self, script_name):
        """繼續執行腳本"""
        if script_name not in self.running_scripts:
            return False
        
        process = self.running_scripts[script_name]
        try:
            os.kill(process.pid, signal.SIGCONT)
            self._update_script_state(script_name, 'RUNNING')
            self._log_script(script_name, f"Resumed script {script_name}")
            return True
        except Exception as e:
            self._log_script(script_name, f"Error resuming script: {str(e)}")
            return False

    def stop_script(self, script_name):
        """終止腳本"""
        if script_name not in self.running_scripts:
            return False
        
        process = self.running_scripts[script_name]
        try:
            process.terminate()
            process.join(timeout=5)
            if process.is_alive():
                process.kill()
            
            del self.running_scripts[script_name]
            self._update_script_state(script_name, 'STOPPED')
            self._log_script(script_name, f"Stopped script {script_name}")
            return True
        except Exception as e:
            self._log_script(script_name, f"Error stopping script: {str(e)}")
            return False

    def get_script_status(self, script_name):
        """獲取腳本狀態"""
        return self.script_states.get(script_name, {
            'status': 'STOPPED',
            'text': current_app.config['SCRIPT_STATES']['STOPPED']['text'],
            'color': current_app.config['SCRIPT_STATES']['STOPPED']['color']
        })

    def get_script_log(self, script_name):
        """獲取腳本日誌"""
        return self.script_logs.get(script_name, [])

    def _run_script_process(self, script_path, script_name):
        """在單獨的進程中執行腳本"""
        try:
            # 重定向標準輸出到自定義處理器
            import sys
            from io import StringIO
            
            class ScriptOutput:
                def __init__(self, script_name):
                    self.script_name = script_name
                    self.buffer = StringIO()

                def write(self, text):
                    if text.strip():
                        socketio.emit('script_log', {
                            'script_name': self.script_name,
                            'message': text.strip()
                        })
                    self.buffer.write(text)

                def flush(self):
                    self.buffer.flush()

            sys.stdout = ScriptOutput(script_name)
            
            with open(script_path, 'r', encoding='utf-8') as f:
                exec(f.read())
                
            self._update_script_state(script_name, 'COMPLETED')
        except Exception as e:
            error_msg = f"Error executing script {script_path}: {str(e)}"
            logging.error(error_msg)
            self._update_script_state(script_name, 'ERROR')
            socketio.emit('script_log', {
                'script_name': script_name,
                'message': error_msg
            })
        finally:
            if script_name in self.running_scripts:
                del self.running_scripts[script_name]

    def _update_script_state(self, script_name, state):
        """更新腳本狀態"""
        self.script_states[script_name] = {
            'status': state,
            'text': current_app.config['SCRIPT_STATES'][state]['text'],
            'color': current_app.config['SCRIPT_STATES'][state]['color']
        }
        
        # 通過 WebSocket 發送狀態更新
        socketio.emit('script_status_update', {
            'script_name': script_name,
            'status': state,
            'text': current_app.config['SCRIPT_STATES'][state]['text'],
            'color': current_app.config['SCRIPT_STATES'][state]['color']
        })

    def _log_script(self, script_name, message):
        """記錄腳本日誌"""
        if script_name not in self.script_logs:
            self.script_logs[script_name] = []
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {message}"
        self.script_logs[script_name].append(log_entry)
        logging.info(log_entry)
        
        # 通過 WebSocket 發送日誌更新
        socketio.emit('script_log', {
            'script_name': script_name,
            'message': log_entry
        })