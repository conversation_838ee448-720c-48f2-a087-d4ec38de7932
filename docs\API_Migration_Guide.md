# Python 腳本管理與排程系統 API 遷移指南

*文檔版本：1.0.0*  
*最後更新日期：2025-03-27*

## 目錄
1. [介紹](#介紹)
2. [遷移時間表](#遷移時間表)
3. [腳本管理 API 遷移](#腳本管理-api-遷移)
4. [排程管理 API 遷移](#排程管理-api-遷移)
5. [系統資源 API 遷移](#系統資源-api-遷移)
6. [常見問題](#常見問題)

## 介紹

為提高系統維護性與一致性，我們對 API 路徑進行了標準化調整。本文檔提供從舊 API 路徑向新 API 路徑的遷移指南，以協助開發人員順利完成過渡。

### 遷移必要性
- 提高 API 路徑一致性
- 優化路由結構與命名規範
- 為後續功能擴展提供更清晰的路徑規則

### 遷移策略
- 後端將在過渡期內同時支援新舊 API 路徑
- 前端代碼應盡快更新為新 API 路徑
- 舊 API 路徑將在過渡期後被移除

## 遷移時間表

| 階段 | 時間 | 說明 |
|------|------|------|
| 1. 雙路徑支援 | 2025-03-27 至 2025-05-01 | 系統同時支援新舊 API 路徑，但舊路徑將顯示棄用警告 |
| 2. 僅新路徑可用 | 2025-05-01 之後 | 系統僅支援新 API 路徑，舊路徑將返回 410 Gone 狀態碼 |

## 腳本管理 API 遷移

### 路徑對照表

| 功能 | 舊路徑 | 新路徑 | 
|------|--------|--------|
| 獲取腳本列表 | `/api/scripts` | `/script` |
| 上傳腳本 | `/api/scripts/upload` | `/script` |
| 獲取腳本信息 | `/api/scripts/{script_name}/info` | `/script/{script_name}/info` |
| 更新腳本元數據 | `/api/scripts/{script_name}/meta` | `/script/{script_name}/meta` |
| 刪除腳本 | `/api/scripts/{script_name}` | `/script/{script_name}` |
| 驗證腳本 | `/api/scripts/validate` | `/script/validate` |
| 安裝依賴 | `/api/scripts/install-dependencies` | `/script/install-dependencies` |

### 代碼示例

從：
```javascript
fetch('/api/scripts')
  .then(response => response.json())
  .then(data => {
    // 處理數據
  });
```

到：
```javascript
fetch('/script')
  .then(response => response.json())
  .then(data => {
    // 處理數據
  });
```

## 排程管理 API 遷移

### 路徑對照表

| 功能 | 舊路徑 | 新路徑 | 
|------|--------|--------|
| 獲取排程列表 | `/api/schedule` | `/schedule/api/schedule` |
| 創建排程 | `/api/schedule` | `/schedule/api/schedule` |
| 獲取排程詳情 | `/api/schedule/{id}` | `/schedule/api/schedule/{id}` |
| 更新排程 | `/api/schedule/{id}` | `/schedule/api/schedule/{id}` |
| 刪除排程 | `/api/schedule/{id}` | `/schedule/api/schedule/{id}` |
| 切換排程狀態 | `/api/schedule/{id}/toggle` | `/schedule/api/schedule/{id}/toggle` |
| 執行排程 | `/api/schedule/{id}/execute` | `/schedule/api/schedule/{id}/execute` |
| 獲取日誌 | `/api/schedule/{id}/logs` | `/schedule/api/schedule/{id}/logs` |
| 任務操作 | `/api/schedule/{id}/task/{action}` | `/schedule/api/schedule/{id}/task/{action}` |

### 代碼示例

從：
```javascript
fetch('/api/schedule')
  .then(response => response.json())
  .then(data => {
    // 處理數據
  });
```

到：
```javascript
fetch('/schedule/api/schedule')
  .then(response => response.json())
  .then(data => {
    // 處理數據
  });
```

## 系統資源 API 遷移

### 路徑對照表

| 功能 | 舊路徑 | 新路徑 | 
|------|--------|--------|
| 獲取系統資源 | `/api/system-resources` | `/schedule/api/system-resources` |

### 代碼示例

從：
```javascript
fetch('/api/system-resources')
  .then(response => response.json())
  .then(data => {
    // 處理數據
  });
```

到：
```javascript
fetch('/schedule/api/system-resources')
  .then(response => response.json())
  .then(data => {
    // 處理數據
  });
```

## 常見問題

### Q: 我的應用只能使用舊 API，需要如何處理？
A: 在過渡期間（2025-03-27 至 2025-05-01），系統會維持對舊 API 的支援，但會在日誌中顯示棄用警告。建議盡快更新您的代碼以使用新 API 路徑。

### Q: 使用新舊路徑有功能上的差異嗎？
A: 沒有功能上的差異，只是路徑名稱的變更，請求與響應格式保持一致。

### Q: 我需要同時修改前端和後端代碼嗎？
A: 後端已完成遷移且保持向下兼容性，您只需修改前端代碼中的 API 調用路徑即可。

### Q: 有自動化工具可用於遷移嗎？
A: 我們提供了一個簡單的代碼檢查腳本，可以掃描您的 JavaScript/TypeScript 代碼並標記需要更新的 API 調用。請聯繫管理員獲取此腳本。

---

如您在遷移過程中遇到任何問題，請聯絡系統管理團隊獲取支援。 