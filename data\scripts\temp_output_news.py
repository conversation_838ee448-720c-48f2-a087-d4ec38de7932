import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
import time
import re
import os
import warnings
from collections import deque
import sys

# 確保輸出為 UTF-8，防止多語言環境下亂碼
sys.stdout.reconfigure(encoding='utf-8')

# 忽略 SSL 憑證警告
warnings.filterwarnings("ignore", message="Unverified HTTPS request")

# 儲存已經爬取過的網址，避免重複
visited_urls = set()

# 基本的網站 URL 和域名
base_url = 'https://www.farglory-hotel.com.tw/'
base_domain = urlparse(base_url).netloc

# 限制爬取的範圍
allowed_paths = ['/news/', '/news-detail/']

# 設定儲存文件的目錄
output_dir = 'output\output_news'
os.makedirs(output_dir, exist_ok=True)

# 定義 URL 轉合法文件名
def url_to_filename(url, prefix="output_"):
    # 加上前置字串 "output_"，並處理不合法字符
    return prefix + (re.sub(r'[\\/:*?"<>|]', '_', urlparse(url).path.strip('/')) or 'index')

# 正規化 URL，移除 query 和 fragment
def normalize_url(url):
    parsed = urlparse(url)
    normalized = parsed._replace(query="", fragment="").geturl()
    return normalized

# 爬取網站（廣度優先）
def scrape_site(base_url, start_path):
    queue = deque([(urljoin(base_url, start_path), 0)])  # 初始化隊列，包含 URL 和深度
    while queue:
        url, depth = queue.popleft()

        # 正規化 URL
        url = normalize_url(url)

        # 檢查是否已爬取
        if url in visited_urls:
            print(f"跳過已訪問頁面：{url}")
            continue

        # 檢查是否屬於目標網域
        sub_url_domain = urlparse(url).netloc
        sub_url_path = urlparse(url).path
        if sub_url_domain != base_domain or not any(sub_url_path.startswith(path) for path in allowed_paths):
            print(f"跳過不符合條件的頁面：{url}")
            continue

        print(f"正在爬取: {url}")
        visited_urls.add(url)

        # 發送 GET 請求來抓取網頁內容
        try:
            response = requests.get(url, verify=False)  # 忽略 SSL 憑證驗證
            response.raise_for_status()  # 若請求失敗則引發異常
        except requests.RequestException as e:
            print(f"無法抓取 {url}，錯誤: {e}")
            continue

        # 解析內容
        soup = BeautifulSoup(response.text, 'html.parser')

        # 移除頁首、頁腳、選單等不必要的區塊
        for tag in ['header', 'footer', 'nav', 'aside']:
            for element in soup.find_all(tag):
                element.decompose()

        # 剔除特定的 class 區塊
        for class_name in ['fixedCookieArea', "businessMenuList", "languageBox", "h2title", "contactLink", "footerInfo"]:
            for element in soup.find_all(class_=class_name):
                element.decompose()

        # 擷取主要內容的文字，保留正常段落間的單一換行
        main_content = soup.get_text(separator="")

        # 定義選單段落的正規表達式
        menu_patterns = [
            r"關於我們\s*最新消息\s*最新公告\s*住房優惠\s*餐飲優惠\s*活動及旅遊\s*所有訊息\s*"
            r"英倫客房\s*美饌饗宴\s*英倫西餐廳\s*唐苑中餐廳\s*秋草鍋物料理\s*皮爾沙酒吧\s*麥坊\s*"
            r"婚宴會議\s*皇家婚禮\s*宴會會議\s*設施服務\s*行程推薦\s*尊寵會員\s*交通位置\s*人才招募\s*投資人專區\s*",
            r"訂房\s*優惠\s*客服\s*導航\s*Top",
            r"最新公告\s*住房優惠\s*餐飲優惠\s*活動及旅遊\s*所有訊息\s*",
            r"立即訂房\s*入住人數\s*1\s*2\s*3\s*4\s*立即搜尋\s*",
            r"海景\s*山景\s*全部房型\s*房型入住須知\s*房價表細目\s*",
            r"全部行程\s*花蓮漫遊東海岸\s*花蓮漫遊縱谷\s",
            r"我要洽詢\s*場地規格\s*皇家婚禮\s*宴會會議\s",
            r"查看更多"
        ]

        # 使用正規表達式匹配並移除選單段落
        for pattern in menu_patterns:
            main_content = re.sub(pattern, "", main_content, flags=re.DOTALL)


        # 將段落之間超過 5 行的空白縮減為 3 行
        main_content = re.sub(r'\n{6,}', '\n\n\n', main_content).strip()

        # 檢查內容中是否有 "~"，並替代為 "-"
        main_content = main_content.replace("~", "-")

        # 生成文件名，基於網頁名稱並加上前置字串
        filename = os.path.join(output_dir, url_to_filename(url) + '.txt')
        with open(filename, 'w', encoding='utf-8') as file:
            file.write(f"--- 擷取自 {url} ---\n\n")
            file.write(main_content)

        print(f"內容已保存到: {filename}")

        # 將子頁面加入隊列
        for link in soup.find_all('a', href=True):
            sub_url = urljoin(base_url, link['href'])

            # 檢查是否屬於目標網域並符合允許的子路徑
            sub_url_domain = urlparse(sub_url).netloc
            sub_url_path = urlparse(sub_url).path
            if sub_url_domain != base_domain or not any(sub_url_path.startswith(path) for path in allowed_paths):
                print(f"跳過不符合條件的子頁面：{sub_url}")
                continue

            # 正規化並檢查是否已爬取過
            sub_url = normalize_url(sub_url)
            if sub_url in visited_urls:
                print(f"跳過已訪問頁面：{sub_url}")
                continue

            print(f"加入隊列：{sub_url}")
            queue.append((sub_url, depth + 1))

# 從特定起始頁開始爬取
if __name__ == "__main__":
    scrape_site(base_url, '/news/')
    print("所有頁面已被爬取並分別保存到指定目錄。")