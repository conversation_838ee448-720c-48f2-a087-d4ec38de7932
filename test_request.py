import requests
import json

def test_api():
    print("Testing API endpoints...")
    
    # 測試列出腳本
    response = requests.get('http://localhost:5000/api/scripts')
    print("\nList scripts response:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    
    # 測試腳本信息
    response = requests.get('http://localhost:5000/api/scripts/test_script.py/info')
    print("\nScript info response:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))

if __name__ == '__main__':
    test_api()
