# API 參考文檔

本文檔提供系統 API 的詳細說明。所有 API 路徑都以 `/api/v1` 為前綴。

## 腳本管理 API

### 獲取所有腳本

- **URL**: `/scripts`
- **方法**: `GET`
- **描述**: 獲取系統中所有腳本清單
- **回應**:
  ```json
  {
    "status": "success",
    "data": [
      {
        "id": "script_id",
        "name": "腳本名稱",
        "description": "腳本描述",
        "filename": "script.py",
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z",
        "tags": ["tag1", "tag2"]
      }
    ]
  }
  ```

### 上傳腳本

- **URL**: `/scripts/upload`
- **方法**: `POST`
- **描述**: 上傳新腳本
- **請求**: 
  - `Content-Type`: `multipart/form-data`
  - 參數:
    - `file`: 腳本文件 (必須是 .py 檔案)
    - `name`: 腳本名稱 (可選，預設為文件名)
    - `description`: 腳本描述 (可選)
    - `tags`: 標籤，逗號分隔 (可選)
- **回應**:
  ```json
  {
    "status": "success",
    "message": "腳本上傳成功",
    "data": {
      "id": "script_id",
      "name": "腳本名稱",
      "description": "腳本描述",
      "filename": "script.py",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "tags": ["tag1", "tag2"]
    }
  }
  ```

### 獲取特定腳本

- **URL**: `/scripts/{script_id}`
- **方法**: `GET`
- **描述**: 獲取特定腳本的詳細資訊
- **URL 參數**:
  - `script_id`: 腳本 ID
- **回應**:
  ```json
  {
    "status": "success",
    "data": {
      "id": "script_id",
      "name": "腳本名稱",
      "description": "腳本描述",
      "filename": "script.py",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "tags": ["tag1", "tag2"],
      "content": "# 腳本內容\n..."
    }
  }
  ```

### 更新腳本

- **URL**: `/scripts/{script_id}`
- **方法**: `PUT`
- **描述**: 更新腳本資訊
- **URL 參數**:
  - `script_id`: 腳本 ID
- **請求體**:
  ```json
  {
    "name": "新腳本名稱",
    "description": "新腳本描述",
    "tags": ["tag1", "tag2", "tag3"]
  }
  ```
- **回應**:
  ```json
  {
    "status": "success",
    "message": "腳本信息更新成功",
    "data": {
      "id": "script_id",
      "name": "新腳本名稱",
      "description": "新腳本描述",
      "filename": "script.py",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "tags": ["tag1", "tag2", "tag3"]
    }
  }
  ```

### 刪除腳本

- **URL**: `/scripts/{script_id}`
- **方法**: `DELETE`
- **描述**: 刪除特定腳本
- **URL 參數**:
  - `script_id`: 腳本 ID
- **回應**:
  ```json
  {
    "status": "success",
    "message": "腳本刪除成功"
  }
  ```

### 執行腳本

- **URL**: `/scripts/{script_id}/execute`
- **方法**: `POST`
- **描述**: 執行特定腳本
- **URL 參數**:
  - `script_id`: 腳本 ID
- **請求體** (可選):
  ```json
  {
    "param1": "value1",
    "param2": "value2"
  }
  ```
- **回應**:
  ```json
  {
    "status": "success",
    "message": "腳本執行成功",
    "data": {
      "execution_id": "execution_id",
      "start_time": "2023-01-01T00:00:00Z",
      "end_time": "2023-01-01T00:00:10Z",
      "status": "completed",
      "output": "執行輸出結果...",
      "exit_code": 0
    }
  }
  ```

## 排程管理 API

### 獲取所有排程

- **URL**: `/schedules`
- **方法**: `GET`
- **描述**: 獲取系統中所有排程清單
- **回應**:
  ```json
  {
    "status": "success",
    "data": [
      {
        "id": "schedule_id",
        "name": "排程名稱",
        "script_id": "script_id",
        "cron_expression": "0 0 * * *",
        "description": "排程描述",
        "is_active": true,
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z"
      }
    ]
  }
  ```

### 創建排程

- **URL**: `/schedules`
- **方法**: `POST`
- **描述**: 創建新排程
- **請求體**:
  ```json
  {
    "script_id": "script_id",
    "name": "排程名稱",
    "cron_expression": "0 0 * * *",
    "description": "排程描述",
    "is_active": true
  }
  ```
- **回應**:
  ```json
  {
    "status": "success",
    "message": "排程建立成功",
    "data": {
      "id": "schedule_id",
      "name": "排程名稱",
      "script_id": "script_id",
      "cron_expression": "0 0 * * *",
      "description": "排程描述",
      "is_active": true,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

### 獲取特定排程

- **URL**: `/schedules/{schedule_id}`
- **方法**: `GET`
- **描述**: 獲取特定排程的詳細資訊
- **URL 參數**:
  - `schedule_id`: 排程 ID
- **回應**:
  ```json
  {
    "status": "success",
    "data": {
      "id": "schedule_id",
      "name": "排程名稱",
      "script_id": "script_id",
      "script_name": "腳本名稱",
      "cron_expression": "0 0 * * *",
      "description": "排程描述",
      "is_active": true,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "next_run": "2023-01-02T00:00:00Z"
    }
  }
  ```

### 更新排程

- **URL**: `/schedules/{schedule_id}`
- **方法**: `PUT`
- **描述**: 更新排程資訊
- **URL 參數**:
  - `schedule_id`: 排程 ID
- **請求體**:
  ```json
  {
    "name": "新排程名稱",
    "cron_expression": "0 0 * * *",
    "description": "新排程描述",
    "is_active": true
  }
  ```
- **回應**:
  ```json
  {
    "status": "success",
    "message": "排程更新成功",
    "data": {
      "id": "schedule_id",
      "name": "新排程名稱",
      "script_id": "script_id",
      "cron_expression": "0 0 * * *",
      "description": "新排程描述",
      "is_active": true,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

### 刪除排程

- **URL**: `/schedules/{schedule_id}`
- **方法**: `DELETE`
- **描述**: 刪除特定排程
- **URL 參數**:
  - `schedule_id`: 排程 ID
- **回應**:
  ```json
  {
    "status": "success",
    "message": "排程刪除成功"
  }
  ```

### 切換排程狀態

- **URL**: `/schedules/{schedule_id}/toggle`
- **方法**: `POST`
- **描述**: 切換排程啟用/停用狀態
- **URL 參數**:
  - `schedule_id`: 排程 ID
- **回應**:
  ```json
  {
    "status": "success",
    "message": "排程已啟用",
    "data": {
      "id": "schedule_id",
      "name": "排程名稱",
      "is_active": true,
      "updated_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

## 系統資源 API

### 獲取系統資源

- **URL**: `/system/resources`
- **方法**: `GET`
- **描述**: 獲取系統資源使用情況
- **回應**:
  ```json
  {
    "status": "success",
    "data": {
      "cpu_percent": 25.5,
      "memory_percent": 40.2,
      "schedule_count": 5,
      "max_schedules": 10
    }
  }
  ```

## 錯誤處理

所有 API 在遇到錯誤時都將返回以下格式的回應：

```json
{
  "status": "error",
  "message": "錯誤描述"
}
```

常見的 HTTP 狀態碼：

- `200 OK`: 請求成功
- `201 Created`: 資源創建成功
- `400 Bad Request`: 請求格式錯誤或缺少必要參數
- `404 Not Found`: 請求的資源不存在
- `500 Internal Server Error`: 服務器內部錯誤

## API 版本控制

目前 API 版本為 `v1`，所有路徑均以 `/api/v1` 為前綴。舊的 API 路徑（如 `/script/api/scripts`）將自動重定向到新路徑。 