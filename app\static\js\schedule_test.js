// 測試每週排程時間計算
console.log("==========測試每週排程時間計算==========");

// 模擬 document
document = {
    getElementById: function(id) {
        if (id === 'weekly-time') {
            return { value: '12:00' };
        }
        if (id === 'weekly-auto-adjusted') {
            return { 
                textContent: '',
                classList: {
                    add: function(cls) { console.log(`添加類 ${cls}`) },
                    remove: function(cls) { console.log(`移除類 ${cls}`) }
                }
            };
        }
        return null;
    },
    querySelectorAll: function() {
        return [];
    }
};

// 測試不同星期幾組合的時間計算
function testWeeklyDateCalculation() {
    console.log("測試各種星期幾組合的日期計算");
    
    const originalDate = new Date('2025-03-26T12:00:00'); // 假設今天是週三
    
    // 模擬當前日期
    const realDate = Date;
    Date = function() {
        return new realDate(originalDate);
    };
    Date.prototype = realDate.prototype;
    
    // 測試用例：選擇不同的星期幾組合
    const testCases = [
        { weekdays: [3], description: "只選擇今天(週三)" },
        { weekdays: [0], description: "選擇週日" },
        { weekdays: [4], description: "選擇明天(週四)" },
        { weekdays: [3, 5], description: "選擇週三和週五" },
        { weekdays: [3, 4, 5], description: "選擇週三、週四和週五" },
        { weekdays: [1, 3], description: "選擇週一和週三" }
    ];
    
    // 執行測試
    for (const testCase of testCases) {
        console.log(`\n測試用例: ${testCase.description}`);
        // 使用問題中的計算函數
        const nextDate = getNextValidDayFromWeekdays(testCase.weekdays);
        console.log(`計算結果: ${nextDate.toISOString()}`);
        console.log(`轉換結果: ${convertToServerTime(nextDate)}`);
    }
    
    // 恢復原始Date
    Date = realDate;
}

// 測試週排程日期格式轉換
function testDateConversion() {
    console.log("\n測試日期格式轉換");
    const testDate = new Date('2025-03-30T12:00:00'); // 週日
    
    console.log(`原始日期: ${testDate.toISOString()}`);
    console.log(`convertToServerTime 結果: ${convertToServerTime(testDate)}`);
    console.log(`原始時間物件: 星期=${testDate.getDay()}, 日期=${testDate.getDate()}, 月=${testDate.getMonth()+1}, 年=${testDate.getFullYear()}`);
    
    // 測試直接構建字符串的方法
    const y = testDate.getFullYear();
    const m = String(testDate.getMonth() + 1).padStart(2, '0');
    const d = String(testDate.getDate()).padStart(2, '0');
    const h = String(testDate.getHours()).padStart(2, '0');
    const min = String(testDate.getMinutes()).padStart(2, '0');
    
    const directString = `${y}-${m}-${d}T${h}:${min}:00`;
    console.log(`直接構建字符串: ${directString}`);
}

// 問題分析：測試格式轉換時是否有日期偏移
function testDateTimeIssue() {
    console.log("\n分析日期偏移問題");
    
    // 測試原本的邏輯
    const testDate = new Date('2025-03-30T12:00:00');
    console.log(`原始日期: ${testDate.toISOString()}`);
    
    // 測試 toISOString 轉換
    const isoString = testDate.toISOString();
    console.log(`toISOString 結果: ${isoString}`);
    
    // 檢查時區影響
    const localYear = testDate.getFullYear();
    const localMonth = testDate.getMonth() + 1;
    const localDay = testDate.getDate();
    const localHour = testDate.getHours();
    const localMinute = testDate.getMinutes();
    
    console.log(`本地時間組件: ${localYear}-${localMonth}-${localDay} ${localHour}:${localMinute}`);
    
    // 使用 UTC 方法檢查
    const utcYear = testDate.getUTCFullYear();
    const utcMonth = testDate.getUTCMonth() + 1;
    const utcDay = testDate.getUTCDate();
    const utcHour = testDate.getUTCHours();
    const utcMinute = testDate.getUTCMinutes();
    
    console.log(`UTC時間組件: ${utcYear}-${utcMonth}-${utcDay} ${utcHour}:${utcMinute}`);
    
    // 檢查時區影響
    const tzOffset = testDate.getTimezoneOffset();
    console.log(`時區偏移(分鐘): ${tzOffset}`);
    
    // 檢查是否日期不同
    if (localDay !== utcDay) {
        console.log("檢測到日期偏移問題: 本地日期與UTC日期不同");
    }
}

// 修復方案測試
function testFixedImplementation() {
    console.log("\n測試修復方案");
    
    // 模擬週三
    const originalDate = new Date('2025-03-26T12:00:00');
    
    // 選擇週日
    const selectedWeekday = 0; // 週日
    
    // 原始計算方法
    const result1 = calculateNextWeekdayOriginal(originalDate, selectedWeekday);
    console.log(`原始方法結果: ${result1.toISOString()}, 日期=${result1.getDate()}, 星期=${result1.getDay()}`);
    
    // 修復後的方法
    const result2 = calculateNextWeekdayFixed(originalDate, selectedWeekday);
    console.log(`修復方法結果: ${result2.toISOString()}, 日期=${result2.getDate()}, 星期=${result2.getDay()}`);
    
    // 轉換為發送格式
    console.log(`原始方法轉換結果: ${convertToServerTime(result1)}`);
    console.log(`修復方法轉換結果: ${convertToServerTimeFixed(result2)}`);
}

// 原始日期計算方法(簡化版)
function calculateNextWeekdayOriginal(baseDate, targetWeekday) {
    const currentDay = baseDate.getDay();
    let daysToAdd = (targetWeekday - currentDay + 7) % 7;
    if (daysToAdd === 0) daysToAdd = 7; // 如果是同一天，則設為下週同一天
    
    const result = new Date(baseDate);
    result.setDate(baseDate.getDate() + daysToAdd);
    return result;
}

// 修復的日期計算方法
function calculateNextWeekdayFixed(baseDate, targetWeekday) {
    const currentDay = baseDate.getDay();
    let daysToAdd = (targetWeekday - currentDay + 7) % 7;
    if (daysToAdd === 0) daysToAdd = 7; // 如果是同一天，則設為下週同一天
    
    // 直接使用年月日创建新日期，避免时区问题
    const year = baseDate.getFullYear();
    const month = baseDate.getMonth();
    const day = baseDate.getDate() + daysToAdd;
    const hour = baseDate.getHours();
    const minute = baseDate.getMinutes();
    
    return new Date(year, month, day, hour, minute, 0);
}

// 修復的時間轉換函數
function convertToServerTimeFixed(date) {
    // 直接使用 Date 對象的本地時間組件，完全避免時區轉換
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, '0');
    const d = String(date.getDate()).padStart(2, '0');
    const h = String(date.getHours()).padStart(2, '0');
    const min = String(date.getMinutes()).padStart(2, '0');
    
    return `${y}-${m}-${d}T${h}:${min}:00`;
}

// 執行測試
testWeeklyDateCalculation();
testDateConversion();
testDateTimeIssue();
testFixedImplementation(); 