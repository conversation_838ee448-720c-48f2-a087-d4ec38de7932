import requests
import json
from datetime import datetime, timedelta
import time

BASE_URL = 'http://localhost:5000/api'

def test_create_schedule():
    """測試建立排程"""
    # 建立一個每分鐘執行的排程
    schedule_data = {
        "script_name": "test_script.py",
        "schedule_type": "minute",
        "interval_minutes": 5,  # 每5分鐘執行一次
        "description": "測試排程 - 每5分鐘執行一次",
        "is_active": True
    }
    
    response = requests.post(f"{BASE_URL}/schedules", json=schedule_data)
    print("\n1. 建立排程結果:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    
    return response.json().get('data', {}).get('id')

def test_get_schedules():
    """測試獲取所有排程"""
    response = requests.get(f"{BASE_URL}/schedules")
    print("\n2. 獲取所有排程:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))

def test_get_schedule(schedule_id):
    """測試獲取特定排程"""
    response = requests.get(f"{BASE_URL}/schedules/{schedule_id}")
    print(f"\n3. 獲取排程 {schedule_id} 的詳細資訊:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))

def main():
    try:
        # 建立排程
        schedule_id = test_create_schedule()
        if not schedule_id:
            print("建立排程失敗")
            return
            
        # 等待幾秒讓排程執行
        print("\n等待 5 秒讓排程執行...")
        time.sleep(5)
        
        # 獲取所有排程
        test_get_schedules()
        
        # 獲取特定排程
        test_get_schedule(schedule_id)
        
    except Exception as e:
        print(f"測試過程中發生錯誤: {str(e)}")

if __name__ == '__main__':
    main() 