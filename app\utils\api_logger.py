# -*- coding: utf-8 -*-
import logging
import time
import traceback
import functools
import json
from flask import request, current_app, g

# 創建專用日誌記錄器
api_logger = logging.getLogger('app.api')
api_logger.setLevel(logging.DEBUG)

class APILogger:
    """API日誌記錄器，用於記錄API請求與回應"""
    
    @staticmethod
    def log_request(func):
        """裝飾器：記錄API請求內容"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            req_id = int(time.time() * 1000)
            g.request_id = req_id
            
            # 記錄請求內容
            request_data = {
                'method': request.method,
                'path': request.path,
                'remote_addr': request.remote_addr,
                'headers': dict(request.headers),
                'args': request.args.to_dict(),
            }
            
            if request.is_json:
                request_data['json'] = request.get_json()
            elif request.form:
                request_data['form'] = request.form.to_dict()
            
            api_logger.info(f"[{req_id}] 請求開始: {request.method} {request.path}")
            api_logger.debug(f"[{req_id}] 請求數據: {json.dumps(request_data, ensure_ascii=False)}")
            
            start_time = time.time()
            try:
                response = func(*args, **kwargs)
                elapsed = (time.time() - start_time) * 1000
                
                # 記錄成功回應
                api_logger.info(f"[{req_id}] 請求完成: {request.method} {request.path} - 耗時: {elapsed:.2f}ms")
                return response
            except Exception as e:
                elapsed = (time.time() - start_time) * 1000
                
                # 記錄異常
                api_logger.error(f"[{req_id}] 請求異常: {request.method} {request.path} - 耗時: {elapsed:.2f}ms")
                api_logger.error(f"[{req_id}] 異常信息: {str(e)}")
                api_logger.error(f"[{req_id}] 異常堆疊: {traceback.format_exc()}")
                raise
            
        return wrapper
    
    @staticmethod
    def monitor_api_health(feature_name):
        """裝飾器：監控API健康狀態"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    elapsed = (time.time() - start_time) * 1000
                    
                    # 記錄成功完成
                    api_logger.info(f"功能 [{feature_name}] 正常運行 - 耗時: {elapsed:.2f}ms")
                    return result
                except Exception as e:
                    elapsed = (time.time() - start_time) * 1000
                    
                    # 記錄功能異常
                    api_logger.error(f"功能 [{feature_name}] 發生異常 - 耗時: {elapsed:.2f}ms")
                    api_logger.error(f"功能 [{feature_name}] 異常信息: {str(e)}")
                    api_logger.error(f"功能 [{feature_name}] 異常堆疊: {traceback.format_exc()}")
                    raise
            return wrapper
        return decorator

def setup_api_logger(app):
    """設置API日誌記錄器"""
    # 確保日誌目錄存在
    import os
    log_dir = app.config.get('LOG_FOLDER', os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs'))
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置檔案處理器
    api_log_file = os.path.join(log_dir, 'api.log')
    file_handler = logging.FileHandler(api_log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    
    # 配置格式
    formatter = logging.Formatter('[%(asctime)s] %(levelname)s in %(module)s: %(message)s')
    file_handler.setFormatter(formatter)
    
    # 添加處理器
    api_logger.addHandler(file_handler)
    
    # 添加控制台處理器用於調試
    if app.debug:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(formatter)
        api_logger.addHandler(console_handler)
    
    app.logger.info("API日誌記錄器已設置") 