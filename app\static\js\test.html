<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排程測試</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', 'PingFang TC', sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        #test-output {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            font-family: Consolas, monospace;
            white-space: pre-wrap;
            height: 500px;
            overflow-y: auto;
            font-size: 14px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>排程日期計算測試</h1>
        <div id="test-output">測試輸出將顯示在這裡...</div>
        <button id="run-test">執行測試</button>
    </div>

    <!-- 複製 schedule.js 必要的功能 -->
    <script>
        // 檢查指定時間是否在當前時間之後
        function isTimeInFuture(hour, minute) {
            const now = new Date();
            return (now.getHours() < hour || (now.getHours() === hour && now.getMinutes() < minute));
        }

        // 獲取一組星期幾中下一個最近的有效日期
        function getNextValidDayFromWeekdays(selectedDays) {
            if (!selectedDays || selectedDays.length === 0) return null;
            
            // 獲取當前日期和時間
            const now = new Date();
            const today = now.getDay(); // 0-6 (週日-週六)
            
            // 獲取選擇的時間 (測試中固定為12:00)
            const selectedHour = 12;
            const selectedMinute = 0;
            
            // 檢查今天是否為選中的星期幾，且時間未過
            const todayIsSelected = selectedDays.includes(today);
            const timeIsFuture = isTimeInFuture(selectedHour, selectedMinute);
            
            if (todayIsSelected && timeIsFuture) {
                const resultDate = new Date(
                    now.getFullYear(),
                    now.getMonth(),
                    now.getDate(),
                    selectedHour,
                    selectedMinute,
                    0
                );
                return resultDate;
            } else {
                return findNextDate(today, selectedDays, selectedHour, selectedMinute);
            }
        }

        // 輔助函數：找出當前日期之後的下一個最近的選中星期幾
        function findNextDate(currentDay, selectedDays, hour, minute) {
            // 獲取今天的日期
            const today = new Date();
            const todayYear = today.getFullYear();
            const todayMonth = today.getMonth();
            const todayDate = today.getDate();
            
            // 存儲所有可能的下一個日期
            const possibleDates = [];
            
            // 遍歷所有選中的星期幾
            for (const weekday of selectedDays) {
                // 計算此星期幾與今天的差距天數
                let dayDiff = (weekday - currentDay + 7) % 7;
                
                // 如果是今天但時間已過，則使用下週同一天
                if (dayDiff === 0 && !isTimeInFuture(hour, minute)) {
                    dayDiff = 7;
                }
                
                // 創建日期對象
                const nextDate = new Date(todayYear, todayMonth, todayDate + dayDiff, hour, minute, 0);
                
                // 保存這個日期
                possibleDates.push({
                    date: nextDate,
                    diff: dayDiff
                });
            }
            
            // 按天數差排序，選擇最近的日期
            possibleDates.sort((a, b) => a.diff - b.diff);
            return possibleDates[0].date;
        }

        // 將日期轉換為服務器所需的格式，保留本地時間
        function convertToServerTime(date) {
            // 從原始日期中提取年、月、日、時、分值
            const y = date.getFullYear();
            const m = date.getMonth() + 1; // 注意 JavaScript 月份是 0-11
            const d = date.getDate();
            const h = date.getHours();
            const min = date.getMinutes();
            
            // 格式化為兩位數
            const padZero = (num) => num.toString().padStart(2, '0');
            
            // 構建標準 ISO 格式的字符串，但不包含任何時區信息
            const dateStr = `${y}-${padZero(m)}-${padZero(d)}T${padZero(h)}:${padZero(min)}:00`;
            
            return dateStr;
        }
    </script>

    <!-- 測試腳本 -->
    <script src="schedule_test.js"></script>

    <script>
        // 重定向 console.log 輸出到頁面元素
        const originalConsoleLog = console.log;
        console.log = function() {
            const output = document.getElementById('test-output');
            for (let i = 0; i < arguments.length; i++) {
                let arg = arguments[i];
                if (typeof arg === 'object') {
                    arg = JSON.stringify(arg, null, 2);
                }
                output.textContent += arg + '\n';
            }
            originalConsoleLog.apply(console, arguments);
        };

        // 按鈕點擊事件
        document.getElementById('run-test').addEventListener('click', function() {
            document.getElementById('test-output').textContent = '';
            // 測試函數已在 schedule_test.js 中自動執行
        });
    </script>
</body>
</html> 