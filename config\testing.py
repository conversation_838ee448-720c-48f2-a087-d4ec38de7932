# -*- coding: utf-8 -*-
from config.default import DefaultConfig

class TestingConfig(DefaultConfig):
    """
    測試環境配置
    """
    
    # 調試模式
    DEBUG = True
    
    # 測試模式
    TESTING = True
    
    # SQL回顯
    SQLALCHEMY_ECHO = True
    
    # 數據庫URI（使用內存數據庫）
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # WTF CSRF驗證關閉
    WTF_CSRF_ENABLED = False
    
    # 最大排程數
    MAX_SCHEDULES = 5
    
    @classmethod
    def init_app(cls, app):
        """初始化應用配置"""
        super().init_app(app)
        
        # 測試環境特定初始化
        app.logger.info('初始化測試環境配置') 