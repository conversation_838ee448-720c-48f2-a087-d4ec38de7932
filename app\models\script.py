import os
from datetime import datetime
from typing import Dict, Any
from .. import db

class Script(db.Model):
    """腳本模型"""
    __tablename__ = 'scripts'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    file_path = db.Column(db.String(500), nullable=False)
    tags = db.Column(db.String(500), nullable=True, default='未分類')  # 標籤字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)
    is_active = db.Column(db.Boolean, default=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'file_path': self.file_path,
            'tags': self.tags or '未分類',
            'created_at': self.created_at.strftime('%Y-%m-%dT%H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%dT%H:%M:%S'),
            'is_active': self.is_active
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Script':
        """從字典格式創建實例"""
        script = cls(
            name=data['name'],
            description=data.get('description'),
            file_path=data['file_path'],
            tags=data.get('tags', '未分類'),
            is_active=data.get('is_active', True)
        )
        if 'id' in data:
            script.id = data['id']
        return script
    
    def update_file_info(self):
        """更新檔案資訊"""
        if self.file_path and os.path.exists(self.file_path):
            self.file_size = os.path.getsize(self.file_path)
            self.updated_at = datetime.now()
    
    def delete_file(self):
        """刪除腳本檔案"""
        if self.file_path and os.path.exists(self.file_path):
            try:
                os.remove(self.file_path)
            except Exception as e:
                raise Exception(f"刪除腳本檔案失敗: {str(e)}") 