#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試標籤問題的腳本
"""
from app import create_app, db
from app.models.script import Script

def check_database():
    """檢查數據庫中的腳本標籤"""
    app = create_app()
    with app.app_context():
        scripts = Script.query.all()
        print(f'數據庫中有 {len(scripts)} 個腳本:')
        print('=' * 50)
        
        for script in scripts:
            print(f'ID: {script.id}')
            print(f'名稱: {script.name}')
            print(f'描述: {script.description}')
            print(f'標籤: "{script.tags}"')
            print(f'標籤類型: {type(script.tags)}')
            print(f'標籤長度: {len(script.tags) if script.tags else 0}')
            print(f'創建時間: {script.created_at}')
            print('-' * 30)

def test_script_creation():
    """測試創建帶標籤的腳本"""
    app = create_app()
    with app.app_context():
        # 創建測試腳本
        test_script = Script(
            name='test_tag_script.py',
            description='測試標籤功能',
            file_path='test_path.py',
            tags='檢核通知'
        )
        
        db.session.add(test_script)
        db.session.commit()
        
        # 查詢剛創建的腳本
        created_script = Script.query.filter_by(name='test_tag_script.py').first()
        if created_script:
            print('測試腳本創建成功:')
            print(f'名稱: {created_script.name}')
            print(f'標籤: "{created_script.tags}"')
            print(f'to_dict(): {created_script.to_dict()}')
        else:
            print('測試腳本創建失敗')

if __name__ == '__main__':
    print('🔍 檢查數據庫中的腳本標籤...')
    check_database()
    
    print('\n🧪 測試腳本創建...')
    test_script_creation()
    
    print('\n🔍 再次檢查數據庫...')
    check_database()
