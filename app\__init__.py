# -*- coding: utf-8 -*-
import os
import logging
from flask import Flask
from logging.handlers import RotatingFileHandler
from flask_sqlalchemy import SQLAlchemy
from flask_socketio import SocketIO
from config import get_config

# 初始化擴充套件
db = SQLAlchemy()
socketio = SocketIO()

def create_app(config_name=None):
    """建立應用程式實例"""
    app = Flask(__name__)
    
    # 配置應用
    if config_name is None:
        # 從環境變量獲取配置名稱
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # 加載配置
    config_class = get_config(config_name)
    app.config.from_object(config_class)
    config_class.init_app(app)
    
    # 確保必要的目錄存在
    for folder in [app.config['UPLOAD_FOLDER'], app.config['OUTPUT_FOLDER']]:
        if not os.path.exists(folder):
            os.makedirs(folder)
    
    # 初始化擴充套件
    db.init_app(app)
    
    # 在非測試環境中初始化 WebSocket
    if not app.config['TESTING']:
        socketio.init_app(app, 
                         message_queue=app.config.get('SOCKETIO_MESSAGE_QUEUE'),
                         async_mode=app.config.get('SOCKETIO_ASYNC_MODE'))
        
        # 註冊 WebSocket 事件處理器
        from .events import socket_events
    
    # 設置日誌系統
    setup_logging(app)
    
    # 初始化API記錄器
    try:
        from app.utils.api_logger import setup_api_logger
        setup_api_logger(app)
        app.logger.info('API日誌記錄器初始化完成')
    except Exception as e:
        app.logger.error(f'API日誌記錄器初始化失敗: {str(e)}')
    
    # 先初始化服務，因為藍圖可能會使用這些服務
    try:
        from app.services import init_services
        init_services(app)
        app.logger.info('服務初始化完成')
    except Exception as e:
        app.logger.error(f'服務初始化失敗: {str(e)}')
    
    # 註冊藍圖
    register_blueprints(app)
    
    # 初始化API
    try:
        from app.api import init_app as init_api
        init_api(app)
        app.logger.info('API模組初始化完成')
    except Exception as e:
        app.logger.error(f'API模組初始化失敗: {str(e)}')
    
    with app.app_context():
        try:
            db.create_all()
            app.logger.info('資料庫表格初始化完成')
        except Exception as e:
            app.logger.error(f'資料庫表格初始化失敗: {str(e)}')
    
    return app

def setup_logging(app):
    """設置日誌系統"""
    log_dir = app.config.get('LOG_FOLDER', os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs'))
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 移除現有的處理器
    for handler in app.logger.handlers[:]:
        app.logger.removeHandler(handler)
    
    # 配置新的檔案處理器
    log_file = os.path.join(log_dir, 'app.log')
    
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=1024 * 1024,  # 1MB
        backupCount=5,
        encoding='utf-8'
    )
    
    file_handler.setFormatter(logging.Formatter(
        '[%(asctime)s] %(levelname)s in %(module)s: %(message)s'
    ))
    file_handler.setLevel(logging.DEBUG)
    
    # 添加處理器到應用日誌器
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.DEBUG)
    
    # 添加控制台日誌處理器
    if app.debug:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '[%(asctime)s] %(levelname)s in %(module)s: %(message)s'
        ))
        console_handler.setLevel(logging.DEBUG)
        app.logger.addHandler(console_handler)
        
    app.logger.info('應用程式日誌系統初始化完成')

def register_blueprints(app):
    """註冊所有藍圖"""
    from .core import bp as core_bp
    from .routes.main_routes import bp as main_blueprint
    from .routes.schedule_routes import bp as schedule_blueprint
    from .routes.script_routes import bp as script_blueprint
    # 使用對應 system_routes.py 中的變數名 bp
    from .routes.system_routes import bp as system_blueprint
    
    app.logger.debug('註冊藍圖...')
    
    try:
        app.register_blueprint(core_bp)
        app.logger.debug('核心藍圖註冊完成')
    except Exception as e:
        app.logger.error(f'核心藍圖註冊失敗: {str(e)}')
    
    try:
        app.register_blueprint(main_blueprint)
        app.logger.debug('主頁藍圖註冊完成')
    except Exception as e:
        app.logger.error(f'主頁藍圖註冊失敗: {str(e)}')
        
    try:
        app.register_blueprint(schedule_blueprint, url_prefix='/schedule')
        app.logger.debug('排程藍圖註冊完成')
    except Exception as e:
        app.logger.error(f'排程藍圖註冊失敗: {str(e)}')
        
    try:
        app.register_blueprint(script_blueprint, url_prefix='/script')
        app.logger.debug('腳本藍圖註冊完成')
    except Exception as e:
        app.logger.error(f'腳本藍圖註冊失敗: {str(e)}')
        
    try:
        app.register_blueprint(system_blueprint)
        app.logger.debug('系統藍圖註冊完成')
    except Exception as e:
        app.logger.error(f'系統藍圖註冊失敗: {str(e)}')
    
    app.logger.debug('所有藍圖註冊完成')
