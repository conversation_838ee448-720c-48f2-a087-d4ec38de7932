// 全局變量
let selectedFile = null; // 當前選擇的文件
let validationResult = null; // 驗證結果
let requiredPackages = []; // 所需的依賴包
let currentStep = 1; // 當前步驟
let loadedScripts = []; // 已加載的腳本列表
let modals = {}; // 模態框集合

// 輔助函數：檢查腳本是否包含特定標籤
function scriptHasTag(script, tagName) {
    if (!script.tags) return false;

    if (Array.isArray(script.tags)) {
        return script.tags.includes(tagName);
    } else if (typeof script.tags === 'string') {
        // 處理逗號分隔的標籤字符串
        const tagsArray = script.tags.split(',').map(tag => tag.trim());
        return tagsArray.includes(tagName);
    }

    return false;
}

// 常量
const STATUS_COLORS = {
    success: '#10B981',
    error: '#EF4444',
    warning: '#F59E0B',
    info: '#3B82F6'
};

// 初始化應用
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 初始化應用...');
    console.log('📍 當前URL:', window.location.href);
    console.log('📍 當前時間:', new Date().toISOString());

    // 檢查必要的元素是否存在
    const scheduleList = document.getElementById('scheduleList');
    console.log('📋 排程列表元素:', scheduleList ? '存在' : '不存在');

    // 初始化所有模態框
    if (typeof bootstrap !== 'undefined') {
        document.querySelectorAll('.modal').forEach(modalElement => {
            const modalId = modalElement.id;
            modals[modalId] = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: false
            });
        
        // 為腳本模態框添加事件監聽
        if (modalId === 'scriptModal') {
            modalElement.addEventListener('shown.bs.modal', function() {
                console.log('腳本模態框已顯示，加載腳本列表');
                loadScriptsList();
            });

            // 添加隱藏事件監聽，確保關閉時重置到列表視圖
            modalElement.addEventListener('hidden.bs.modal', function() {
                console.log('腳本模態框已隱藏，重置狀態');

                // 重置到列表視圖
                const scriptListView = document.getElementById('scriptListView');
                const uploadView = document.getElementById('uploadView');

                if (scriptListView && uploadView) {
                    uploadView.style.display = 'none';
                    scriptListView.style.display = 'block';
                }

                // 重置上傳狀態
                resetUploadState();

                console.log('腳本模態框狀態重置完成');
            });
        }
        });
    } else {
        console.error('Bootstrap 未載入，無法初始化模態框');
    }

    initializeButtonEffects();
    initializeUploadForm();
    
    // 預先加載腳本列表，即使腳本管理模態框尚未打開
    try {
        loadScriptsList();
    } catch (error) {
        console.error('載入腳本列表失敗:', error);
    }

    // 加載排程列表
    try {
        loadSchedulesList();
    } catch (error) {
        console.error('載入排程列表失敗:', error);
    }

    // 定期更新系統資源
    try {
        updateSystemResources();
        setInterval(updateSystemResources, 5000);
    } catch (error) {
        console.error('更新系統資源失敗:', error);
    }
});

// 排程相關函數 - 新增
// 加載排程列表
async function loadSchedulesList() {
    try {
        console.log('🔄 開始載入排程列表...');
        const url = `${window.location.origin}/schedule/api/schedule`;
        console.log('📡 請求URL:', url);

        const response = await fetch(url);
        console.log('📊 排程列表響應狀態:', response.status);
        console.log('📊 響應頭:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ HTTP錯誤響應內容:', errorText);
            throw new Error(`HTTP error! status: ${response.status}, content: ${errorText}`);
        }

        const data = await response.json();
        console.log('✅ 排程列表響應數據:', data);

        if (data.status === 'success') {
            // 修正元素ID - 使用正確的ID
            const scheduleList = document.getElementById('scheduleList');
            if (scheduleList) {
                scheduleList.innerHTML = data.data.length > 0 ?
                    data.data.map(schedule => createScheduleRow(schedule)).join('') :
                    '<tr><td colspan="5" class="text-center">沒有排程記錄</td></tr>';
                console.log(`成功載入 ${data.data.length} 個排程`);
            } else {
                console.error('找不到排程列表元素 (scheduleList)');
            }
        } else {
            throw new Error(data.message || '載入排程列表失敗');
        }
    } catch (error) {
        console.error('❌ 載入排程列表失敗:', error);
        console.error('❌ 錯誤類型:', error.constructor.name);
        console.error('❌ 錯誤訊息:', error.message);
        console.error('❌ 錯誤堆疊:', error.stack);

        const scheduleList = document.getElementById('scheduleList');
        if (scheduleList) {
            scheduleList.innerHTML = `<tr><td colspan="5" class="text-center text-danger">
                載入排程失敗<br>
                <small>錯誤: ${error.message}</small>
            </td></tr>`;
        }
    }
}

// 創建排程表格行
function createScheduleRow(schedule) {
    // 格式化執行時間
    const nextRun = schedule.next_run ? formatDateTime(new Date(schedule.next_run)) : '未設置';

    // 生成排程類型說明
    const scheduleTypeText = getScheduleTypeText(schedule);

    // 設置狀態樣式
    const statusHtml = getStatusHtml(schedule);

    // 設置操作按鈕
    const actionButtons = getActionButtons(schedule);

    // 直接返回HTML字符串而不是DOM元素
    return `
        <tr>
            <td>${schedule.description || '無描述'}</td>
            <td>${schedule.script_name}</td>
            <td>${nextRun}</td>
            <td>${statusHtml}</td>
            <td>${actionButtons}</td>
        </tr>
    `;
}

// 獲取排程類型的顯示文字
function getScheduleTypeText(schedule) {
    const typeMap = {
        'immediate': '立即執行',
        'once': '一次執行',
        'minute': '每分鐘',
        'daily': '每日執行',
        'weekly': '每週執行',
        'monthly': '每月執行'
    };
    
    return typeMap[schedule.schedule_type] || schedule.schedule_type;
}

// 獲取狀態顯示 HTML
function getStatusHtml(schedule) {
    let statusClass = '';
    let statusText = '';
    
    switch (schedule.status) {
        case 'pending':
            statusClass = 'text-info';
            statusText = '等待執行';
            break;
        case 'running':
            statusClass = 'text-primary';
            statusText = '<div class="spinner-border spinner-border-sm" role="status"></div> 執行中';
            break;
        case 'completed':
            statusClass = 'text-success';
            statusText = '已完成';
            break;
        case 'failed':
            statusClass = 'text-danger';
            statusText = '執行失敗';
            if (schedule.error_message) {
                statusText += ` <i class="fas fa-info-circle" title="${schedule.error_message}"></i>`;
            }
            break;
        default:
            statusClass = 'text-secondary';
            statusText = schedule.status;
    }
    
    const activeStatus = schedule.is_active ? 
        '<span class="badge bg-success ms-2">啟用</span>' : 
        '<span class="badge bg-secondary ms-2">停用</span>';
    
    return `<span class="${statusClass}">${statusText}</span>${activeStatus}`;
}

// 獲取操作按鈕 HTML
function getActionButtons(schedule) {
    // 排程操作按鈕
    const toggleButton = schedule.is_active ? 
        `<button class="btn btn-sm btn-outline-secondary me-1" onclick="toggleScheduleStatus('${schedule.id}', false)">停用</button>` : 
        `<button class="btn btn-sm btn-outline-success me-1" onclick="toggleScheduleStatus('${schedule.id}', true)">啟用</button>`;
    
    const editButton = `<button class="btn btn-sm btn-outline-primary me-1" onclick="editSchedule('${schedule.id}')">修改</button>`;
    const deleteButton = `<button class="btn btn-sm btn-outline-danger me-1" onclick="deleteSchedule('${schedule.id}')">刪除</button>`;
    
    // 任務操作按鈕
    let taskButtons = '';
    if (schedule.status === 'running') {
        taskButtons = `
            <button class="btn btn-sm btn-warning me-1" onclick="pauseSchedule('${schedule.id}')">暫停</button>
            <button class="btn btn-sm btn-danger me-1" onclick="stopSchedule('${schedule.id}')">停止</button>
        `;
    } else if (schedule.status === 'failed') {
        taskButtons = `
            <button class="btn btn-sm btn-primary me-1" onclick="retrySchedule('${schedule.id}')">重試</button>
        `;
    }
    
    // 日誌按鈕
    const logButton = `<button class="btn btn-sm btn-info" onclick="viewScheduleLog('${schedule.id}')">日誌</button>`;
    
    return `
        <div class="d-flex flex-wrap">
            ${toggleButton}
            ${editButton}
            ${deleteButton}
            ${taskButtons}
            ${logButton}
        </div>
    `;
}

// 格式化日期時間
function formatDateTime(date) {
    if (!date || isNaN(date.getTime())) return '無效日期';
    
    const options = { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit',
        hour: '2-digit', 
        minute: '2-digit'
    };
    
    return date.toLocaleString('zh-TW', options);
}

// 切換排程啟用狀態
async function toggleScheduleStatus(scheduleId, isActive) {
    try {
        const response = await fetch(`${window.location.origin}/schedule/api/schedule/${scheduleId}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification(
                '操作成功', 
                `排程已${isActive ? '啟用' : '停用'}`,
                isActive ? 'success' : 'info'
            );
            // 重新加載排程列表
            loadSchedulesList();
        } else {
            showNotification('操作失敗', result.message, 'error');
        }
    } catch (error) {
        console.error('切換排程狀態時發生錯誤:', error);
        showNotification('操作失敗', '發生未知錯誤', 'error');
    }
}

// 刪除排程
async function deleteSchedule(scheduleId) {
    console.log('刪除排程被調用，ID:', scheduleId);

    if (!confirm('確定要刪除此排程嗎？此操作無法恢復。')) {
        console.log('用戶取消刪除操作');
        return;
    }

    try {
        console.log('發送刪除請求到:', `${window.location.origin}/schedule/api/schedule/${scheduleId}`);

        const response = await fetch(`${window.location.origin}/schedule/api/schedule/${scheduleId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('刪除響應狀態:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('刪除響應結果:', result);

        if (result.status === 'success') {
            showNotification('操作成功', '排程已刪除', 'success');
            // 重新加載排程列表
            await loadSchedulesList();
        } else {
            showNotification('操作失敗', result.message || '刪除失敗', 'error');
        }
    } catch (error) {
        console.error('刪除排程時發生錯誤:', error);
        showNotification('操作失敗', `發生錯誤: ${error.message}`, 'error');
    }
}

// 修改排程
function editSchedule(scheduleId) {
    // 這裡實現編輯排程的邏輯
    alert('編輯排程功能即將推出');
}

// 查看排程日誌
function viewScheduleLog(scheduleId) {
    // 這裡實現查看排程日誌的邏輯
    alert('查看日誌功能即將推出');
}

// 切換模態框顯示
function toggleModal(modalId) {
    console.log('開始執行 toggleModal 函數，modalId:', modalId);
    const modal = modals[modalId];
    
    if (!modal) {
        console.error('找不到模態框對象:', modalId);
        return;
    }
    
    console.log('獲取到 modal 對象:', modal);
    const modalElement = document.getElementById(modalId);
    console.log('模態框 DOM 元素:', modalElement);
    const isVisible = modalElement.classList.contains('show');
    console.log('模態框是否可見:', isVisible);
    
    if (isVisible) {
        console.log('正在隱藏模態框');
        modal.hide();
    } else {
        console.log('正在顯示模態框');
        // 如果是腳本管理模態框
        if (modalId === 'scriptModal') {
            console.log('腳本管理模態框處理');
            const scriptListView = document.getElementById('scriptListView');
            console.log('scriptListView 元素:', scriptListView);
            const uploadView = document.getElementById('uploadView');
            console.log('uploadView 元素:', uploadView);
            
            if (scriptListView && uploadView) {
                console.log('顯示腳本列表，隱藏上傳視圖');
                // 顯示腳本列表，隱藏上傳視圖
                scriptListView.style.display = 'block';
                uploadView.style.display = 'none';
                
                // 加載腳本列表
                console.log('加載腳本列表');
                loadScriptsList();
            } else {
                console.error('找不到 scriptListView 或 uploadView 元素');
            }
        }
        
        console.log('執行 modal.show()');
        modal.show();
    }
}

// 初始化按鈕效果
function initializeButtonEffects() {
    // 為所有按鈕添加懸停效果
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('mouseover', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(-1px)';
                this.style.transition = 'all 0.2s ease';
            }
        });
        
        button.addEventListener('mouseout', function() {
            this.style.transform = 'none';
        });
        
        // 添加點擊效果
        button.addEventListener('mousedown', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(1px)';
            }
        });
        
        button.addEventListener('mouseup', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(-1px)';
            }
        });
    });
}

// 處理文件拖放
function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    const dropZone = event.target.closest('.drop-zone');
    if (dropZone) {
        dropZone.classList.add('dragover');
    }
}

function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    const dropZone = event.target.closest('.drop-zone');
    if (dropZone) {
        dropZone.classList.remove('dragover');
    }
}

function handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const dropZone = event.target.closest('.drop-zone');
    if (dropZone) {
        dropZone.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    }
}

// 處理文件選擇
function handleFileSelect(file) {
    console.log('處理文件選擇:', file.name);
    
    // 基本驗證
    if (!file) {
        showError('請選擇文件');
        return;
    }
    
    // 檢查文件類型
    if (!file.name.toLowerCase().endsWith('.py')) {
        showError('請選擇 Python 腳本文件 (.py)');
        resetFileSelection();
        return;
    }
    
    // 檢查文件大小 (限制為 5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        showError('文件大小不能超過 5MB');
        resetFileSelection();
        return;
    }
    
    // 更新選中的文件
    selectedFile = file;
    
    // 更新拖放區域的視覺效果
    const dropZone = document.getElementById('dropZone');
    if (dropZone) {
        dropZone.innerHTML = `
            <div class="file-success-icon bounce-in">
                <i class="fas fa-check-circle fa-3x text-success"></i>
            </div>
            <div class="file-success-message fade-in">
                <h5 class="text-success mb-3">文件已選擇成功！</h5>
                <div class="file-details">
                    <i class="fas fa-file-code text-primary me-2"></i>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size ms-2">(${formatFileSize(file.size)})</span>
                </div>
            </div>
            <button type="button" class="btn btn-outline-secondary mt-3" onclick="resetFileSelection()">
                <i class="fas fa-redo"></i> 重新選擇
            </button>
        `;
        
        // 添加成功狀態的樣式
        dropZone.classList.add('file-selected');
    }
    
    // 更新步驟狀態
    updateStepStatus(1, 'completed');
    
    // 啟用下一步按鈕
    const nextBtn = document.getElementById('nextStepBtn');
    if (nextBtn) {
        nextBtn.disabled = false;
        nextBtn.classList.add('pulse-animation');
    }
    
    // 顯示成功通知
    showNotification('成功', '文件已選擇', 'success');
}

// 重置文件選擇
function resetFileSelection() {
    console.log('重置文件選擇');
    
    // 清除選中的文件
    selectedFile = null;
    
    // 清除文件輸入
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.value = '';
    }
    
    // 重置拖放區域
    const dropZone = document.getElementById('dropZone');
    if (dropZone) {
        dropZone.innerHTML = `
            <div class="upload-icon bounce-in">
                <i class="fas fa-cloud-upload-alt fa-3x"></i>
            </div>
            <div class="upload-text fade-in">
                <p class="mb-2">拖放 Python 腳本至此處</p>
                <p class="text-muted">或</p>
                <button type="button" class="btn btn-outline-primary mt-2" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-folder-open"></i> 選擇文件
                </button>
            </div>
            <div class="upload-hint mt-3">
                <p class="text-muted small">僅支援 .py 文件，最大 5MB</p>
            </div>
        `;
        
        // 移除所有狀態類
        dropZone.classList.remove('file-selected', 'dragover');
    }
    
    // 更新步驟狀態
    updateStepStatus(1, 'active');
    
    // 禁用下一步按鈕
    const nextBtn = document.getElementById('nextStepBtn');
    if (nextBtn) {
        nextBtn.disabled = true;
        nextBtn.classList.remove('pulse-animation');
    }
    
    // 重置其他相關狀態
    validationResult = null;
    requiredPackages = [];
}

// 顯示錯誤提示
function showError(message) {
    const dropZone = document.getElementById('dropZone');
    if (dropZone) {
        dropZone.innerHTML = `
            <div class="error-icon bounce-in">
                <i class="fas fa-times-circle fa-3x text-danger"></i>
            </div>
            <div class="error-message fade-in">
                <p class="text-danger mb-3">${message}</p>
                <button type="button" class="btn btn-outline-primary" onclick="resetFileSelection()">
                    <i class="fas fa-redo"></i> 重新選擇
                </button>
            </div>
        `;
        dropZone.classList.add('error');
    }
    
    // 更新步驟狀態
    updateStepStatus(1, 'error');
    
    // 顯示錯誤通知
    showNotification('錯誤', message, 'error');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 更新步驟狀態
function updateStepStatus(stepNumber, status) {
    console.log(`更新步驟 ${stepNumber} 狀態為 ${status}`);
    
    const indicator = document.querySelector(`.step-indicator:nth-child(${stepNumber})`);
    const title = document.querySelector(`.step-title:nth-child(${stepNumber})`);
    
    if (indicator) {
        // 移除所有狀態類
        indicator.classList.remove('active', 'completed', 'error');
        
        // 添加新狀態
        if (status && status !== '') {
            switch (status) {
                case 'active':
                    indicator.classList.add('active');
                    indicator.innerHTML = stepNumber;
                    break;
                case 'completed':
                    indicator.classList.add('completed');
                    indicator.innerHTML = '<i class="fas fa-check"></i>';
                    // 添加完成動畫
                    indicator.style.animation = 'bounceIn 0.5s';
                    setTimeout(() => {
                        indicator.style.animation = '';
                    }, 500);
                    break;
                case 'error':
                    indicator.classList.add('error');
                    indicator.innerHTML = '<i class="fas fa-times"></i>';
                    break;
            }
        }
    }
    
    if (title && status && status !== '') {
        title.classList.remove('active', 'completed', 'error');
        title.classList.add(status);
    }
}

// 驗證腳本
async function validateScript(file) {
    console.log('開始驗證腳本:', file.name);
    const validationDetails = document.getElementById('validationDetails');
    const nextBtn = document.getElementById('nextStepBtn');
    
    if (!validationDetails) {
        console.error('找不到驗證詳情容器');
        return;
    }
    
    try {
        // 更新 UI 顯示驗證中
        if (nextBtn) {
            nextBtn.disabled = true;
            nextBtn.innerHTML = `
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                驗證中...
            `;
        }
        
        validationDetails.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">驗證中...</span>
                </div>
                <p class="mb-0">正在驗證 Python 腳本，請稍候...</p>
            </div>
        `;
    
    const formData = new FormData();
        formData.append('file', file);
        
        console.log('發送驗證請求到伺服器');
        
        try {
            const response = await fetch('/script/validate', {
            method: 'POST',
            body: formData
        });
            
            console.log('收到伺服器回應', response.status, response.statusText);
            
            if (!response.ok) {
                let errorMessage = '驗證過程發生錯誤';
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorMessage;
                } catch (jsonError) {
                    console.error('解析錯誤回應失敗:', jsonError);
                    // 如果不是 JSON，嘗試獲取文本
                    try {
                        const errorText = await response.text();
                        console.error('錯誤回應文本:', errorText);
                        errorMessage = '伺服器回應非預期格式，請檢查控制台獲取詳情';
                    } catch (textError) {
                        console.error('獲取錯誤回應文本失敗:', textError);
                    }
                }
                throw new Error(errorMessage);
            }
        
        const result = await response.json();
            console.log('驗證結果:', result);

            // 更新驗證結果 UI
            let validationContent = '<div class="validation-results">';
            
            // 嚴重錯誤 (紅色提示)
            if (result.errors && result.errors.length > 0) {
                validationContent += `
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">
                            <i class="fas fa-times-circle me-2"></i>
                            語法錯誤
                        </h6>
                        <ul class="list-unstyled mb-0">
                            ${result.errors.map(error => `
                                <li class="mt-2">
                                    <i class="fas fa-exclamation-circle text-danger me-2"></i>
                                    <span>${error}</span>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }

            // 缺失的依賴包 (藍色提示)
            if (result.missing_packages && result.missing_packages.length > 0) {
                validationContent += `
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            缺少依賴包
                        </h6>
                        <ul class="list-unstyled mb-0">
                            ${result.missing_packages.map(pkg => `
                                <li class="mt-2">
                                    <i class="fas fa-box text-info me-2"></i>
                                    <span>${pkg}</span>
                                </li>
                            `).join('')}
                        </ul>
                        <div class="mt-3">
                            <small class="text-muted">您可以點擊"下一步"進入依賴安裝頁面</small>
                        </div>
                    </div>
                `;
            }

            // 警告信息 (黃色提示)
            if (result.warnings && result.warnings.length > 0) {
                validationContent += `
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            警告提示
                        </h6>
                        <ul class="list-unstyled mb-0">
                            ${result.warnings.map(warning => `
                                <li class="mt-2">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    <span>${warning}</span>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }

            // 無問題時的成功提示
            if (!result.has_severe_errors && 
                !result.has_missing_packages && 
                (!result.warnings || result.warnings.length === 0)) {
                validationContent += `
                    <div class="alert alert-success">
                        <h6 class="alert-heading">
                            <i class="fas fa-check-circle me-2"></i>
                            驗證成功
                        </h6>
                        <p class="mb-0">腳本通過驗證，沒有發現任何問題</p>
                    </div>
                `;
            }

            validationContent += '</div>';
            validationDetails.innerHTML = validationContent;

            // 儲存所需的依賴包
            if (result.required_packages && result.required_packages.length > 0) {
                requiredPackages = result.required_packages.map(pkg => {
                    return {
                        name: pkg,
                        status: 'missing'
                    };
                });
        } else {
                requiredPackages = [];
            }

            // 更新按鈕狀態
            // 只有嚴重錯誤時禁用下一步，依賴缺失與警告可以繼續
            const hasErrors = result.has_severe_errors === true;
            if (nextBtn) {
                nextBtn.disabled = hasErrors;
                nextBtn.innerHTML = '下一步 <i class="fas fa-arrow-right"></i>';
                
                // 嚴重錯誤時不高亮，依賴缺失或成功時高亮下一步按鈕
                if (!hasErrors) {
                    nextBtn.classList.add('pulse-animation');
                } else {
                    nextBtn.classList.remove('pulse-animation');
                }
            }

            // 更新驗證狀態
            validationResult = {
                status: hasErrors ? 'error' : 
                       (result.has_missing_packages ? 'missing_packages' : 
                       (result.warnings && result.warnings.length > 0 ? 'warning' : 'success')),
                errors: result.errors || [],
                warnings: result.warnings || [],
                missing_packages: result.missing_packages || [],
                required_packages: result.required_packages || [],
                has_severe_errors: result.has_severe_errors,
                has_missing_packages: result.has_missing_packages
            };

            // 更新步驟狀態
            let stepStatus = 'completed';
            if (validationResult.has_severe_errors) {
                stepStatus = 'error';
            } else if (validationResult.has_missing_packages) {
                stepStatus = 'warning';  // 使用警告狀態表示缺少依賴
            }
            updateStepStatus(2, stepStatus);
            
            // 顯示通知
            let notificationType, notificationTitle, notificationMessage;
            
            if (validationResult.has_severe_errors) {
                notificationType = 'error';
                notificationTitle = '驗證失敗';
                notificationMessage = '腳本包含語法錯誤，無法繼續';
            } else if (validationResult.has_missing_packages) {
                notificationType = 'info';
                notificationTitle = '需要安裝依賴';
                notificationMessage = '腳本需要安裝依賴包才能運行';
            } else if (validationResult.warnings && validationResult.warnings.length > 0) {
                notificationType = 'warning';
                notificationTitle = '驗證通過，但有警告';
                notificationMessage = '腳本可以運行，但存在潛在問題';
            } else {
                notificationType = 'success';
                notificationTitle = '驗證成功';
                notificationMessage = '腳本通過驗證，沒有發現任何問題';
            }
            
            showNotification(notificationTitle, notificationMessage, notificationType);
        } catch (fetchError) {
            throw fetchError;
        }

    } catch (error) {
        console.error('驗證錯誤:', error);
        validationDetails.innerHTML = `
            <div class="alert alert-danger mb-0">
                <h6 class="alert-heading">
                    <i class="fas fa-times-circle me-2"></i>
                    驗證失敗
                </h6>
                <p class="mb-3">${error.message || '無法驗證腳本，請稍後再試'}</p>
                <button type="button" class="btn btn-danger" onclick="validateScript(selectedFile)">
                    <i class="fas fa-redo me-2"></i>重試
                </button>
            </div>
        `;
        
        // 標記驗證狀態為失敗
        validationResult = {
            status: 'error',
            errors: [error.message || '驗證過程發生錯誤'],
            warnings: [],
            missing_packages: [],
            required_packages: [],
            has_severe_errors: true,
            has_missing_packages: false
        };
        
        // 更新步驟狀態
        updateStepStatus(2, 'error');
        
        if (nextBtn) {
            nextBtn.disabled = true;
            nextBtn.innerHTML = '下一步 <i class="fas fa-arrow-right"></i>';
            nextBtn.classList.remove('pulse-animation');
        }
        
        // 顯示錯誤通知
        showNotification('驗證出錯', error.message || '無法驗證腳本，請稍後再試', 'error');
    }
}

// 顯示驗證結果
function showValidationResult(result) {
    console.log('顯示驗證結果:', result);
    const validationDiv = document.getElementById('validationDetails');
    if (!validationDiv) return;
    
    let html = '<div class="validation-results">';
    
    // 顯示錯誤
    if (result.errors && result.errors.length > 0) {
        html += '<div class="alert alert-danger"><h5>錯誤：</h5><ul>';
        result.errors.forEach(error => {
            html += `<li><i class="fas fa-times-circle"></i> ${error}</li>`;
        });
        html += '</ul></div>';
    }
    
    // 顯示警告
    if (result.warnings && result.warnings.length > 0) {
        html += '<div class="alert alert-warning"><h5>警告：</h5><ul>';
        result.warnings.forEach(warning => {
            html += `<li><i class="fas fa-exclamation-triangle"></i> ${warning}</li>`;
        });
        html += '</ul></div>';
    }
    
    // 顯示依賴包
    if (result.required_packages && result.required_packages.length > 0) {
        html += '<div class="alert alert-info"><h5>需要的依賴包：</h5><ul>';
        result.required_packages.forEach(pkg => {
            html += `<li><i class="fas fa-box"></i> ${pkg}</li>`;
        });
        html += '</ul></div>';
    }
    
    html += '</div>';
    validationDiv.innerHTML = html;
    validationDiv.style.display = 'block';
}

// 顯示依賴包信息
function showDependenciesInfo() {
    console.log('顯示依賴包信息');
    const dependencyInfo = document.getElementById('dependencyInfo');
    
    if (!dependencyInfo) {
        console.error('找不到依賴信息容器');
        return;
    }
    
    if (!requiredPackages || requiredPackages.length === 0) {
        // 沒有依賴需要安裝
        dependencyInfo.innerHTML = `
            <div class="alert alert-success">
                <h6 class="alert-heading">
                    <i class="fas fa-check-circle me-2"></i>
                    無需安裝依賴
                </h6>
                <p class="mb-0">此腳本不需要額外的依賴包，可以直接使用</p>
                </div>
        `;
        
        // 啟用下一步按鈕
        enableNextButton();
        return;
    }
    
    // 依賴包列表
    const missingDeps = requiredPackages.filter(pkg => pkg.status === 'missing');
    const installedDeps = requiredPackages.filter(pkg => pkg.status === 'installed');
    
    let html = '<div class="dependency-info">';
    
    // 顯示缺失的依賴包
    if (missingDeps.length > 0) {
        html += `
            <div class="alert alert-info">
                <h6 class="alert-heading">
                    <i class="fas fa-download me-2"></i>
                    待安裝的依賴包
                </h6>
                <div class="d-flex justify-content-end mb-3">
                    <button class="btn btn-primary btn-sm install-all-btn">
                        <i class="fas fa-download me-1"></i>
                        一鍵安裝全部
                    </button>
                            </div>
                <ul class="list-group mt-3">
        `;
        
        missingDeps.forEach(pkg => {
            html += `
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-box text-info me-2"></i>
                        <span>${pkg.name}</span>
                            </div>
                    <div>
                        <button class="btn btn-sm btn-primary install-pkg-btn" data-pkg="${pkg.name}">
                            <i class="fas fa-download me-1"></i>
                            安裝
                        </button>
                    </div>
                </li>
            `;
        });
        
        html += `
                </ul>
                <div class="form-check mt-3">
                    <input class="form-check-input" type="checkbox" id="forceUpload">
                    <label class="form-check-label" for="forceUpload">
                        強制繼續上傳（即使有未安裝的依賴包）
                    </label>
                </div>
            </div>
        `;
    }
    
    // 顯示已安裝的依賴包
    if (installedDeps.length > 0) {
        html += `
            <div class="alert alert-success">
                <h6 class="alert-heading">
                    <i class="fas fa-check-circle me-2"></i>
                    已安裝的依賴包
                </h6>
                <ul class="list-group mt-3">
        `;
        
        installedDeps.forEach(pkg => {
            html += `
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-box text-success me-2"></i>
                        <span>${pkg.name}</span>
                    </div>
                    <div>
                        <span class="badge bg-success">已安裝</span>
                    </div>
                                        </li>
            `;
        });
        
        html += `
                                </ul>
                            </div>
        `;
    }
    
    // 如果所有依賴都已安裝，顯示完成提示
    if (missingDeps.length === 0 && installedDeps.length > 0) {
        html += `
            <div class="alert alert-success mt-3">
                <h6 class="alert-heading">
                    <i class="fas fa-check-circle me-2"></i>
                    所有依賴已準備就緒
                </h6>
                <p class="mb-0">所有必要的依賴包都已安裝完成，您可以繼續下一步</p>
                    </div>
        `;
        
        // 啟用下一步按鈕
        setTimeout(() => enableNextButton(), 500);
    }
    
    // 如果沒有要顯示的依賴包（理論上不會發生）
    if (missingDeps.length === 0 && installedDeps.length === 0) {
        html += `
            <div class="alert alert-warning">
                <h6 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    依賴包信息不完整
                </h6>
                <p class="mb-0">未能獲取完整的依賴包信息，您可以選擇繼續或返回重新檢查</p>
                </div>
        `;
    }
    
    html += '</div>';
    dependencyInfo.innerHTML = html;
    
    // 綁定安裝按鈕事件
    document.querySelectorAll('.install-pkg-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const pkgName = this.getAttribute('data-pkg');
            installPackage(pkgName, this);
        });
    });
    
    // 綁定一鍵安裝按鈕事件
    const installAllBtn = document.querySelector('.install-all-btn');
    if (installAllBtn && missingDeps.length > 0) {
        installAllBtn.addEventListener('click', function() {
            installAllPackages(missingDeps);
        });
    }
    
    // 綁定強制上傳複選框事件
    const forceUploadCheckbox = document.getElementById('forceUpload');
    if (forceUploadCheckbox) {
        forceUploadCheckbox.addEventListener('change', function() {
            updateNextButtonState(this.checked);
        });
        
        // 初始狀態下，如果有缺失的依賴包且未勾選強制上傳則禁用下一步
        updateNextButtonState(forceUploadCheckbox.checked);
    }
    
    // 檢查是否所有依賴都已安裝
    checkAllDependenciesInstalled();
}

// 更新「下一步」按鈕狀態
function updateNextButtonState(forceEnabled = false) {
    const nextBtn = document.getElementById('nextStepBtn');
    if (!nextBtn) return;
    
    const missingDeps = requiredPackages.filter(pkg => pkg.status === 'missing');
    const shouldEnable = missingDeps.length === 0 || forceEnabled;
    
    nextBtn.disabled = !shouldEnable;
    
    if (shouldEnable) {
        nextBtn.classList.add('pulse-animation');
    } else {
        nextBtn.classList.remove('pulse-animation');
    }
}

// 啟用「下一步」按鈕
function enableNextButton() {
    const nextBtn = document.getElementById('nextStepBtn');
    if (nextBtn) {
        nextBtn.disabled = false;
        nextBtn.classList.add('pulse-animation');
    }
}

// 檢查所有依賴是否都已安裝
function checkAllDependenciesInstalled() {
    if (!requiredPackages || requiredPackages.length === 0) return true;
    
    const missingDeps = requiredPackages.filter(pkg => pkg.status === 'missing');
    const allInstalled = missingDeps.length === 0;
    
    if (allInstalled) {
        // 所有依賴都已安裝，啟用下一步按鈕
        enableNextButton();
        
        // 顯示成功通知
        showNotification('安裝完成', '所有依賴包已安裝完成，可以進入下一步', 'success');
    }
    
    return allInstalled;
}

// 安裝所有依賴包
async function installAllPackages(packages) {
    if (!packages || packages.length === 0) return;
    
    // 禁用所有安裝按鈕
    document.querySelectorAll('.install-pkg-btn, .install-all-btn').forEach(btn => {
        btn.disabled = true;
    });
    
    // 更新一鍵安裝按鈕狀態
    const installAllBtn = document.querySelector('.install-all-btn');
    if (installAllBtn) {
        installAllBtn.innerHTML = `
            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
            安裝中...
        `;
    }
    
    let successCount = 0;
    let failCount = 0;
    
    for (const pkg of packages) {
        try {
            // 找到對應的按鈕
            const pkgBtn = document.querySelector(`.install-pkg-btn[data-pkg="${pkg.name}"]`);
            
            // 更新按鈕狀態
            if (pkgBtn) {
                pkgBtn.innerHTML = `
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    安裝中...
                `;
            }
            
            // 發送安裝請求
            const response = await fetch('/script/install-dependencies', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    script_name: pkg.name
                })
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                // 安裝成功
                successCount++;
                
                // 更新依賴包狀態
                const pkgIndex = requiredPackages.findIndex(p => p.name === pkg.name);
                if (pkgIndex !== -1) {
                    requiredPackages[pkgIndex].status = 'installed';
                }
                
                // 更新按鈕狀態
                if (pkgBtn) {
                    pkgBtn.innerHTML = `
                        <i class="fas fa-check me-1"></i>
                        已安裝
                    `;
                    pkgBtn.classList.remove('btn-primary');
                    pkgBtn.classList.add('btn-success');
                    pkgBtn.disabled = true;
                }
            } else {
                failCount++;
                throw new Error(result.message || '安裝失敗');
            }
        } catch (error) {
            console.error(`安裝 ${pkg.name} 時發生錯誤:`, error);
            failCount++;
            
            // 更新按鈕狀態
            const pkgBtn = document.querySelector(`.install-pkg-btn[data-pkg="${pkg.name}"]`);
            if (pkgBtn) {
                pkgBtn.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    重試
                `;
                pkgBtn.disabled = false;
            }
        }
    }
    
    // 更新一鍵安裝按鈕狀態
    if (installAllBtn) {
        if (failCount === 0) {
            installAllBtn.innerHTML = `
                <i class="fas fa-check me-1"></i>
                全部安裝完成
            `;
            installAllBtn.classList.remove('btn-primary');
            installAllBtn.classList.add('btn-success');
            installAllBtn.disabled = true;
        } else {
            installAllBtn.innerHTML = `
                <i class="fas fa-download me-1"></i>
                重試安裝
            `;
            installAllBtn.disabled = false;
        }
    }
    
    // 顯示安裝結果通知
    if (failCount === 0) {
        showNotification('安裝成功', `所有 ${successCount} 個依賴包已成功安裝`, 'success');
    } else {
        showNotification('安裝部分完成', `${successCount} 個已安裝, ${failCount} 個失敗`, 'warning');
    }
    
    // 重新檢查依賴安裝狀態並更新 UI
    setTimeout(() => {
        // 重新渲染依賴信息
        showDependenciesInfo();
        
        // 檢查是否所有依賴都已安裝
        checkAllDependenciesInstalled();
    }, 500);
}

// 安裝依賴包
async function installPackage(packageName, buttonElement) {
    if (!packageName || !buttonElement) {
        console.error('缺少包名或按鈕元素');
        return;
    }
    
    try {
        // 更新按鈕狀態
        const originalInnerHTML = buttonElement.innerHTML;
        buttonElement.disabled = true;
        buttonElement.innerHTML = `
            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
            安裝中...
        `;
        
        // 發送安裝請求
        const response = await fetch('/script/install-dependencies', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                script_name: packageName
            })
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            // 安裝成功
            buttonElement.innerHTML = `
                <i class="fas fa-check me-1"></i>
                已安裝
            `;
            buttonElement.classList.remove('btn-primary');
            buttonElement.classList.add('btn-success');
            buttonElement.disabled = true;
            
            // 更新依賴包狀態
            const pkgIndex = requiredPackages.findIndex(p => p.name === packageName);
            if (pkgIndex !== -1) {
                requiredPackages[pkgIndex].status = 'installed';
            }
            
            // 檢查是否所有依賴包都已安裝
            const missingDeps = requiredPackages.filter(pkg => pkg.status === 'missing');
            if (missingDeps.length === 0) {
                // 所有依賴包已安裝，啟用下一步按鈕
                enableNextButton();
                
                // 更新依賴信息顯示
                setTimeout(() => showDependenciesInfo(), 500);
            }
            
            // 顯示成功通知
            showNotification('安裝成功', `依賴包 ${packageName} 已成功安裝`, 'success');
            
        } else {
            // 安裝失敗
            buttonElement.innerHTML = originalInnerHTML;
            buttonElement.disabled = false;
            throw new Error(result.message || '安裝失敗');
        }
    } catch (error) {
        console.error('安裝依賴包時發生錯誤:', error);
        // 恢復按鈕狀態
        buttonElement.innerHTML = `
            <i class="fas fa-download me-1"></i>
            重試
        `;
        buttonElement.disabled = false;
        
        // 顯示錯誤通知
        showNotification('安裝失敗', error.message || `無法安裝依賴包 ${packageName}`, 'error');
    }
}

// 上傳腳本
// 全局變量來保存用戶選擇的標籤
let userSelectedTag = '未分類';

async function uploadScript() {
    console.log('開始上傳腳本');

    // 獲取描述
    const description = document.getElementById('scriptDescription')?.value?.trim();

    // 多種方式獲取選中的標籤，確保不會丟失
    let selectedTag = '未分類';

    // 方法1: 直接查找選中的radio按鈕
    const selectedTagRadio = document.querySelector('input[name="scriptTag"]:checked');
    if (selectedTagRadio) {
        selectedTag = selectedTagRadio.value;
        console.log('方法1成功 - 從radio按鈕獲取:', selectedTag);
    }

    // 方法2: 如果方法1失敗，使用全局變量
    else if (userSelectedTag !== '未分類') {
        selectedTag = userSelectedTag;
        console.log('方法2成功 - 從全局變量獲取:', selectedTag);
    }

    // 方法3: 如果前兩種都失敗，遍歷所有radio按鈕
    else {
        const allRadios = document.querySelectorAll('input[name="scriptTag"]');
        for (let radio of allRadios) {
            if (radio.checked) {
                selectedTag = radio.value;
                console.log('方法3成功 - 遍歷找到:', selectedTag);
                break;
            }
        }
    }

    // 方法4: 最後的備份 - 從localStorage獲取
    if (selectedTag === '未分類') {
        const savedTag = localStorage.getItem('selectedScriptTag');
        if (savedTag && savedTag !== '未分類') {
            selectedTag = savedTag;
            console.log('方法4成功 - 從localStorage獲取:', selectedTag);
        }
    }

    console.log('=== 標籤選擇調試信息 ===');
    console.log('最終選中的標籤:', selectedTag);
    console.log('全局保存的標籤:', userSelectedTag);
    console.log('所有radio按鈕:', document.querySelectorAll('input[name="scriptTag"]'));
    console.log('========================');
    
    if (!description) {
        showNotification('提示', '請填寫腳本描述', 'warning');
        return;
    }
    
    // 保存原始內容以便出錯時恢復
    const uploadConfirm = document.getElementById('uploadConfirm');
    const originalContent = uploadConfirm.innerHTML;
    
    try {
        // 創建表單數據
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('description', description);
        formData.append('tags', selectedTag);
        
        // 顯示上傳中狀態
        uploadConfirm.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">上傳中...</span>
            </div>
                <p class="mb-0">正在上傳腳本，請稍候...</p>
        </div>
    `;
    
        // 發送上傳請求
        const response = await fetch('/script/', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification('成功', '腳本上傳成功', 'success');
            
            // 重置上傳狀態
            resetUploadState();
            
            // 重新加載腳本列表
            await loadScriptsList();
            
            // 切換回列表視圖
            const scriptListView = document.getElementById('scriptListView');
            const uploadView = document.getElementById('uploadView');
            if (scriptListView && uploadView) {
                uploadView.style.display = 'none';
                scriptListView.style.display = 'block';
            }
            
            // 關閉模態框
            try {
                if (typeof bootstrap !== 'undefined') {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('scriptModal'));
                    if (modal) {
                        modal.hide();
                    }
                }
            } catch (modalError) {
                console.error('關閉模態框時發生錯誤:', modalError);
            }
        } else {
            throw new Error(result.message || '上傳失敗');
        }
    } catch (error) {
        console.error('上傳腳本失敗:', error);
        
        // 檢查是否為網絡錯誤
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
            showNotification('錯誤', '網絡連接失敗，請檢查網絡狀態', 'error');
        } else {
            showNotification('錯誤', error.message || '上傳失敗', 'error');
        }
        
        // 延遲檢查文件是否已上傳成功
        setTimeout(async () => {
            try {
                console.log('最終檢查文件是否已上傳成功...');
                const response = await fetch('/script/');
                const result = await response.json();
        
                if (result.status === 'success') {
                    const uploadedFile = result.data.find(file => file.name === selectedFile.name);
                    
                    if (uploadedFile) {
                        console.log('最終確認：文件已成功上傳');
                        showNotification('提示', '雖然顯示了錯誤，但文件已成功上傳', 'info');
                        
                        // 重置上傳狀態
                        resetUploadState();
                        
                        // 重新加載腳本列表
                        await loadScriptsList();
                    }
                }
            } catch (checkError) {
                console.error('檢查文件上傳狀態時發生錯誤:', checkError);
            }
        }, 2000);
        
        // 恢復上傳確認區域
        uploadConfirm.innerHTML = originalContent;
    }
}

// 綁定上傳表單事件
function bindUploadFormEvents() {
    // 綁定字數計數器
    const description = document.getElementById('scriptDescription');
    const descriptionLength = document.getElementById('descriptionLength');
    
    if (description && descriptionLength) {
        description.addEventListener('input', function() {
            const length = this.value.length;
            descriptionLength.textContent = length;
            
            // 更新下一步按鈕狀態
            const nextBtn = document.getElementById('nextStepBtn');
            if (nextBtn) {
                nextBtn.disabled = length === 0;
            }
        });
    }
    
    // 綁定標籤選擇
    const tagButtons = document.querySelectorAll('.tag-btn');
    const selectedTagsContainer = document.getElementById('selectedTags');
    const selectedTags = new Set();
    
    tagButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const tag = this.getAttribute('data-tag');
            if (this.classList.contains('active')) {
                // 取消選擇
                this.classList.remove('active');
                selectedTags.delete(tag);
            } else {
                // 選擇標籤
                this.classList.add('active');
                selectedTags.add(tag);
            }
            
            // 更新已選標籤顯示
            if (selectedTagsContainer) {
                if (selectedTags.size > 0) {
                    selectedTagsContainer.innerHTML = `
                        <div class="text-muted small">
                            <i class="fas fa-tags me-1"></i>
                            已選擇：${Array.from(selectedTags).join('、')}
            </div>
        `;
                } else {
                    selectedTagsContainer.innerHTML = '';
                }
            }
        });
    });
}

// 顯示上傳表單
function showUploadForm() {
    console.log('顯示上傳確認表單');
    const uploadConfirm = document.getElementById('uploadConfirm');

    if (!uploadConfirm) {
        console.error('找不到上傳確認容器');
        return;
    }

    // 檢查是否已經有表單，避免重複創建導致狀態丟失
    if (uploadConfirm.querySelector('.card')) {
        console.log('上傳表單已存在，跳過重新創建');
        return;
    }
    
    uploadConfirm.innerHTML = `
        <div class="card shadow-sm border-0 mb-4">
            <div class="card-body p-4">
                <div class="mb-4">
                    <label for="scriptDescription" class="form-label fw-bold">
                        <i class="fas fa-pencil-alt me-2 text-primary"></i>腳本描述
                    </label>
                    <input type="text" class="form-control" id="scriptDescription" 
                           maxlength="20" placeholder="請輸入腳本描述（最多20字）">
                    <div class="form-text text-end">
                        還可以輸入<span id="charCount" class="fw-bold">20</span>個字
            </div>
            </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-tags me-2 text-primary"></i>選擇標籤
                    </label>
                    <div class="tag-options p-3 border rounded bg-light">
                        <div class="row">
                            <div class="col">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="scriptTag" value="網頁爬蟲" id="tag1">
                                    <label class="form-check-label" for="tag1">
                                        <i class="fas fa-spider me-1 text-secondary"></i>網頁爬蟲
                                    </label>
        </div>
                            </div>
                            <div class="col">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="scriptTag" value="檢核通知" id="tag2">
                                    <label class="form-check-label" for="tag2">
                                        <i class="fas fa-check-circle me-1 text-secondary"></i>檢核通知
                                    </label>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="scriptTag" value="報表生成" id="tag3">
                                    <label class="form-check-label" for="tag3">
                                        <i class="fas fa-file-alt me-1 text-secondary"></i>報表生成
                                    </label>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="scriptTag" value="檔案管理" id="tag4">
                                    <label class="form-check-label" for="tag4">
                                        <i class="fas fa-folder me-1 text-secondary"></i>檔案管理
                                    </label>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="scriptTag" value="備份還原" id="tag5">
                                    <label class="form-check-label" for="tag5">
                                        <i class="fas fa-sync-alt me-1 text-secondary"></i>備份還原
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 初始化字符計數器
    initializeDescriptionCounter();

    // 添加標籤選擇事件監聽器
    initializeTagSelection();
}

// 初始化標籤選擇事件監聽器
function initializeTagSelection() {
    // 延遲執行，確保DOM已完全渲染
    setTimeout(() => {
        const tagRadios = document.querySelectorAll('input[name="scriptTag"]');
        console.log('找到標籤radio按鈕數量:', tagRadios.length);

        // 先清除之前的事件監聽器（避免重複綁定）
        tagRadios.forEach(radio => {
            // 克隆節點來移除所有事件監聽器
            const newRadio = radio.cloneNode(true);
            radio.parentNode.replaceChild(newRadio, radio);
        });

        // 重新獲取更新後的radio按鈕
        const updatedTagRadios = document.querySelectorAll('input[name="scriptTag"]');

        updatedTagRadios.forEach(radio => {
            // 添加多種事件監聽器
            ['change', 'click', 'input'].forEach(eventType => {
                radio.addEventListener(eventType, function() {
                    if (this.checked) {
                        userSelectedTag = this.value;
                        console.log(`標籤選擇 (${eventType}):`, userSelectedTag);

                        // 立即保存到localStorage作為備份
                        localStorage.setItem('selectedScriptTag', userSelectedTag);

                        // 觸發自定義事件，通知其他組件
                        document.dispatchEvent(new CustomEvent('tagSelected', {
                            detail: { tag: userSelectedTag }
                        }));
                    }
                });
            });

            // 添加標籤點擊的視覺反饋
            radio.parentElement.addEventListener('click', function(e) {
                e.preventDefault();
                const radioInput = this.querySelector('input[type="radio"]');
                if (radioInput) {
                    // 先取消所有選中狀態
                    updatedTagRadios.forEach(r => r.checked = false);
                    // 設置當前選中
                    radioInput.checked = true;
                    userSelectedTag = radioInput.value;
                    localStorage.setItem('selectedScriptTag', userSelectedTag);
                    console.log('標籤點擊選擇:', userSelectedTag);

                    // 觸發change事件
                    radioInput.dispatchEvent(new Event('change'));
                }
            });
        });

        // 嘗試從localStorage恢復之前的選擇
        const savedTag = localStorage.getItem('selectedScriptTag');
        if (savedTag && savedTag !== '未分類') {
            userSelectedTag = savedTag;
            const savedRadio = document.querySelector(`input[name="scriptTag"][value="${savedTag}"]`);
            if (savedRadio) {
                savedRadio.checked = true;
                console.log('從localStorage恢復標籤選擇:', savedTag);
            }
        }

        console.log('標籤選擇事件監聽器已初始化，共', updatedTagRadios.length, '個標籤');
        console.log('當前選中標籤:', userSelectedTag);
    }, 200);
}

// 重置上傳表單
function resetUploadForm() {
    console.log('重置上傳表單');

    // 清空全局變量
    selectedFile = null;
    userSelectedTag = '未分類';

    // 清空localStorage
    localStorage.removeItem('selectedScriptTag');

    // 重置表單容器
    const uploadConfirm = document.getElementById('uploadConfirm');
    if (uploadConfirm) {
        uploadConfirm.innerHTML = '';
    }

    // 重置文件預覽
    const filePreview = document.getElementById('filePreview');
    if (filePreview) {
        filePreview.classList.add('d-none');
    }

    // 重置上傳區域提示
    const uploadPrompt = document.querySelector('.upload-prompt p');
    if (uploadPrompt) {
        uploadPrompt.innerHTML = '<i class="fas fa-cloud-upload-alt me-2"></i>拖拽文件到此處或點擊選擇文件';
    }
}

// 顯示文件預覽
function showFilePreview(file) {
    const preview = document.getElementById('filePreview');
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    preview.classList.remove('d-none');
    
    // 更新上傳區域提示
    const uploadPrompt = document.querySelector('.upload-prompt p');
    uploadPrompt.innerHTML = '<i class="fas fa-check-circle text-success"></i> 已選擇文件';
}

// 顯示文件信息表單
function showFileInfo() {
    const fileInfo = document.getElementById('fileInfo');
    fileInfo.classList.remove('d-none');
    
    // 重置表單
    document.getElementById('description').value = '';
    document.getElementById('author').value = '';
    document.getElementById('tags').value = '';
}

// 顯示腳本列表
async function loadScriptsList(callback) {
    try {
        // 使用重寫版 API 端點
        const response = await fetch('/script/');
        const data = await response.json();
        
        if (data.status === 'success') {
            // 更新全局腳本列表
            loadedScripts = data.data;
            
            // 更新腳本選擇器（排程創建用）
            const scriptSelect = document.getElementById('script-select');
            if (scriptSelect) {
                // 清空下拉選單，但保留第一個默認選項
                while (scriptSelect.options.length > 1) {
                    scriptSelect.remove(1);
                }
                
                // 填充腳本選項
                loadedScripts.forEach(script => {
                    const option = document.createElement('option');
                    option.value = script.name;
                    option.textContent = `${script.name} - ${script.description || '無描述'}`;
                    scriptSelect.appendChild(option);
                });
            }
            
            // 更新腳本列表 DOM
            const scriptsContainer = document.getElementById('scriptList');
            if (scriptsContainer) {
                if (loadedScripts.length === 0) {
                    scriptsContainer.innerHTML = `
                        <div class="text-center my-4">
                            <i class="fas fa-file-code fa-3x text-muted mb-3"></i>
                            <p class="lead">沒有可用的腳本</p>
                        </div>
                    `;
                } else {
                    scriptsContainer.innerHTML = '';
                    loadedScripts.forEach(script => {
                        const scriptElement = createScriptElement(script);
                        scriptsContainer.appendChild(scriptElement);
                    });
                }
            }
            
            // 如果有回調函數，執行它
            if (typeof callback === 'function') {
                callback(loadedScripts);
            }
        }
    } catch (error) {
        console.error('獲取腳本列表時發生錯誤:', error);
        
        const scriptsContainer = document.getElementById('scriptList');
        if (scriptsContainer) {
            scriptsContainer.innerHTML = `
                <div class="alert alert-danger text-center" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    載入腳本列表失敗，請稍後再試
                </div>
            `;
        }
    }
}

// 創建腳本元素
function createScriptElement(script) {
    const element = document.createElement('div');
    element.className = 'list-group-item list-group-item-action border-0 mb-2 shadow-sm rounded';
    
    // 確保描述有值
    const description = script.description || '無描述';
    
    // 處理標籤
    let tagDisplay = '未分類';
    let tagIcon = 'tag';

    // 處理不同格式的標籤數據
    if (script.tag) {
        // 單個標籤字段
        tagDisplay = script.tag;
    } else if (script.tags) {
        if (Array.isArray(script.tags) && script.tags.length > 0) {
            // 標籤數組
            tagDisplay = script.tags[0];
        } else if (typeof script.tags === 'string' && script.tags.trim() !== '') {
            // 標籤字符串（可能包含逗號分隔的多個標籤）
            const tagsArray = script.tags.split(',').map(tag => tag.trim()).filter(tag => tag !== '');
            if (tagsArray.length > 0) {
                tagDisplay = tagsArray[0];
            }
        }
    }

    // 腳本標籤處理完成
    
    // 根據標籤類型設置圖標
    switch(tagDisplay) {
        case '網頁爬蟲':
            tagIcon = 'spider';
            break;
        case '檢核通知':
            tagIcon = 'check-circle';
            break;
        case '報表生成':
            tagIcon = 'file-alt';
            break;
        case '檔案管理':
            tagIcon = 'folder';
            break;
        case '備份還原':
            tagIcon = 'sync-alt';
            break;
    }
    
    element.innerHTML = `
        <div class="d-flex w-100 justify-content-between align-items-center p-2">
            <div class="d-flex align-items-center flex-grow-1">
                <div class="script-tag me-3">
                    <span class="badge bg-primary fs-6 py-2 px-3">
                        <i class="fas fa-${tagIcon} me-1"></i>${tagDisplay}
                    </span>
                </div>
                <div class="script-info">
                    <h6 class="mb-1 fw-bold">
                        <i class="fas fa-file-code text-primary me-2"></i>${script.name}
                    </h6>
                    <p class="mb-0 text-muted small">${description}</p>
                </div>
            </div>
            <div>
                <button class="btn btn-sm btn-outline-danger rounded-pill" onclick="deleteScript('${script.name}')">
                    <i class="fas fa-trash"></i> 刪除
                </button>
            </div>
        </div>
    `;
    
    return element;
}

// 顯示上傳視圖
function showUploadView() {
    console.log('顯示上傳視圖');
    
    // 獲取元素
    const scriptListView = document.getElementById('scriptListView');
    const uploadView = document.getElementById('uploadView');
    
    // 顯示上傳視圖，隱藏腳本列表
    if (scriptListView) scriptListView.style.display = 'none';
    if (uploadView) uploadView.style.display = 'block';
    
    // 重置狀態
    resetUploadState();
    
    // 初始化步驟
    currentStep = 1;
    updateStepIndicators(1);
    
    // 顯示第一步內容
    const stepContents = document.querySelectorAll('.step-content');
    stepContents.forEach(content => {
        content.style.display = 'none';
        content.classList.remove('active');
    });
    
    const firstStep = document.querySelector('.step-content[data-step="1"]');
    if (firstStep) {
        firstStep.style.display = 'block';
        firstStep.classList.add('active');
    }
    
    // 更新按鈕狀態
    const prevBtn = document.getElementById('prevStepBtn');
    const nextBtn = document.getElementById('nextStepBtn');
    
    if (prevBtn) prevBtn.style.display = 'none';
    if (nextBtn) {
        nextBtn.disabled = true;
        nextBtn.innerHTML = '下一步 <i class="fas fa-arrow-right"></i>';
    }
    
    // 初始化拖放區域
    initializeDropZone();
}

// 初始化拖放區域
function initializeDropZone() {
    console.log('初始化拖放區域');
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    
    if (!dropZone || !fileInput) {
        console.error('找不到拖放區域或文件輸入元素');
        return;
    }
    
    // 清空之前的選擇
    fileInput.value = '';
    
    // 重置拖放區域的內容
    dropZone.innerHTML = `
        <div class="upload-icon bounce-in">
            <i class="fas fa-cloud-upload-alt fa-3x"></i>
                    </div>
        <div class="upload-text fade-in">
            <p class="mb-2">拖放 Python 腳本至此處</p>
            <p class="text-muted">或</p>
            <button type="button" class="btn btn-outline-primary mt-2" onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-folder-open"></i> 選擇文件
            </button>
            </div>
        <div class="upload-hint mt-3">
            <p class="text-muted small">僅支援 .py 文件，最大 5MB</p>
        </div>
    `;
    
    // 移除所有狀態類
    dropZone.classList.remove('dragover', 'file-selected', 'error');
    
    // 綁定事件
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    
    fileInput.addEventListener('change', (e) => {
        if (e.target.files && e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });
    
    console.log('拖放區域初始化完成');
}

// 重置上傳狀態
function resetUploadState() {
    console.log('重置上傳狀態');
    
    // 重置文件選擇
    resetFileSelection();
    
    // 重置步驟
    currentStep = 1;
    showStepContent(1);
    
    // 重置步驟指示器
    updateStepStatus(1, 'active');
    for (let i = 2; i <= 4; i++) {
        updateStepStatus(i, '');
    }
    
    // 重置驗證結果
    validationResult = null;
    const validationDetails = document.getElementById('validationDetails');
    if (validationDetails) {
        validationDetails.innerHTML = '';
    }
    
    // 重置依賴信息
    requiredPackages = [];
    const dependencyInfo = document.getElementById('dependencyInfo');
    if (dependencyInfo) {
        dependencyInfo.innerHTML = '';
    }
    
    // 重置上傳確認信息
    const uploadConfirm = document.getElementById('uploadConfirm');
    if (uploadConfirm) {
        uploadConfirm.innerHTML = '';
    }
    
    // 禁用下一步按鈕
    const nextBtn = document.getElementById('nextStepBtn');
    if (nextBtn) {
        nextBtn.disabled = true;
        nextBtn.classList.remove('pulse-animation');
    }
    
    // 更新當前步驟標題
    const stepTitle = document.querySelector('.current-step-title');
    if (stepTitle) {
        stepTitle.textContent = '選擇檔案';
    }
}

// 上傳成功後的處理
function handleUploadSuccess() {
    console.log('上傳成功');
    
    // 顯示成功通知
    showNotification('成功', '腳本上傳成功', 'success');
    
    // 重新加載腳本列表
    loadScripts();
    
    // 重置上傳狀態
    resetUploadState();
    
    // 切換回列表視圖
    document.getElementById('scriptListView').style.display = 'block';
    document.getElementById('uploadView').style.display = 'none';
}

// 返回列表視圖
function showListView() {
    console.log('返回列表視圖');
    document.getElementById('uploadView').style.display = 'none';
    document.getElementById('scriptListView').style.display = 'block';
    resetAllStates();
}

// 查看腳本詳情
async function viewScriptDetails(scriptName) {
    console.log('查看腳本詳情:', scriptName);
    try {
        const response = await fetch(`/script/${scriptName}/info`);
        const result = await response.json();
        
        if (result.status === 'success') {
            const script = result.data;
            
            // 創建並顯示模態框
            const modalContent = `
                <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                            <h5 class="modal-title">腳本詳情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                            <form id="scriptDetailsForm">
                            <div class="mb-3">
                                    <label class="form-label">腳本名稱</label>
                                    <input type="text" class="form-control" value="${script.name}" readonly>
                            </div>
                                
                            <div class="mb-3">
                                    <label class="form-label">描述</label>
                                    <textarea class="form-control" id="editDescription" rows="2" maxlength="50"
                                              placeholder="請簡要描述此腳本的主要功能（50字以內）">${script.description || ''}</textarea>
                                    <div class="form-text text-end">
                                        <span id="descriptionLength">${(script.description || '').length}</span>/50
                            </div>
                            </div>

                            <div class="mb-3">
                                    <label class="form-label">標籤</label>
                                    <div class="tag-options d-flex flex-wrap gap-2">
                                        <button type="button" class="btn btn-outline-primary tag-btn ${scriptHasTag(script, '網頁爬蟲') ? 'active' : ''}" data-tag="網頁爬蟲">
                                            <i class="fas fa-spider me-1"></i>網頁爬蟲
                                        </button>
                                        <button type="button" class="btn btn-outline-primary tag-btn ${scriptHasTag(script, '檢核通知') ? 'active' : ''}" data-tag="檢核通知">
                                            <i class="fas fa-check-circle me-1"></i>檢核通知
                                        </button>
                                        <button type="button" class="btn btn-outline-primary tag-btn ${scriptHasTag(script, '報表生成') ? 'active' : ''}" data-tag="報表生成">
                                            <i class="fas fa-file-alt me-1"></i>報表生成
                                        </button>
                                        <button type="button" class="btn btn-outline-primary tag-btn ${scriptHasTag(script, '檔案管理') ? 'active' : ''}" data-tag="檔案管理">
                                            <i class="fas fa-folder me-1"></i>檔案管理
                                        </button>
                                        <button type="button" class="btn btn-outline-primary tag-btn ${scriptHasTag(script, '備份還原') ? 'active' : ''}" data-tag="備份還原">
                                            <i class="fas fa-sync-alt me-1"></i>備份還原
                                        </button>
                            </div>
                                    <div id="selectedTags" class="mt-2 selected-tags"></div>
                            </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="saveScriptDetails('${script.name}')">儲存</button>
                </div>
            </div>
        </div>
    `;

            // 更新模態框內容
            const modalElement = document.getElementById('scriptDetailsModal');
            if (!modalElement) {
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.id = 'scriptDetailsModal';
                modal.setAttribute('tabindex', '-1');
                modal.innerHTML = modalContent;
    document.body.appendChild(modal);
                
                if (typeof bootstrap !== 'undefined') {
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                }
                
                bindScriptDetailsEvents();
            } else {
                modalElement.innerHTML = modalContent;
                if (typeof bootstrap !== 'undefined') {
                    const bsModal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
                    bsModal.show();
                }
                
                bindScriptDetailsEvents();
            }
        } else {
            showNotification('錯誤', '獲取腳本信息失敗', 'error');
        }
    } catch (error) {
        console.error('獲取腳本信息失敗:', error);
        showNotification('錯誤', '獲取腳本信息失敗', 'error');
    }
}

// 綁定腳本詳情相關事件
function bindScriptDetailsEvents() {
    // 描述字數計數
    const description = document.getElementById('editDescription');
    const descriptionLength = document.getElementById('descriptionLength');
    
    if (description && descriptionLength) {
        description.addEventListener('input', function() {
            const length = this.value.length;
            descriptionLength.textContent = length;
        });
    }
    
    // 標籤選擇
    const tagButtons = document.querySelectorAll('#scriptDetailsModal .tag-btn');
    tagButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            this.classList.toggle('active');
            updateSelectedTags();
        });
    });
    
    // 初始更新已選標籤
    updateSelectedTags();
}

// 更新已選標籤顯示
function updateSelectedTags() {
    const selectedTags = Array.from(document.querySelectorAll('#scriptDetailsModal .tag-btn.active'))
        .map(btn => btn.getAttribute('data-tag'));
    
    const selectedTagsContainer = document.getElementById('selectedTags');
    if (selectedTagsContainer) {
        if (selectedTags.length > 0) {
            selectedTagsContainer.innerHTML = `
                <div class="text-muted small">
                    <i class="fas fa-tags me-1"></i>
                    已選擇：${selectedTags.join('、')}
                </div>
            `;
        } else {
            selectedTagsContainer.innerHTML = '';
        }
    }
}

// 儲存腳本詳情
async function saveScriptDetails(scriptName) {
    const description = document.getElementById('editDescription').value.trim();
    const selectedTags = Array.from(document.querySelectorAll('#scriptDetailsModal .tag-btn.active'))
        .map(btn => btn.getAttribute('data-tag'));
    
    console.log('保存腳本詳情:', scriptName);
    console.log('描述:', description);
    console.log('標籤:', selectedTags);
    
    try {
        const response = await fetch(`/script/${scriptName}/meta`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                description: description,
                tags: selectedTags || []  // 確保即使沒有選擇標籤也傳送空數組
            })
        });
        
        const result = await response.json();
        console.log('更新結果:', result);
        
        if (result.status === 'success') {
            showNotification('成功', '腳本資訊已更新', 'success');
            
            // 關閉模態框
            if (typeof bootstrap !== 'undefined') {
                const modal = bootstrap.Modal.getInstance(document.getElementById('scriptDetailsModal'));
                if (modal) {
                    modal.hide();
                }
            }
            
            // 等待模態框完全關閉後再重新載入列表
            setTimeout(async () => {
            await loadScriptsList();
            }, 500);  // 增加延遲時間，確保元數據已更新
        } else {
            showNotification('錯誤', result.message || '更新失敗', 'error');
        }
    } catch (error) {
        console.error('更新腳本資訊失敗:', error);
        showNotification('錯誤', '更新腳本資訊失敗', 'error');
    }
}

// 刪除腳本
async function deleteScript(scriptName) {
    console.log('刪除腳本:', scriptName);
    if (!confirm(`確定要刪除腳本 "${scriptName}" 嗎？`)) {
        return;
    }
    
    try {
        const response = await fetch(`/script/${scriptName}`, {
            method: 'DELETE'
        });
        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification('成功', '腳本已刪除', 'success');
            await loadScriptsList();
        } else {
            showNotification('錯誤', result.message, 'error');
        }
    } catch (error) {
        console.error('刪除腳本失敗:', error);
        showNotification('錯誤', '刪除腳本失敗', 'error');
    }
}

// 顯示通知
function showNotification(title, message, type = 'info') {
    // 移除現有通知
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 創建新通知
    const notification = document.createElement('div');
    notification.className = 'notification fade-in';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        max-width: 350px;
        border-left: 4px solid ${STATUS_COLORS[type]};
    `;

    notification.innerHTML = `
        <div style="margin-bottom: 5px; color: ${STATUS_COLORS[type]}; font-weight: 600;">
            ${title}
        </div>
        <div style="color: #4B5563; font-size: 14px;">
            ${message}
        </div>
    `;

    document.body.appendChild(notification);

    // 3秒後自動消失
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// 切換加載動畫
function toggleLoading(show) {
    const loading = document.querySelector('.loading');
    if (loading) {
        loading.style.display = show ? 'flex' : 'none';
    }
}

// 初始化上傳表單
function initializeUploadForm() {
    console.log('初始化上傳表單');

    // 初始化拖放區域
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');

    if (dropZone) {
        // 拖放事件
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('dragleave', handleDragLeave);
        dropZone.addEventListener('drop', handleDrop);

        // 點擊事件
        dropZone.addEventListener('click', (e) => {
            // 如果點擊的是按鈕，不觸發文件選擇
            if (!e.target.closest('button')) {
                fileInput.click();
            }
        });

        // 初始化顯示
        resetFileSelection();
    }

    // 文件輸入事件
    if (fileInput) {
        fileInput.addEventListener('change', (e) => {
            if (e.target.files && e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });
    }

    // 初始化標籤選擇
    initializeTagSelection();
}

// 防抖函數
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 顯示步驟內容
function showStepContent(step) {
    console.log(`顯示步驟 ${step} 內容`);
    
    // 隱藏所有步驟內容
    document.querySelectorAll('.step-content').forEach(content => {
        content.style.display = 'none';
    });
    
    // 顯示當前步驟內容
    const currentContent = document.querySelector(`.step-content[data-step="${step}"]`);
    if (currentContent) {
        currentContent.style.display = 'block';
        console.log(`成功顯示步驟 ${step} 內容`);
    } else {
        console.error(`找不到步驟 ${step} 的內容元素`);
    }
    
    // 更新按鈕狀態
    updateStepControls(step);
    
    // 更新步驟指示器
    updateStepIndicators(step);
    
    // 根據步驟執行特定操作
    switch (step) {
        case 1:
            console.log('步驟1：選擇檔案');
            break;
            
        case 2:
            console.log('步驟2：驗證檢查');
            if (selectedFile && !validationResult) {
                validateScript(selectedFile);
            }
            break;
            
        case 3:
            console.log('步驟3：依賴安裝');
            if (validationResult && validationResult.required_packages) {
                requiredPackages = validationResult.required_packages.map(pkg => ({
                    name: pkg,
                    status: 'missing'
                }));
                showDependenciesInfo();
            } else {
                // 沒有依賴包需要安裝，直接更新按鈕狀態
                enableNextButton();
            }
            break;
            
        case 4:
            console.log('步驟4：確認上傳');
            showUploadConfirmStep();
            break;
    }
}

// 更新步驟指示器
function updateStepIndicators(step) {
    console.log('更新步驟指示器:', step);
    
    const steps = document.querySelectorAll('.step-indicator');
    const stepTitles = document.querySelectorAll('.step-title');
    
    // 更新步驟標題
    const titles = ['選擇檔案', '驗證檢查', '依賴安裝', '確認上傳'];
    const currentStepTitle = document.querySelector('.current-step-title');
    if (currentStepTitle) {
        currentStepTitle.textContent = titles[step - 1];
    }
    
    // 更新每個步驟的狀態
    steps.forEach((indicator, index) => {
        const stepNumber = index + 1;
        const isCurrentStep = stepNumber === step;
        const isCompletedStep = stepNumber < step;
        
        // 移除所有狀態類
        indicator.classList.remove('active', 'completed', 'error');
        if (stepTitles[index]) {
            stepTitles[index].classList.remove('active', 'completed', 'error');
        }
        
        // 設置新狀態
        if (isCurrentStep) {
            indicator.classList.add('active');
            indicator.innerHTML = stepNumber;
            if (stepTitles[index]) {
                stepTitles[index].classList.add('active');
            }
        } else if (isCompletedStep) {
            indicator.classList.add('completed');
            indicator.innerHTML = '<i class="fas fa-check"></i>';
            if (stepTitles[index]) {
                stepTitles[index].classList.add('completed');
            }
        } else {
            indicator.innerHTML = stepNumber;
        }
    });
}

// 下一步
function nextStep() {
    console.log('嘗試進入下一步，當前步驟:', currentStep);
    
    if (!canProceedToNextStep()) {
        let message = '';
        switch (currentStep) {
            case 1:
                message = '請選擇一個Python腳本文件';
                break;
            case 2:
                message = '請等待腳本驗證完成';
                break;
            case 3:
                message = '請安裝所需依賴或勾選強制上傳';
                break;
            case 4:
                message = '請填寫腳本描述';
                break;
            default:
                message = '請完成當前步驟的所有必要操作';
        }
        showNotification('提示', message, 'warning');
        return;
    }
    
    if (currentStep < 4) {
        // 更新當前步驟狀態為已完成
        if (currentStep === 1) {
            updateStepStatus(1, 'completed');
        } else if (currentStep === 2 && validationResult) {
            updateStepStatus(2, validationResult.status === 'error' ? 'error' : 'completed');
        } else if (currentStep === 3) {
            updateStepStatus(3, 'completed');
        }
        
        // 更新步驟
        currentStep++;
        console.log(`進入步驟 ${currentStep}`);
        
        // 標記新步驟為活動狀態
        updateStepStatus(currentStep, 'active');
        
        // 更新UI
        showStepContent(currentStep);
    } else {
        // 最後一步，執行上傳
        uploadScript();
    }
}

// 根據狀態檢查是否可以進入下一步
function canProceedToNextStep() {
    switch(currentStep) {
        case 1:
            // 第一步：選擇檔案
            return selectedFile !== null;
            
        case 2:
            // 第二步：驗證檢查
            // 如果有嚴重錯誤則不能進入下一步，但如果只是缺少依賴包或有警告則可以
            return validationResult && !validationResult.has_severe_errors;
            
        case 3:
            // 第三步：依賴安裝
            // 檢查是否有依賴包需要安裝
            if (!requiredPackages || requiredPackages.length === 0) {
                // 沒有依賴包需要安裝
                return true;
            }
            
            // 檢查是否所有依賴包都已安裝
            const missingDeps = requiredPackages.filter(pkg => pkg.status === 'missing');
            
            // 如果沒有缺失的依賴包，或者勾選了強制上傳選項，則允許進入下一步
            return missingDeps.length === 0 || document.getElementById('forceUpload')?.checked === true;
            
        case 4:
            // 第四步：確認上傳
            return document.getElementById('scriptDescription')?.value?.trim() !== '';
            
        default:
            return false;
    }
}

// 更新步驟控制按鈕狀態
function updateStepControls(step) {
    console.log(`更新步驟 ${step} 控制按鈕狀態`);
    
    const prevBtn = document.getElementById('prevStepBtn');
    const nextBtn = document.getElementById('nextStepBtn');
    
    // 基本樣式
    const baseStyle = 'btn rounded-pill fw-bold px-4 py-2 shadow-sm';
    
    if (prevBtn) {
        if (step === 1) {
            prevBtn.style.display = 'none';
        } else {
            prevBtn.style.display = 'inline-block';
            prevBtn.className = `${baseStyle} btn-outline-primary me-2`;
            prevBtn.innerHTML = '<i class="fas fa-arrow-left me-1"></i>上一步';
        }
    }
    
    if (nextBtn) {
        // 根據當前步驟設置按鈕文字和功能
        if (step === 4) {
            nextBtn.innerHTML = '<i class="fas fa-cloud-upload-alt me-1"></i>上傳';
            nextBtn.className = `${baseStyle} btn-primary`;
            nextBtn.onclick = handleFinalStep;
        } else {
            nextBtn.innerHTML = '下一步<i class="fas fa-arrow-right ms-1"></i>';
            nextBtn.className = `${baseStyle} btn-primary`;
            nextBtn.onclick = nextStep;
        }
        
        // 檢查能否進入下一步
        nextBtn.disabled = !canProceedToNextStep();
        if (nextBtn.disabled) {
            nextBtn.className = `${baseStyle} btn-secondary`;
        }
    }
}

// 上一步
function prevStep() {
    console.log('返回上一步，當前步驟:', currentStep);
    
    if (currentStep > 1) {
        // 將當前步驟重置為非活動狀態
        updateStepStatus(currentStep, '');
        
        // 更新步驟
        currentStep--;
        console.log(`返回步驟 ${currentStep}`);
        
        // 標記新步驟為活動狀態
        updateStepStatus(currentStep, 'active');
        
        // 更新UI
        showStepContent(currentStep);
    } else {
        console.log('已經是第一步，無法返回');
    }
}

// 添加CSS動畫
const style = document.createElement('style');
style.textContent = `
@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

.step-indicator {
    transition: all 0.3s ease;
}

.step-indicator.completed {
    background-color: #28a745;
    color: white;
}

.step-indicator.error {
    background-color: #dc3545;
    color: white;
}

.step-indicator.active {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
`;
document.head.appendChild(style);

// 驗證描述長度
function validateDescription(description) {
    return description.length <= 20;
}

// 初始化描述字符計數
function initializeDescriptionCounter() {
    const description = document.getElementById('scriptDescription');
    const charCount = document.getElementById('charCount');
    
    if (description && charCount) {
        // 設置初始值
        charCount.textContent = 20 - description.value.length;
        
        description.addEventListener('input', function() {
            const remainingChars = 20 - this.value.length;
            charCount.textContent = remainingChars;
            
            // 更新視覺反饋
            if (remainingChars < 0) {
                charCount.style.color = '#dc3545';  // 紅色
            } else if (remainingChars <= 5) {
                charCount.style.color = '#ffc107';  // 黃色
            } else {
                charCount.style.color = '#198754';  // 綠色
            }
        });
    }
}

// 處理最後步驟的表單提交
async function handleFinalStep() {
    console.log('處理最後步驟');
    
    if (!selectedFile) {
        showNotification('錯誤', '請選擇一個文件', 'error');
        return false;
    }
    
    const description = document.getElementById('scriptDescription').value;
    const selectedTag = document.querySelector('input[name="scriptTag"]:checked');
    
    if (!description || description.trim().length === 0) {
        showNotification('錯誤', '請輸入腳本描述', 'error');
        return false;
    }
    
    if (description.length > 20) {
        showNotification('錯誤', '描述不能超過20個字符', 'error');
        return false;
    }
    
    if (!selectedTag) {
        showNotification('錯誤', '請選擇一個標籤', 'error');
        return false;
    }
    
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('description', description);
    formData.append('tag', selectedTag.value);
    
    // 顯示加載狀態
    toggleLoading(true);
    
    try {
        const response = await fetch('/script/', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        toggleLoading(false);
        
        if (result.status === 'success') {
            showNotification('成功', '上傳成功', 'success');
            // 重置上傳表單並返回列表視圖
            setTimeout(() => {
                resetUploadForm();
                showListView();
                loadScripts();
            }, 300);
            return true;
        } else {
            showNotification('錯誤', result.message || '上傳失敗', 'error');
            return false;
        }
    } catch (error) {
        console.error('上傳錯誤:', error);
        toggleLoading(false);
        showNotification('錯誤', '上傳過程中發生錯誤', 'error');
        return false;
    }
}

// 顯示上傳確認步驟
function showUploadConfirmStep() {
    console.log('顯示上傳確認步驟');
    
    // 更新標題和按鈕
    document.querySelector('.current-step-title').textContent = '確認上傳';
    const nextButton = document.getElementById('nextStepBtn');
    if (nextButton) {
        nextButton.textContent = '上傳';
        nextButton.onclick = handleFinalStep;
    }
    
    // 獲取上傳確認容器
    const uploadConfirm = document.getElementById('uploadConfirm');
    if (!uploadConfirm) {
        console.error('找不到上傳確認容器');
        return;
    }
    
    // 顯示上傳表單
    showUploadForm();
}

// 更新系統資源使用情況
async function updateSystemResources() {
    try {
        const response = await fetch(`${window.location.origin}/api/v1/system/resources`);

        if (!response.ok) {
            console.warn(`系統資源API響應錯誤: ${response.status}`);
            return;
        }

        const data = await response.json();

        if (data && data.status === 'success' && data.data) {
            const resourcesData = data.data;

            // 更新 CPU 使用率
            const cpuElement = document.getElementById('cpu-usage');
            if (cpuElement && resourcesData) {
                const cpuValue = parseFloat(resourcesData.cpu_percent || resourcesData.cpu || 0);
                if (!isNaN(cpuValue)) {
                    cpuElement.textContent = `${cpuValue.toFixed(1)}%`;
                    cpuElement.style.width = `${Math.min(cpuValue, 100)}%`;
                }
            }

            // 更新記憶體使用率
            const memoryElement = document.getElementById('memory-usage');
            if (memoryElement && resourcesData) {
                const memoryValue = parseFloat(resourcesData.memory_percent || resourcesData.memory || 0);
                if (!isNaN(memoryValue)) {
                    memoryElement.textContent = `${memoryValue.toFixed(1)}%`;
                    memoryElement.style.width = `${Math.min(memoryValue, 100)}%`;
                }
            }

            // 更新排程數量
            const schedulesElement = document.getElementById('schedule-count');
            if (schedulesElement && resourcesData) {
                const current = parseInt(resourcesData.schedule_count || 0);
                const max = parseInt(resourcesData.max_schedules || 10);
                const percentage = max > 0 ? (current / max) * 100 : 0;
                schedulesElement.style.width = `${Math.min(percentage, 100)}%`;
                schedulesElement.textContent = `${current}/${max}`;
            }
        } else {
            console.warn('系統資源API返回無效數據:', data);
        }
    } catch (error) {
        console.warn('更新系統資源失敗:', error.message);
        // 靜默處理錯誤，避免在Console中顯示過多錯誤
    }
}

// 載入腳本列表
async function loadScripts() {
    try {
        const response = await fetch('/script/');
        const data = await response.json();
        if (data.status === 'success') {
            const scriptList = document.getElementById('script-list');
            if (scriptList) {
                if (data.data.length === 0) {
                    scriptList.innerHTML = '<div class="text-center my-4">沒有找到腳本</div>';
                } else {
                    scriptList.innerHTML = data.data.map(script => `
                        <div class="script-item">
                            <span class="script-name">${script.name}</span>
                            <span class="script-description">${script.description || '無描述'}</span>
                            <span class="script-tags">${script.tags || '未分類'}</span>
                        </div>
                    `).join('');
                }
            }
            
            // 同時更新腳本選擇器
            const scriptSelect = document.getElementById('script-select');
            if (scriptSelect) {
                // 清空下拉選單（保留第一個默認選項）
                while (scriptSelect.options.length > 1) {
                    scriptSelect.remove(1);
                }
                
                // 添加腳本選項
                data.data.forEach(script => {
                    const option = document.createElement('option');
                    option.value = script.name;
                    option.textContent = `${script.name} - ${script.description || '無描述'}`;
                    scriptSelect.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('載入腳本列表失敗:', error);
        const scriptList = document.getElementById('script-list');
        if (scriptList) {
            scriptList.innerHTML = '<div class="text-center my-4 text-danger">載入腳本列表失敗</div>';
        }
    }
}


