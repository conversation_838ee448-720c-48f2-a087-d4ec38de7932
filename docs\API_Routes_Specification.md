# Python 腳本管理與排程系統 API 路由規範

*文檔版本：1.0.0*  
*最後更新日期：2025-03-27*

## 目錄
1. [概述](#概述)
2. [路徑規範](#路徑規範)
3. [腳本管理 API](#腳本管理-api)
4. [排程管理 API](#排程管理-api)
5. [系統資源 API](#系統資源-api)
6. [前端實作範例](#前端實作範例)

## 概述

本文檔定義了 Python 腳本管理與排程系統的 API 路由規範，用於統一後端 API 實作與前端調用的路徑標準。系統 API 主要分為三大類：
- 腳本管理相關 API
- 排程管理相關 API
- 系統資源相關 API

## 路徑規範

### 命名規則
* 使用全小寫英文與底線組合
* 使用名詞而非動詞描述資源
* 複數形式表示集合資源，單數形式表示具體資源

### 路徑前綴
* 腳本管理 API：`/script`
* 排程管理 API：`/schedule/api/schedule`
* 系統資源 API：`/schedule/api/system-resources`

### HTTP 方法
* `GET`：獲取資源
* `POST`：創建資源
* `PUT`：更新資源
* `DELETE`：刪除資源

### 響應格式
所有 API 均返回 JSON 格式，標準響應結構如下：
```json
{
  "status": "success|error",
  "message": "操作結果描述",
  "data": { ... } // 可選，響應數據
}
```

## 腳本管理 API

| 方法   | 路徑                           | 描述                      | 權限  |
|--------|--------------------------------|--------------------------|-------|
| GET    | `/script`                      | 獲取腳本列表              | 一般  |
| POST   | `/script`                      | 上傳腳本                  | 一般  |
| GET    | `/script/{script_name}/info`   | 獲取指定腳本的詳細信息    | 一般  |
| PUT    | `/script/{script_name}/meta`   | 更新腳本元數據（標籤、描述）| 一般  |
| DELETE | `/script/{script_name}`        | 刪除指定腳本              | 一般  |
| POST   | `/script/validate`             | 驗證腳本內容及格式        | 一般  |
| POST   | `/script/install-dependencies` | 安裝腳本依賴庫            | 管理員|

### 請求/響應示例

#### 獲取腳本列表
```
GET /script
```

響應：
```json
{
  "status": "success",
  "data": [
    {
      "name": "data_processor.py",
      "description": "數據處理腳本",
      "tags": ["數據處理", "分析"],
      "created_at": "2025-03-20T10:30:45",
      "updated_at": "2025-03-21T14:25:30"
    },
    // 更多腳本...
  ]
}
```

#### 上傳腳本
```
POST /script
Content-Type: multipart/form-data

file: [檔案數據]
description: "數據處理腳本"
tags: ["數據處理", "分析"]
```

響應：
```json
{
  "status": "success",
  "message": "腳本上傳成功",
  "data": {
    "name": "data_processor.py",
    "description": "數據處理腳本",
    "tags": ["數據處理", "分析"]
  }
}
```

## 排程管理 API

| 方法   | 路徑                                         | 描述                  | 權限  |
|--------|--------------------------------------------|----------------------|-------|
| GET    | `/schedule/api/schedule`                    | 獲取排程列表          | 一般  |
| POST   | `/schedule/api/schedule`                    | 建立新排程            | 一般  |
| GET    | `/schedule/api/schedule/{id}`               | 獲取指定排程詳情      | 一般  |
| PUT    | `/schedule/api/schedule/{id}`               | 更新排程信息          | 一般  |
| DELETE | `/schedule/api/schedule/{id}`               | 刪除指定排程          | 一般  |
| POST   | `/schedule/api/schedule/{id}/toggle`        | 切換排程啟用/停用狀態  | 一般  |
| POST   | `/schedule/api/schedule/{id}/execute`       | 立即執行排程          | 一般  |
| GET    | `/schedule/api/schedule/{id}/logs`          | 獲取排程執行日誌      | 一般  |
| GET    | `/schedule/api/schedule/{id}/output-files`  | 獲取排程輸出檔案列表  | 一般  |
| POST   | `/schedule/api/schedule/{id}/task/{action}` | 執行任務相關操作      | 一般  |

### 請求/響應示例

#### 獲取排程列表
```
GET /schedule/api/schedule
```

響應：
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "script_name": "data_processor.py",
      "schedule_type": "daily",
      "execution_time": "09:00:00",
      "next_run": "2025-03-28T09:00:00",
      "status": "pending",
      "is_active": true,
      "description": "每日數據處理"
    },
    // 更多排程...
  ]
}
```

#### 建立新排程
```
POST /schedule/api/schedule
Content-Type: application/json

{
  "script_name": "data_processor.py",
  "schedule_type": "daily",
  "execution_time": "09:00:00",
  "description": "每日數據處理"
}
```

響應：
```json
{
  "status": "success",
  "message": "排程建立成功",
  "data": {
    "id": 1,
    "script_name": "data_processor.py",
    "schedule_type": "daily",
    "execution_time": "09:00:00",
    "next_run": "2025-03-28T09:00:00",
    "status": "pending",
    "is_active": true,
    "description": "每日數據處理"
  }
}
```

## 系統資源 API

| 方法   | 路徑                              | 描述                  | 權限  |
|--------|----------------------------------|----------------------|-------|
| GET    | `/schedule/api/system-resources` | 獲取系統資源使用情況   | 一般  |

### 請求/響應示例

#### 獲取系統資源使用情況
```
GET /schedule/api/system-resources
```

響應：
```json
{
  "status": "success",
  "data": {
    "cpu": 25.4,
    "memory": 48.7,
    "disk": 62.3,
    "schedules": {
      "current": 5,
      "max": 20
    }
  }
}
```

## 前端實作範例

### 腳本上傳範例
```javascript
// 上傳腳本
async function uploadScript() {
    try {
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('description', description);
        formData.append('tags', JSON.stringify(selectedTags));
        
        const response = await fetch('/script', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification('成功', '腳本上傳成功', 'success');
        } else {
            throw new Error(result.message || '上傳失敗');
        }
    } catch (error) {
        console.error('上傳腳本失敗:', error);
        showNotification('錯誤', error.message || '上傳失敗', 'error');
    }
}
```

### 排程管理範例
```javascript
// 載入排程列表
async function loadSchedulesList() {
    try {
        const response = await fetch('/schedule/api/schedule');
        const data = await response.json();
        
        if (data.status === 'success') {
            renderScheduleList(data.data);
        } else {
            throw new Error('載入排程失敗');
        }
    } catch (error) {
        console.error('載入排程失敗:', error);
        showError('載入排程列表失敗');
    }
}

// 切換排程狀態
async function toggleScheduleStatus(scheduleId, isActive) {
    try {
        const response = await fetch(`/schedule/api/schedule/${scheduleId}/toggle`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification('成功', isActive ? '排程已啟用' : '排程已停用', 'success');
            await loadSchedulesList();
        } else {
            throw new Error(result.message || '操作失敗');
        }
    } catch (error) {
        console.error('切換排程狀態失敗:', error);
        showNotification('錯誤', error.message || '操作失敗', 'error');
    }
}
```

### 系統資源監控範例
```javascript
// 更新系統資源使用情況
async function updateSystemResources() {
    try {
        const response = await fetch('/schedule/api/system-resources');
        const data = await response.json();
        
        if (data.status === 'success') {
            const resourcesData = data.data;
            
            // 更新 CPU 使用率
            updateCpuUsage(resourcesData.cpu);
            
            // 更新記憶體使用率
            updateMemoryUsage(resourcesData.memory);
            
            // 更新排程數量
            updateSchedulesCount(resourcesData.schedules.current, resourcesData.schedules.max);
        }
    } catch (error) {
        console.error('更新系統資源失敗:', error);
    }
}
```

---
**注意**：本文檔為系統開發規範參考，實際實作時應遵循此規範以確保系統一致性與穩定性。API 路由變更時應更新本文檔並通知所有開發人員。 