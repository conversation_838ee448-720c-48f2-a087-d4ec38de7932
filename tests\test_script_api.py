import os
import pytest
from datetime import datetime
from app import create_app, db
from app.models.script import Script
from app.services.script_executor import ScriptExecutor

class TestScriptAPI:
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """每個測試方法執行前的設置"""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 確保資料庫表格存在
        with self.app.app_context():
            db.drop_all()
            db.create_all()
        
        # 建立測試腳本
        self.test_script = Script(
            name='test_script.py',
            description='測試腳本',
            tags=['測試', '自動化'],
            created_at=datetime.now()
        )
        db.session.add(self.test_script)
        db.session.commit()
        
        yield
        
        # 清理
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_get_scripts(self):
        """測試獲取腳本列表"""
        response = self.client.get('/script/api/scripts')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert len(response.json['data']) >= 1

    def test_upload_script(self):
        """測試上傳腳本"""
        # 建立測試腳本檔案
        test_script_content = "print('Hello, World!')"
        test_script_path = os.path.join(self.app.config['UPLOAD_FOLDER'], 'test_upload.py')
        with open(test_script_path, 'w') as f:
            f.write(test_script_content)
        
        # 上傳腳本
        with open(test_script_path, 'rb') as f:
            response = self.client.post(
                '/script/api/scripts',
                data={'file': (f, 'test_upload.py')},
                content_type='multipart/form-data'
            )
        
        assert response.status_code == 201
        assert response.json['status'] == 'success'
        
        # 清理測試檔案
        os.remove(test_script_path)

    def test_get_script_detail(self):
        """測試獲取腳本詳情"""
        response = self.client.get(f'/script/api/scripts/{self.test_script.id}')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert response.json['data']['name'] == 'test_script.py'

    def test_update_script(self):
        """測試更新腳本"""
        data = {
            'description': '更新後的描述',
            'tags': ['更新', '測試']
        }
        response = self.client.put(f'/script/api/scripts/{self.test_script.id}', json=data)
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        assert response.json['data']['description'] == '更新後的描述'

    def test_delete_script(self):
        """測試刪除腳本"""
        response = self.client.delete(f'/script/api/scripts/{self.test_script.id}')
        assert response.status_code == 200
        assert response.json['status'] == 'success'
        
        # 確認腳本已被刪除
        response = self.client.get(f'/script/api/scripts/{self.test_script.id}')
        assert response.status_code == 404 