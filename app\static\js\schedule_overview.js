// 排程狀態配置
const ScheduleStatus = {
    WAITING: {
        badge: 'badge-warning',
        text: '等待中',
        icon: 'fa-clock',
        controls: []
    },
    RUNNING: {
        badge: 'badge-success',
        text: '執行中',
        icon: 'fa-play',
        controls: ['pause', 'stop', 'log', 'files']
    },
    PAUSED: {
        badge: 'badge-info',
        text: '已暫停',
        icon: 'fa-pause',
        controls: ['resume', 'stop']
    },
    COMPLETED: {
        badge: 'badge-success',
        text: '已完成',
        icon: 'fa-check',
        controls: ['log', 'files']
    },
    FAILED: {
        badge: 'badge-danger',
        text: '執行失敗',
        icon: 'fa-times',
        controls: ['retry', 'log', 'files']
    }
};

// 初始化 WebSocket 連接
const socket = io();

// 監聽狀態更新
socket.on('status_update', function(data) {
    updateScheduleStatus(data.schedule_id, data.status, data.progress);
});

// 監聽日誌更新
socket.on('log_update', function(data) {
    appendLog(data.schedule_id, data.message);
});

// 載入排程列表
async function loadScheduleList() {
    try {
        const response = await fetch('/schedule/api/schedule');
        const schedules = await response.json();
        
        const scheduleList = document.getElementById('scheduleList');
        scheduleList.innerHTML = schedules.map(schedule => 
            generateScheduleRow(schedule)
        ).join('');
    } catch (error) {
        showError('載入排程列表失敗');
    }
}

// 生成排程列表項目
function generateScheduleRow(schedule) {
    const status = ScheduleStatus[schedule.status];
    return `
        <tr data-schedule-id="${schedule.id}">
            <td>${schedule.script_name}</td>
            <td>${schedule.schedule_type}</td>
            <td>${formatScheduleCondition(schedule)}</td>
            <td>${formatNextRunTime(schedule.next_run)}</td>
            <td>
                <div class="status-badge ${status.badge}">
                    <i class="fas ${status.icon}"></i>
                    <span>${status.text}</span>
                    ${schedule.status === 'RUNNING' ? generateProgressBar(schedule.progress) : ''}
                </div>
            </td>
            <td>
                <div class="schedule-controls">
                    ${generateScheduleControls(schedule)}
                </div>
            </td>
            <td>
                <div class="task-controls">
                    ${generateTaskControls(schedule, status)}
                </div>
            </td>
        </tr>
        <tr class="output-row" id="output-row-${schedule.id}" style="display: none;">
            <td colspan="7">
                <div class="output-container">
                    <div class="output-tabs">
                        <button class="tab-btn active" onclick="showTab(${schedule.id}, 'log')">執行日誌</button>
                        <button class="tab-btn" onclick="showTab(${schedule.id}, 'files')">輸出檔案</button>
                    </div>
                    <div class="tab-content">
                        <div id="log-content-${schedule.id}" class="tab-pane active">
                            <pre class="log-output"></pre>
                        </div>
                        <div id="files-content-${schedule.id}" class="tab-pane">
                            <div class="file-list"></div>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
    `;
}

// 生成進度條
function generateProgressBar(progress) {
    return `
        <div class="progress-container">
            <div class="progress-fill" style="width: ${progress}%"></div>
        </div>
    `;
}

// 生成排程控制按鈕
function generateScheduleControls(schedule) {
    return `
        <button class="action-btn ${schedule.is_active ? 'btn-danger' : 'btn-success'}"
                onclick="toggleSchedule(${schedule.id})">
            <i class="fas ${schedule.is_active ? 'fa-pause-circle' : 'fa-play-circle'}"></i>
            ${schedule.is_active ? '停用' : '啟用'}
        </button>
        <button class="action-btn btn-primary" onclick="editSchedule(${schedule.id})">
            <i class="fas fa-edit"></i> 修改
        </button>
        <button class="action-btn btn-danger" onclick="deleteSchedule(${schedule.id})">
            <i class="fas fa-trash"></i> 刪除
        </button>
    `;
}

// 生成任務控制按鈕
function generateTaskControls(schedule, status) {
    return status.controls.map(control => {
        const buttonConfig = {
            pause: { icon: 'fa-pause', text: '暫停', class: 'btn-warning' },
            resume: { icon: 'fa-play', text: '繼續', class: 'btn-success' },
            stop: { icon: 'fa-stop', text: '停止', class: 'btn-danger' },
            log: { icon: 'fa-file-alt', text: '日誌', class: 'btn-info' },
            files: { icon: 'fa-folder-open', text: '檔案', class: 'btn-info' },
            retry: { icon: 'fa-redo', text: '重試', class: 'btn-warning' }
        }[control];

        return `
            <button class="action-btn ${buttonConfig.class}"
                    onclick="handleTaskAction(${schedule.id}, '${control}')">
                <i class="fas ${buttonConfig.icon}"></i> ${buttonConfig.text}
            </button>
        `;
    }).join('');
}

// 處理任務操作
async function handleTaskAction(scheduleId, action) {
    try {
        const response = await fetch(`/api/v1/schedules/${scheduleId}/task/${action}`, {
            method: 'POST'
        });
        
        if (!response.ok) {
            throw new Error('操作失敗');
        }
        
        // 根據操作類型執行相應的 UI 更新
        switch(action) {
            case 'log':
            case 'files':
                toggleOutput(scheduleId, action);
                break;
        }
    } catch (error) {
        showError('操作失敗：' + error.message);
    }
}

// 切換輸出顯示
function toggleOutput(scheduleId, type) {
    const outputRow = document.getElementById(`output-row-${scheduleId}`);
    const isVisible = outputRow.style.display !== 'none';
    
    if (!isVisible) {
        outputRow.style.display = 'table-row';
        showTab(scheduleId, type);
        if (type === 'files') {
            loadOutputFiles(scheduleId);
        }
    } else {
        outputRow.style.display = 'none';
    }
}

// 切換標籤頁
function showTab(scheduleId, tab) {
    const tabs = document.querySelectorAll(`#output-row-${scheduleId} .tab-btn`);
    const contents = document.querySelectorAll(`#output-row-${scheduleId} .tab-pane`);
    
    tabs.forEach(t => t.classList.remove('active'));
    contents.forEach(c => c.classList.remove('active'));
    
    document.querySelector(`#output-row-${scheduleId} .tab-btn[onclick*="${tab}"]`).classList.add('active');
    document.getElementById(`${tab}-content-${scheduleId}`).classList.add('active');
}

// 載入輸出檔案列表
async function loadOutputFiles(scheduleId) {
    try {
        const response = await fetch(`/api/v1/schedules/${scheduleId}/output-files`);
        const files = await response.json();
        
        const fileList = document.querySelector(`#files-content-${scheduleId} .file-list`);
        fileList.innerHTML = files.map(file => `
            <div class="file-item">
                <i class="fas ${getFileIcon(file.type)} file-icon"></i>
                <span>${file.name}</span>
                <div class="file-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="previewFile(${scheduleId}, '${file.name}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="downloadFile(${scheduleId}, '${file.name}')">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        `).join('');
    } catch (error) {
        showError('載入檔案列表失敗');
    }
}

// 更新排程狀態
function updateScheduleStatus(scheduleId, status, progress) {
    const row = document.querySelector(`tr[data-schedule-id="${scheduleId}"]`);
    if (!row) return;

    const statusConfig = ScheduleStatus[status];
    const statusCell = row.querySelector('.status-badge');
    statusCell.className = `status-badge ${statusConfig.badge}`;
    statusCell.innerHTML = `
        <i class="fas ${statusConfig.icon}"></i>
        <span>${statusConfig.text}</span>
        ${status === 'RUNNING' ? generateProgressBar(progress) : ''}
    `;

    // 更新操作按鈕
    const taskControls = row.querySelector('.task-controls');
    taskControls.innerHTML = generateTaskControls(
        { id: scheduleId, status: status },
        statusConfig
    );
}

// 輔助函數
function formatScheduleCondition(schedule) {
    switch(schedule.schedule_type) {
        case 'DAILY':
            return `每日 ${schedule.execution_time}`;
        case 'WEEKLY':
            return `每週 ${schedule.weekdays.join(', ')} ${schedule.execution_time}`;
        case 'MONTHLY':
            return `每月 ${schedule.days_of_month.join(', ')} ${schedule.execution_time}`;
        case 'ONCE':
            return `一次性 ${schedule.execution_time}`;
        default:
            return schedule.schedule_type;
    }
}

function formatNextRunTime(time) {
    if (!time) return '-';
    return new Date(time).toLocaleString('zh-TW');
}

function getFileIcon(fileType) {
    const icons = {
        'txt': 'fa-file-alt',
        'csv': 'fa-file-csv',
        'json': 'fa-file-code',
        'log': 'fa-file-alt'
    };
    return icons[fileType] || 'fa-file';
}

function showError(message) {
    const toast = document.createElement('div');
    toast.className = 'toast toast-error';
    toast.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        <span>${message}</span>
    `;
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
}

// 頁面載入時初始化
document.addEventListener('DOMContentLoaded', function() {
    loadScheduleList();
}); 