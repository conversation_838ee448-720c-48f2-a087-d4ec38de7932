<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript 測試</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button { background-color: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>JavaScript 功能測試</h1>
        <div id="test-results"></div>
        
        <button onclick="testBasicJS()">測試基本 JavaScript</button>
        <button onclick="testFetch()">測試 Fetch API</button>
        <button onclick="testMainJS()">測試 main.js 載入</button>
        
        <h2>控制台日誌</h2>
        <div id="console-log"></div>
    </div>

    <script>
        const originalLog = console.log;
        const originalError = console.error;
        const logContainer = document.getElementById('console-log');
        
        function addLogEntry(message, type = 'log') {
            const entry = document.createElement('div');
            entry.className = `test-result ${type === 'error' ? 'error' : 'success'}`;
            entry.textContent = `[${type.toUpperCase()}] ${message}`;
            logContainer.appendChild(entry);
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogEntry(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLogEntry(args.join(' '), 'error');
        };
        
        window.addEventListener('error', function(e) {
            addLogEntry(`錯誤: ${e.message} (${e.filename}:${e.lineno})`, 'error');
        });
        
        function addResult(message, success = true) {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            div.textContent = message;
            results.appendChild(div);
        }
        
        function testBasicJS() {
            try {
                const testArray = [1, 2, 3];
                const testObject = { name: 'test', value: 123 };
                const testFunction = () => 'Hello World';
                
                if (testArray.length === 3 && testObject.name === 'test' && testFunction() === 'Hello World') {
                    addResult('✓ 基本 JavaScript 功能正常');
                } else {
                    addResult('✗ 基本 JavaScript 功能異常', false);
                }
            } catch (error) {
                addResult(`✗ 基本 JavaScript 測試失敗: ${error.message}`, false);
            }
        }
        
        async function testFetch() {
            try {
                addResult('正在測試 Fetch API...');
                const response = await fetch('http://127.0.0.1:5000/');
                if (response.ok) {
                    addResult('✓ Fetch API 正常，伺服器可訪問');
                } else {
                    addResult(`✗ 伺服器響應錯誤: ${response.status}`, false);
                }
            } catch (error) {
                addResult(`✗ Fetch API 測試失敗: ${error.message}`, false);
            }
        }
        
        function testMainJS() {
            try {
                const script = document.createElement('script');
                script.src = 'app/static/js/main.js';
                script.onload = function() {
                    addResult('✓ main.js 載入成功');
                    setTimeout(() => {
                        if (typeof showNotification === 'function') {
                            addResult('✓ showNotification 函數可用');
                        } else {
                            addResult('✗ showNotification 函數不可用', false);
                        }
                        
                        if (typeof updateSystemResources === 'function') {
                            addResult('✓ updateSystemResources 函數可用');
                        } else {
                            addResult('✗ updateSystemResources 函數不可用', false);
                        }
                        
                        if (typeof loadScriptsList === 'function') {
                            addResult('✓ loadScriptsList 函數可用');
                        } else {
                            addResult('✗ loadScriptsList 函數不可用', false);
                        }
                    }, 100);
                };
                script.onerror = function() {
                    addResult('✗ main.js 載入失敗', false);
                };
                document.head.appendChild(script);
            } catch (error) {
                addResult(`✗ main.js 測試失敗: ${error.message}`, false);
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            addResult('頁面載入完成');
            testBasicJS();
        });
        
        console.log('測試頁面初始化完成');
    </script>
</body>
</html>
