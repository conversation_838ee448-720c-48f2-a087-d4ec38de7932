// 測試步驟切換和UI顯示邏輯
describe('步驟切換和UI顯示測試', () => {
    // 模擬DOM環境
    document.body.innerHTML = `
        <div id="stepperContainer">
            <div class="step" data-step="1">步驟1</div>
            <div class="step" data-step="2">步驟2</div>
            <div class="step" data-step="3">步驟3</div>
            <div class="step" data-step="4">步驟4</div>
        </div>
        <div class="step-content" data-step="1">步驟1內容</div>
        <div class="step-content" data-step="2">步驟2內容</div>
        <div class="step-content" data-step="3">步驟3內容</div>
        <div class="step-content" data-step="4">步驟4內容</div>
        <button id="prevStepBtn">上一步</button>
        <button id="nextStepBtn">下一步</button>
        <div id="validationResult"></div>
        <div id="filePreview"></div>
    `;

    beforeEach(() => {
        // 重置全局變量
        currentStep = 1;
        validationResult = null;
        currentFile = null;
        // 重置UI
        updateStepperUI();
    });

    test('初始狀態測試', () => {
        expect(currentStep).toBe(1);
        expect(document.querySelector('.step.active')).toBeTruthy();
        expect(document.querySelector('.step-content[data-step="1"]').style.display).toBe('block');
    });

    test('步驟切換測試', () => {
        // 模擬文件選擇
        currentFile = new File(['test'], 'test.py', { type: 'text/plain' });
        
        // 測試下一步
        nextStep();
        expect(currentStep).toBe(2);
        expect(document.querySelector('.step-content[data-step="2"]').style.display).toBe('block');
        
        // 測試上一步
        prevStep();
        expect(currentStep).toBe(1);
        expect(document.querySelector('.step-content[data-step="1"]').style.display).toBe('block');
    });

    test('驗證結果顯示測試', () => {
        const testValidation = {
            status: 'success',
            validation_result: {
                errors: [],
                warnings: ['測試警告'],
                required_packages: []
            }
        };
        
        validationResult = testValidation;
        showValidationResult(testValidation.validation_result);
        
        const validationDiv = document.getElementById('validationResult');
        expect(validationDiv.style.display).toBe('block');
        expect(validationDiv.innerHTML).toContain('測試警告');
    });

    test('步驟內容顯示測試', () => {
        // 測試所有步驟的內容顯示
        [1, 2, 3, 4].forEach(step => {
            showStepContent(step);
            const content = document.querySelector(`.step-content[data-step="${step}"]`);
            expect(content.style.display).toBe('block');
            
            // 確保其他步驟內容被隱藏
            document.querySelectorAll('.step-content').forEach(otherContent => {
                if (otherContent !== content) {
                    expect(otherContent.style.display).toBe('none');
                }
            });
        });
    });

    test('按鈕狀態測試', () => {
        // 測試第一步時的按鈕狀態
        updateStepperUI();
        expect(document.getElementById('prevStepBtn').disabled).toBe(true);
        expect(document.getElementById('nextStepBtn').disabled).toBe(true);

        // 模擬文件選擇
        currentFile = new File(['test'], 'test.py', { type: 'text/plain' });
        updateStepperUI();
        expect(document.getElementById('nextStepBtn').disabled).toBe(false);

        // 測試最後一步
        currentStep = 4;
        updateStepperUI();
        expect(document.getElementById('nextStepBtn').disabled).toBe(true);
    });
}); 