<!DOCTYPE html>
<html>
<head>
    <title>簡單測試</title>
</head>
<body>
    <h1>伺服器測試</h1>
    <p>如果你看到這個頁面，表示伺服器正在運行。</p>
    
    <script>
        console.log('🚀 JavaScript 正在執行');
        
        // 測試 API 連接
        fetch('/api/v1/schedules')
            .then(response => {
                console.log('✅ API 響應狀態:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('✅ API 數據:', data);
                document.body.innerHTML += '<p style="color: green;">✅ API 連接成功！</p>';
            })
            .catch(error => {
                console.error('❌ API 錯誤:', error);
                document.body.innerHTML += '<p style="color: red;">❌ API 連接失敗: ' + error.message + '</p>';
            });
    </script>
</body>
</html>
