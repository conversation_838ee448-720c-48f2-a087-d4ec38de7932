# -*- coding: utf-8 -*-
import inspect
from flask import current_app
import traceback
import logging

# 服務實例字典
_services = {}

def register_service(service_name, service_instance):
    """
    註冊服務實例
    
    Args:
        service_name (str): 服務名稱
        service_instance (object): 服務實例
    """
    if not service_name:
        raise ValueError("服務名稱不能為空")
    
    if service_instance is None:
        raise ValueError("服務實例不能為空")
    
    _services[service_name] = service_instance
    logger = logging.getLogger('app.services')
    logger.info(f'服務已註冊: {service_name}')
    
    # 如果服務實例有init方法且未初始化，則進行初始化
    if hasattr(service_instance, 'init') and callable(service_instance.init):
        if not getattr(service_instance, '_initialized', False):
            try:
                service_instance.init()
                setattr(service_instance, '_initialized', True)
                logger.info(f'服務已初始化: {service_name}')
            except Exception as e:
                logger.error(f'服務初始化失敗: {service_name}, 錯誤: {str(e)}')

def get_service(service_name):
    """
    獲取服務實例
    
    Args:
        service_name (str): 服務名稱
        
    Returns:
        object: 服務實例
        
    Raises:
        KeyError: 如果服務未註冊
    """
    logger = logging.getLogger('app.services')
    if service_name not in _services:
        logger.error(f'找不到服務: {service_name}，已註冊服務: {list(_services.keys())}')
        try:
            from app.utils.api_logger import api_logger
            api_logger.error(f'找不到服務: {service_name}，已註冊服務: {list(_services.keys())}')
        except:
            pass  # 如果api_logger不可用，跳過日誌記錄
        raise KeyError(f"找不到服務: {service_name}")
    
    return _services[service_name]

def has_service(service_name):
    """檢查服務是否已註冊"""
    return service_name in _services

def init_services(app):
    """
    初始化所有服務
    
    Args:
        app (Flask): Flask應用實例
    """
    app.logger.debug('初始化服務...')
    
    # 清空服務註冊表，以便重新初始化
    _services.clear()
    
    # 附加服務到應用上下文，以便通過current_app訪問
    with app.app_context():
        # 首先初始化系統監控服務，因為其他服務可能依賴它
        try:
            from app.services.system_monitor import SystemMonitor
            system_monitor = SystemMonitor(app)
            # 確保直接設置在app上下文
            app.system_monitor = system_monitor
            # 同時註冊到服務表
            register_service('system_monitor', system_monitor)
            # 提供兩個別名，確保向後兼容
            register_service('SystemMonitor', system_monitor)
            register_service('system_service', system_monitor)
            app.logger.info('系統監控服務初始化完成')
        except Exception as e:
            app.logger.error(f'系統監控服務初始化失敗: {str(e)}')
            app.logger.error(traceback.format_exc())
        
        # 初始化腳本執行器
        try:
            from app.services.script_executor import ScriptExecutor
            script_executor = ScriptExecutor(app)
            # 直接設置到app
            app.script_executor = script_executor
            # 註冊到服務表，提供多個別名確保兼容性
            register_service('script_executor', script_executor)
            register_service('script_service', script_executor)  # 關鍵：API使用此名稱
            app.logger.info('腳本執行器初始化完成')
        except Exception as e:
            app.logger.error(f'腳本執行器初始化失敗: {str(e)}')
            app.logger.error(traceback.format_exc())
        
        # 初始化排程管理器
        try:
            from app.services.schedule_manager import ScheduleManager
            schedule_manager = ScheduleManager(app)
            # 直接設置到app
            app.schedule_manager = schedule_manager
            # 註冊到服務表
            register_service('schedule_manager', schedule_manager)
            register_service('schedule_service', schedule_manager)  # 兼容舊API
            app.logger.info('排程管理器初始化完成')
        except Exception as e:
            app.logger.error(f'排程管理器初始化失敗: {str(e)}')
            app.logger.error(traceback.format_exc())
        
        # 初始化排程執行器
        try:
            from app.services.schedule_executor import ScheduleExecutor
            schedule_executor = ScheduleExecutor(app)
            # 直接設置到app
            app.schedule_executor = schedule_executor
            # 註冊到服務表
            register_service('schedule_executor', schedule_executor)
            register_service('execution_service', schedule_executor)  # 兼容舊API
            app.logger.info('排程執行器初始化完成')
        except Exception as e:
            app.logger.error(f'排程執行器初始化失敗: {str(e)}')
            app.logger.error(traceback.format_exc())
            
    app.logger.info(f'所有服務初始化完成，已註冊 {len(_services)} 個服務') 