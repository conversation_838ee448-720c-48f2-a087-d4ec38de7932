{% macro schedule_form(schedule=None, scripts=[], schedule_types=[]) %}
<div class="schedule-form">
    <h3>{{ '編輯排程' if schedule else '新增排程' }}</h3>
    
    <form id="scheduleForm" onsubmit="return handleSubmit(event)">
        <input type="hidden" name="schedule_id" value="{{ schedule.id if schedule else '' }}">
        
        <div class="form-group">
            <label for="script_name">腳本名稱</label>
            <select class="form-control" id="script_name" name="script_name" required>
                <option value="">請選擇腳本</option>
                {% for script in scripts %}
                <option value="{{ script.name }}" {{ 'selected' if schedule and schedule.script_name == script.name else '' }}>
                    {{ script.name }}
                </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="form-group">
            <label for="schedule_type">排程類型</label>
            <select class="form-control" id="schedule_type" name="schedule_type" required onchange="handleScheduleTypeChange()">
                <option value="">請選擇類型</option>
                <option value="immediate" {{ 'selected' if schedule and schedule.schedule_type == 'immediate' else '' }}>立即執行</option>
                <option value="once" {{ 'selected' if schedule and schedule.schedule_type == 'once' else '' }}>一次性</option>
                <option value="daily" {{ 'selected' if schedule and schedule.schedule_type == 'daily' else '' }}>每日</option>
                <option value="weekly" {{ 'selected' if schedule and schedule.schedule_type == 'weekly' else '' }}>每週</option>
                <option value="monthly" {{ 'selected' if schedule and schedule.schedule_type == 'monthly' else '' }}>每月</option>
            </select>
        </div>
        
        <div id="execution_time_group" class="form-group" style="display: none;">
            <label for="execution_time">執行時間</label>
            <input type="datetime-local" class="form-control" id="execution_time" name="execution_time"
                   value="{{ schedule.execution_time.strftime('%Y-%m-%dT%H:%M') if schedule and schedule.execution_time else '' }}">
        </div>
        
        <div id="weekdays_group" class="form-group" style="display: none;">
            <label>執行星期</label>
            <div class="weekday-buttons">
                <button type="button" class="btn btn-outline-primary weekday-btn" data-day="0">週日</button>
                <button type="button" class="btn btn-outline-primary weekday-btn" data-day="1">週一</button>
                <button type="button" class="btn btn-outline-primary weekday-btn" data-day="2">週二</button>
                <button type="button" class="btn btn-outline-primary weekday-btn" data-day="3">週三</button>
                <button type="button" class="btn btn-outline-primary weekday-btn" data-day="4">週四</button>
                <button type="button" class="btn btn-outline-primary weekday-btn" data-day="5">週五</button>
                <button type="button" class="btn btn-outline-primary weekday-btn" data-day="6">週六</button>
            </div>
            <input type="hidden" id="weekdays" name="weekdays" value="{{ schedule.weekdays|tojson if schedule and schedule.weekdays else '[]' }}">
        </div>
        
        <div id="days_of_month_group" class="form-group" style="display: none;">
            <label for="days_of_month">執行日期</label>
            <input type="text" class="form-control" id="days_of_month" name="days_of_month"
                   placeholder="請輸入日期（用逗號分隔，例如：1,15,30）"
                   value="{{ schedule.days_of_month|join(',') if schedule and schedule.days_of_month else '' }}">
        </div>
        
        <div class="form-group">
            <label for="description">描述</label>
            <textarea class="form-control" id="description" name="description" rows="3">{{ schedule.description if schedule else '' }}</textarea>
        </div>
        
        <div class="form-actions">
            <button type="submit" class="btn btn-primary">儲存</button>
            <button type="button" class="btn btn-secondary" onclick="window.history.back()">取消</button>
        </div>
    </form>
</div>

<style>
.schedule-form {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.weekday-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.weekday-btn {
    min-width: 60px;
}

.weekday-btn.active {
    background-color: #007bff;
    color: white;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 30px;
}
</style>

<script>
// 初始化星期按鈕狀態
function initWeekdayButtons() {
    const weekdays = JSON.parse(document.getElementById('weekdays').value || '[]');
    document.querySelectorAll('.weekday-btn').forEach(btn => {
        const day = parseInt(btn.dataset.day);
        if (weekdays.includes(day)) {
            btn.classList.add('active');
        }
    });
}

// 處理星期按鈕點擊
document.querySelectorAll('.weekday-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        this.classList.toggle('active');
        updateWeekdaysValue();
    });
});

// 更新星期值
function updateWeekdaysValue() {
    const weekdays = Array.from(document.querySelectorAll('.weekday-btn.active'))
        .map(btn => parseInt(btn.dataset.day));
    document.getElementById('weekdays').value = JSON.stringify(weekdays);
}

// 處理排程類型變更
function handleScheduleTypeChange() {
    const scheduleType = document.getElementById('schedule_type').value;
    const executionTimeGroup = document.getElementById('execution_time_group');
    const weekdaysGroup = document.getElementById('weekdays_group');
    const daysOfMonthGroup = document.getElementById('days_of_month_group');
    
    executionTimeGroup.style.display = scheduleType === 'once' ? 'block' : 'none';
    weekdaysGroup.style.display = scheduleType === 'weekly' ? 'block' : 'none';
    daysOfMonthGroup.style.display = scheduleType === 'monthly' ? 'block' : 'none';
}

// 處理表單提交
function handleSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    
    // 處理日期時間格式
    if (data.execution_time) {
        data.execution_time = new Date(data.execution_time).toISOString();
    }
    
    // 處理星期和日期陣列
    data.weekdays = JSON.parse(data.weekdays || '[]');
    data.days_of_month = data.days_of_month ? data.days_of_month.split(',').map(Number) : [];
    
    const url = data.schedule_id ? `/schedule/api/schedule/${data.schedule_id}` : '/schedule/api/schedule';
    const method = data.schedule_id ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.status === 'success') {
            window.location.href = '/schedule';
        } else {
            alert('儲存失敗：' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('儲存失敗，請稍後再試');
    });
    
    return false;
}

// 初始化表單
document.addEventListener('DOMContentLoaded', function() {
    initWeekdayButtons();
    handleScheduleTypeChange();
});
</script>
{% endmacro %} 