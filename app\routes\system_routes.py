# -*- coding: utf-8 -*-
from flask import Blueprint, jsonify, current_app
import psutil
from app.utils.api_logger import api_logger

# 藍圖命名需與其他檔案保持一致性 (app/__init__.py)
bp = Blueprint('system', __name__)

@bp.route('/api/v1/system/resources', methods=['GET'])
def get_system_resources():
    """獲取系統資源使用情況"""
    try:
        api_logger.info("獲取系統資源使用情況")
        
        # 方式1：嘗試從服務工廠獲取系統監控服務
        try:
            from app.services import get_service
            system_monitor = get_service('system_monitor')
            resources = system_monitor.get_system_resources()
            api_logger.debug("使用系統監控服務獲取資源信息")
        except Exception as service_error:
            api_logger.warning(f"無法使用系統監控服務: {str(service_error)}，使用降級方案")
            
            # 方式2：嘗試使用 current_app.system_monitor
            if hasattr(current_app, 'system_monitor') and current_app.system_monitor:
                try:
                    resources = current_app.system_monitor.get_system_resources()
                    api_logger.debug("使用app.system_monitor獲取資源信息")
                except Exception as app_error:
                    api_logger.warning(f"使用app.system_monitor失敗: {str(app_error)}，使用psutil降級方案")
                    resources = get_resources_fallback()
            else:
                # 方式3：降級處理，直接使用psutil
                api_logger.warning("未找到system_monitor服務，使用psutil降級方案")
                resources = get_resources_fallback()
        
        return jsonify({
            'status': 'success',
            'data': resources
        })
    except Exception as e:
        api_logger.error(f'獲取系統資源使用狀況失敗: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': f'Failed to get system resources: {str(e)}'
        }), 500

def get_resources_fallback():
    """使用psutil直接獲取系統資源的降級方案"""
    api_logger.debug("使用psutil降級方案獲取系統資源")
    try:
        # 獲取CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # 獲取記憶體使用率
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 嘗試獲取排程數量
        try:
            from app.models.schedule import Schedule
            from flask_sqlalchemy import SQLAlchemy
            
            db = SQLAlchemy()
            schedule_count = Schedule.query.filter_by(is_active=True).count()
        except Exception as db_error:
            api_logger.warning(f"獲取排程數量失敗: {str(db_error)}")
            schedule_count = 0
            
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory_percent,
            'schedule_count': schedule_count,
            'max_schedules': current_app.config.get('MAX_SCHEDULES', 10)
        }
    except Exception as e:
        api_logger.error(f"降級獲取系統資源失敗: {str(e)}")
        # 返回默認值
        return {
            'cpu_percent': 0,
            'memory_percent': 0,
            'schedule_count': 0,
            'max_schedules': 10
        }

@bp.route('/api/process/<int:pid>/resources', methods=['GET'])
def get_process_resources(pid):
    """獲取特定進程的資源使用狀況"""
    try:
        api_logger.info(f"獲取進程資源使用狀況: PID={pid}")
        
        # 嘗試使用系統監控服務
        try:
            from app.services import get_service
            system_monitor = get_service('system_monitor')
            resources = system_monitor.get_process_resources(pid)
        except Exception:
            # 降級處理，直接使用psutil
            api_logger.warning(f"使用服務獲取進程資源失敗，降級使用psutil")
            try:
                process = psutil.Process(pid)
                resources = {
                    "pid": pid,
                    "cpu_percent": process.cpu_percent(interval=0.1),
                    "memory_percent": process.memory_percent(),
                    "status": process.status(),
                    "create_time": process.create_time()
                }
            except psutil.NoSuchProcess:
                api_logger.warning(f"進程不存在: PID={pid}")
                return jsonify({
                    "error": "進程不存在",
                    "message": f"PID {pid} 不存在"
                }), 404
        
        if "error" in resources:
            return jsonify(resources), 404
            
        return jsonify(resources)
        
    except Exception as e:
        api_logger.error(f"獲取進程資源時發生錯誤: {str(e)}")
        return jsonify({
            "error": "獲取進程資源失敗",
            "message": str(e)
        }), 500 