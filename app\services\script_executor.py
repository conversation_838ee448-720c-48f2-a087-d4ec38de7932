# -*- coding: utf-8 -*-
import os
import logging
import shutil
import importlib.util
import sys
import traceback
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app

class ScriptExecutor:
    """腳本執行器，負責管理和執行腳本"""
    
    def __init__(self, app=None):
        """初始化腳本執行器"""
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)
        self._initialized = False
        
        # 確保基本目錄結構
        self.scripts_dir = None
        self.outputs_dir = None
        self.timeout = 60  # 預設超時時間為60秒
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """使用Flask應用初始化腳本執行器"""
        self.app = app
        
        # 確保日誌設置
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(asctime)s] %(levelname)s in %(module)s: %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        # 從配置中獲取路徑
        self.scripts_dir = app.config.get('UPLOAD_FOLDER', os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'uploads'))
        self.outputs_dir = app.config.get('OUTPUT_FOLDER', os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'outputs'))
        self.timeout = app.config.get('SCRIPT_TIMEOUT', 60)
        
        # 確保目錄存在
        os.makedirs(self.scripts_dir, exist_ok=True)
        os.makedirs(self.outputs_dir, exist_ok=True)
        
        self.logger.debug(f"腳本執行器初始化完成：\n腳本目錄：{self.scripts_dir}\n輸出目錄：{self.outputs_dir}\n超時時間：{self.timeout}秒")
        self._initialized = True
        
        # 將自身添加到app上下文
        app.script_executor = self
    
    def get_all_scripts(self):
        """獲取所有已上傳的腳本"""
        try:
            self.logger.info("獲取腳本列表")
            if not os.path.exists(self.scripts_dir):
                os.makedirs(self.scripts_dir, exist_ok=True)
                self.logger.warning(f"腳本目錄不存在，已創建: {self.scripts_dir}")
                return []
            
            scripts = []
            for filename in os.listdir(self.scripts_dir):
                if filename.endswith('.py'):
                    script_path = os.path.join(self.scripts_dir, filename)
                    script_info = self._get_script_info(filename)
                    scripts.append(script_info)
            
            self.logger.debug(f"找到 {len(scripts)} 個腳本")
            return scripts
        except Exception as e:
            self.logger.error(f"獲取腳本列表失敗: {str(e)}")
            self.logger.error(traceback.format_exc())
            return []
    
    def _get_script_info(self, filename):
        """獲取腳本的詳細信息"""
        try:
            script_path = os.path.join(self.scripts_dir, filename)
            
            # 獲取基本文件信息
            stat_info = os.stat(script_path)
            size = stat_info.st_size
            created = datetime.fromtimestamp(stat_info.st_ctime).strftime('%Y-%m-%d %H:%M:%S')
            modified = datetime.fromtimestamp(stat_info.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            
            # 從檔案內容提取說明信息
            description = ""
            metadata = {}
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    first_lines = [next(f, '').strip() for _ in range(20)]  # 讀取前20行
                for line in first_lines:
                    if line.startswith('# Description:'):
                        description = line.replace('# Description:', '').strip()
                    elif line.startswith('# Tags:'):
                        tags_str = line.replace('# Tags:', '').strip()
                        metadata['tags'] = [tag.strip() for tag in tags_str.split(',')]
                    elif line.startswith('# Author:'):
                        metadata['author'] = line.replace('# Author:', '').strip()
                    elif line.startswith('# Version:'):
                        metadata['version'] = line.replace('# Version:', '').strip()
            except Exception as e:
                self.logger.warning(f"讀取腳本說明信息失敗: {filename}, 錯誤: {str(e)}")
            
            return {
                'id': filename.replace('.py', ''),
                'name': filename,
                'description': description,
                'path': script_path,
                'size': size,
                'created': created,
                'modified': modified,
                'metadata': metadata
            }
        except Exception as e:
            self.logger.error(f"獲取腳本信息失敗: {filename}, 錯誤: {str(e)}")
            return {
                'id': filename.replace('.py', ''),
                'name': filename,
                'description': '無法讀取腳本信息',
                'error': str(e)
            }
    
    def create_script(self, file, name=None, description=None, tags=None):
        """創建一個新腳本"""
        try:
            self.logger.info(f"創建新腳本: {name or file.filename}")
            
            # 確保腳本目錄存在
            if not os.path.exists(self.scripts_dir):
                os.makedirs(self.scripts_dir, exist_ok=True)
                self.logger.info(f"腳本目錄不存在，已創建: {self.scripts_dir}")
            
            # 安全處理文件名
            if name:
                # 確保有 .py 擴展名
                if not name.endswith('.py'):
                    name = name + '.py'
                filename = secure_filename(name)
            else:
                filename = secure_filename(file.filename)
            
            # 檢查文件擴展名
            if not filename.endswith('.py'):
                raise ValueError("只允許上傳Python腳本文件(.py)")
            
            # 保存文件
            script_path = os.path.join(self.scripts_dir, filename)
            file.save(script_path)
            self.logger.info(f"腳本文件已保存: {script_path}")
            
            # 驗證腳本
            validation_result = self._validate_script(script_path)
            if not validation_result['valid']:
                # 驗證失敗，刪除文件
                os.remove(script_path)
                self.logger.warning(f"腳本驗證失敗，已刪除: {script_path}, 原因: {validation_result['message']}")
                raise ValueError(f"腳本驗證失敗: {validation_result['message']}")
            
            # 添加描述和標籤信息
            if description or tags:
                self._add_metadata_to_script(script_path, description, tags)
            
            # 返回腳本信息
            script_info = self._get_script_info(filename)
            self.logger.info(f"腳本創建成功: {filename}")
            return script_info
        except Exception as e:
            self.logger.error(f"創建腳本失敗: {str(e)}")
            self.logger.error(traceback.format_exc())
            raise
    
    def _validate_script(self, script_path):
        """驗證腳本的基本語法和安全性"""
        try:
            self.logger.debug(f"驗證腳本: {script_path}")

            errors = []
            warnings = []
            missing_packages = []

            # 檢查文件是否存在
            if not os.path.exists(script_path):
                errors.append('腳本文件不存在')
                return {
                    'valid': False,
                    'errors': errors,
                    'warnings': warnings,
                    'missing_packages': missing_packages
                }

            # 讀取腳本內容
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    script_content = f.read()
            except UnicodeDecodeError:
                errors.append('文件編碼錯誤，請使用UTF-8編碼')
                return {
                    'valid': False,
                    'errors': errors,
                    'warnings': warnings,
                    'missing_packages': missing_packages
                }

            # 檢查基本語法
            try:
                compile(script_content, script_path, 'exec')
            except SyntaxError as e:
                errors.append(f'語法錯誤 (第{e.lineno}行): {e.msg}')

            # 檢查導入的模塊
            import ast
            try:
                tree = ast.parse(script_content)
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            self._check_module_availability(alias.name, missing_packages)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            self._check_module_availability(node.module, missing_packages)
            except:
                # 如果AST解析失敗，跳過模塊檢查
                pass

            # 基本安全檢查
            dangerous_patterns = [
                ('os.system', '使用了系統命令執行'),
                ('subprocess.call', '使用了子進程調用'),
                ('eval(', '使用了eval函數'),
                ('exec(', '使用了exec函數'),
                ('__import__', '使用了動態導入'),
                ('open(', '包含文件操作')
            ]

            for pattern, description in dangerous_patterns:
                if pattern in script_content:
                    warnings.append(f'{description}: {pattern}')

            # 判斷是否有效
            is_valid = len(errors) == 0

            return {
                'valid': is_valid,
                'errors': errors,
                'warnings': warnings,
                'missing_packages': list(set(missing_packages))  # 去重
            }

        except Exception as e:
            self.logger.error(f"驗證腳本時發生錯誤: {str(e)}")
            return {
                'valid': False,
                'errors': [f'驗證過程出錯: {str(e)}'],
                'warnings': [],
                'missing_packages': []
            }

    def _check_module_availability(self, module_name, missing_packages):
        """檢查模塊是否可用"""
        try:
            __import__(module_name)
        except ImportError:
            # 跳過標準庫模塊
            standard_modules = {
                'os', 'sys', 'time', 'datetime', 'json', 'csv', 'math', 'random',
                'collections', 'itertools', 'functools', 're', 'urllib', 'http',
                'pathlib', 'logging', 'argparse', 'configparser', 'sqlite3'
            }
            if module_name not in standard_modules:
                missing_packages.append(module_name)
    
    def _add_metadata_to_script(self, script_path, description=None, tags=None):
        """向腳本添加元數據信息"""
        try:
            # 讀取原始腳本內容
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 準備元數據行
            metadata_lines = []
            if description:
                metadata_lines.append(f"# Description: {description}")
            if tags:
                tags_str = ', '.join(tags)
                metadata_lines.append(f"# Tags: {tags_str}")
            
            # 添加創建時間
            created_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            metadata_lines.append(f"# Created: {created_time}")
            
            # 組合新內容
            metadata_text = '\n'.join(metadata_lines)
            
            # 檢查是否已有 Python 解釋器聲明
            if content.startswith('#!/') or content.startswith('#!'):
                # 找到第一行的結束位置
                first_line_end = content.find('\n')
                if first_line_end != -1:
                    new_content = content[:first_line_end+1] + '\n' + metadata_text + '\n' + content[first_line_end+1:]
                else:
                    new_content = content + '\n' + metadata_text
            else:
                new_content = metadata_text + '\n\n' + content
            
            # 寫入新內容
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            self.logger.debug(f"已向腳本添加元數據: {script_path}")
        except Exception as e:
            self.logger.error(f"添加腳本元數據失敗: {script_path}, 錯誤: {str(e)}")
    
    def get_script_by_id(self, script_id):
        """根據ID獲取特定腳本"""
        try:
            self.logger.info(f"獲取腳本 ID: {script_id}")
            
            # 嘗試直接使用ID作為文件名
            script_filename = f"{script_id}.py"
            script_path = os.path.join(self.scripts_dir, script_filename)
            
            if os.path.exists(script_path):
                script_info = self._get_script_info(script_filename)
                return script_info
            
            # 如果直接匹配失敗，嘗試查找所有腳本
            for filename in os.listdir(self.scripts_dir):
                if filename.endswith('.py'):
                    script_info = self._get_script_info(filename)
                    if script_info['id'] == script_id:
                        return script_info
            
            self.logger.warning(f"找不到腳本 ID: {script_id}")
            return None
        except Exception as e:
            self.logger.error(f"獲取腳本詳情失敗: {script_id}, 錯誤: {str(e)}")
            return None
    
    def update_script(self, script_id, name=None, description=None, tags=None):
        """更新腳本信息"""
        try:
            self.logger.info(f"更新腳本 ID: {script_id}")
            
            # 獲取腳本信息
            script_info = self.get_script_by_id(script_id)
            if not script_info:
                raise ValueError(f"腳本不存在: {script_id}")
            
            script_path = script_info['path']
            
            # 如果要重命名
            if name and name != script_info['name']:
                new_name = name if name.endswith('.py') else name + '.py'
                new_path = os.path.join(self.scripts_dir, secure_filename(new_name))
                
                # 重命名文件
                shutil.move(script_path, new_path)
                script_path = new_path
                self.logger.info(f"腳本已重命名: {script_info['name']} -> {new_name}")
            
            # 更新描述和標籤
            if description or tags:
                self._add_metadata_to_script(script_path, description, tags)
            
            # 獲取更新後的腳本信息
            updated_script = self._get_script_info(os.path.basename(script_path))
            self.logger.info(f"腳本更新成功: {script_id}")
            
            return updated_script
        except Exception as e:
            self.logger.error(f"更新腳本失敗: {script_id}, 錯誤: {str(e)}")
            raise
    
    def delete_script(self, script_id):
        """刪除腳本"""
        try:
            self.logger.info(f"刪除腳本 ID: {script_id}")
            
            # 獲取腳本信息
            script_info = self.get_script_by_id(script_id)
            if not script_info:
                raise ValueError(f"腳本不存在: {script_id}")
            
            script_path = script_info['path']
            
            # 刪除文件
            os.remove(script_path)
            self.logger.info(f"腳本已刪除: {script_path}")
            
            return True
        except Exception as e:
            self.logger.error(f"刪除腳本失敗: {script_id}, 錯誤: {str(e)}")
            raise
    
    def execute_script(self, script_id, params=None):
        """執行腳本"""
        try:
            self.logger.info(f"執行腳本 ID: {script_id}")
            
            # 獲取腳本信息
            script_info = self.get_script_by_id(script_id)
            if not script_info:
                raise ValueError(f"腳本不存在: {script_id}")
            
            script_path = script_info['path']
            
            # 準備輸出目錄
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_dir = os.path.join(self.outputs_dir, f"{script_info['id']}_{timestamp}")
            os.makedirs(output_dir, exist_ok=True)
            
            # 準備參數
            execution_params = params or {}
            
            # 執行腳本
            result = self._run_script(script_path, output_dir, execution_params)
            
            execution_result = {
                'script_id': script_id,
                'script_name': script_info['name'],
                'execution_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 'success' if result['success'] else 'error',
                'output': result['output'],
                'error': result['error'],
                'execution_time_ms': result['execution_time_ms'],
                'output_dir': output_dir
            }
            
            self.logger.info(f"腳本執行完成: {script_id}, 狀態: {execution_result['status']}")
            return execution_result
        except Exception as e:
            self.logger.error(f"執行腳本失敗: {script_id}, 錯誤: {str(e)}")
            self.logger.error(traceback.format_exc())
            return {
                'script_id': script_id,
                'status': 'error',
                'error': str(e),
                'execution_time_ms': 0
            }
    
    def _run_script(self, script_path, output_dir, params):
        """內部方法：實際執行腳本"""
        import importlib.util
        import sys
        import io
        import time
        import contextlib
        
        start_time = time.time()
        stdout_buffer = io.StringIO()
        stderr_buffer = io.StringIO()
        success = False
        error_message = ""
        
        try:
            # 動態導入腳本
            spec = importlib.util.spec_from_file_location("dynamic_script", script_path)
            module = importlib.util.module_from_spec(spec)
            
            # 提供參數和輸出目錄
            module.params = params
            module.output_dir = output_dir
            
            # 重定向標準輸出和錯誤輸出
            with contextlib.redirect_stdout(stdout_buffer), contextlib.redirect_stderr(stderr_buffer):
                # 執行腳本
                spec.loader.exec_module(module)
                
                # 檢查是否有main函數
                if hasattr(module, 'main') and callable(module.main):
                    module.main()
            
            success = True
        except Exception as e:
            error_message = f"腳本執行錯誤: {str(e)}\n{traceback.format_exc()}"
            self.logger.error(error_message)
        
        execution_time = time.time() - start_time
        
        # 獲取輸出內容
        stdout_content = stdout_buffer.getvalue()
        stderr_content = stderr_buffer.getvalue()
        
        # 寫入輸出文件
        with open(os.path.join(output_dir, 'stdout.log'), 'w', encoding='utf-8') as f:
            f.write(stdout_content)
        
        with open(os.path.join(output_dir, 'stderr.log'), 'w', encoding='utf-8') as f:
            f.write(stderr_content)
        
        if error_message:
            with open(os.path.join(output_dir, 'error.log'), 'w', encoding='utf-8') as f:
                f.write(error_message)
        
        return {
            'success': success,
            'output': stdout_content,
            'error': stderr_content or error_message,
            'execution_time_ms': int(execution_time * 1000)
        } 