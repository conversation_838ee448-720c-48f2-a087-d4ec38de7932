# -*- coding: utf-8 -*-
import os
import sys
import subprocess
import json
from pathlib import Path

def print_header(message):
    """打印帶有格式的標題"""
    print("\n" + "=" * 60)
    print(f" {message}")
    print("=" * 60)

def check_requirements():
    """檢查必要的依賴套件"""
    print_header("檢查依賴套件")
    required_packages = ['flask', 'sqlalchemy', 'psutil', 'croniter', 'pytest']
    
    try:
        import pkg_resources
        
        missing_packages = []
        for package in required_packages:
            try:
                pkg_resources.get_distribution(package)
                print(f"✓ {package} 已安裝")
            except pkg_resources.DistributionNotFound:
                missing_packages.append(package)
                print(f"✗ {package} 未安裝")
        
        if missing_packages:
            print("\n需要安裝以下套件:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        
        return True
    except Exception as e:
        print(f"檢查依賴套件時出錯: {str(e)}")
        return False

def create_env_file():
    """創建環境變數文件"""
    print_header("創建環境變數文件")
    
    if os.path.exists('.env'):
        print("環境變數文件已存在")
        return True
    
    env_content = """# 應用環境設置
FLASK_APP=run.py
FLASK_ENV=development
# FLASK_ENV=production

# 數據庫配置
# DATABASE_URL=sqlite:///app.db

# 安全配置
SECRET_KEY=your-secret-key-here

# 應用配置
MAX_SCHEDULES=10
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("已創建 .env 文件")
        return True
    except Exception as e:
        print(f"創建環境變數文件時出錯: {str(e)}")
        return False

def init_config():
    """初始化配置"""
    print_header("初始化配置")
    
    # 檢查配置文件是否已存在
    if (os.path.exists('config/development.py') and 
        os.path.exists('config/production.py') and
        os.path.exists('config/testing.py')):
        print("配置文件已存在")
        return True
    
    try:
        # 執行配置遷移腳本
        print("執行配置遷移...")
        from migrate_config import main as migrate_config
        migrate_config()
        print("配置遷移完成")
        return True
    except Exception as e:
        print(f"初始化配置時出錯: {str(e)}")
        try:
            # 直接導入失敗時，嘗試使用subprocess運行
            print("嘗試使用subprocess運行配置遷移腳本...")
            result = subprocess.run([sys.executable, 'scripts/migrate_config.py'], 
                                   check=True, capture_output=True, text=True)
            print(result.stdout)
            return True
        except Exception as sub_e:
            print(f"使用subprocess運行配置遷移腳本時出錯: {str(sub_e)}")
            return False

def migrate_data():
    """遷移數據"""
    print_header("遷移數據")
    
    # 檢查目標文件夾
    if (os.path.exists('app/data/uploads') and 
        os.path.exists('app/data/outputs') and 
        (len(os.listdir('app/data/uploads')) > 0 or 
         len(os.listdir('app/data/outputs')) > 0)):
        print("數據目錄已存在且不為空")
        return True
    
    try:
        # 執行數據遷移腳本
        print("執行數據遷移...")
        from migrate_data import main as migrate_data
        migrate_data()
        print("數據遷移完成")
        return True
    except Exception as e:
        print(f"遷移數據時出錯: {str(e)}")
        try:
            # 直接導入失敗時，嘗試使用subprocess運行
            print("嘗試使用subprocess運行數據遷移腳本...")
            result = subprocess.run([sys.executable, 'scripts/migrate_data.py'], 
                                   check=True, capture_output=True, text=True)
            print(result.stdout)
            return True
        except Exception as sub_e:
            print(f"使用subprocess運行數據遷移腳本時出錯: {str(sub_e)}")
            return False

def update_api_paths():
    """更新API路徑"""
    print_header("更新API路徑")
    
    try:
        # 執行API路徑更新腳本
        print("執行API路徑更新...")
        from update_api_paths import main as update_paths
        update_paths()
        print("API路徑更新完成")
        return True
    except Exception as e:
        print(f"更新API路徑時出錯: {str(e)}")
        try:
            # 直接導入失敗時，嘗試使用subprocess運行
            print("嘗試使用subprocess運行API路徑更新腳本...")
            result = subprocess.run([sys.executable, 'scripts/update_api_paths.py'], 
                                   check=True, capture_output=True, text=True)
            print(result.stdout)
            return True
        except Exception as sub_e:
            print(f"使用subprocess運行API路徑更新腳本時出錯: {str(sub_e)}")
            return False

def create_init_file(directory):
    """在目錄中創建__init__.py文件"""
    init_file = os.path.join(directory, '__init__.py')
    if not os.path.exists(init_file):
        with open(init_file, 'w', encoding='utf-8') as f:
            f.write('# -*- coding: utf-8 -*-\n')
        return True
    return False

def init_package_structure():
    """初始化包結構"""
    print_header("初始化包結構")
    
    # 需要創建__init__.py的目錄列表
    dirs = [
        'app/api',
        'app/api/v1',
        'app/api/middleware',
        'app/data',
        'app/services/executors',
        'app/services/managers',
        'tests/unit/models',
        'tests/unit/services',
        'tests/integration/api',
        'tests/integration/services',
        'tests/e2e'
    ]
    
    for d in dirs:
        os.makedirs(d, exist_ok=True)
        if create_init_file(d):
            print(f"在 {d} 創建了 __init__.py")
    
    print("包結構初始化完成")
    return True

def main():
    """主函數"""
    print_header("開始初始化新架構")
    
    # 確保當前目錄是專案根目錄
    if not os.path.exists('app'):
        print("錯誤: 請在專案根目錄下運行此腳本")
        sys.exit(1)
    
    # 檢查依賴套件
    if not check_requirements():
        print("警告: 有缺少的依賴套件，但將繼續執行")
    
    # 創建環境變數文件
    create_env_file()
    
    # 初始化包結構
    init_package_structure()
    
    # 初始化配置
    init_config()
    
    # 遷移數據
    migrate_data()
    
    # 更新API路徑
    update_api_paths()
    
    print_header("初始化新架構完成")
    print("""
請執行以下步驟完成設置：
1. 檢查並編輯 .env 文件設置環境變數
2. 使用 'flask run' 啟動應用測試新架構
""")

if __name__ == "__main__":
    main() 